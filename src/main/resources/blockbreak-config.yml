# BlockBreak Threading Configuration
# This configuration file controls the delayed block break processing system

# Threading System Settings
threading:
  # Enable or disable the delayed processing system
  # If disabled, the original immediate processing will be used
  enabled: true
  
  # Delayed Processing Configuration
  processing:
    # Delay in ticks before processing block break events
    # 1 tick = 50ms, so 1 = 50ms delay, 2 = 100ms delay, etc.
    # Higher values spread load more but increase response time
    delay-ticks: 1

    # Maximum number of delayed tasks to queue
    # If queue is full, tasks will be processed immediately
    max-queue-size: 1000

    # Enable batching of multiple block breaks from the same player
    # This can improve performance when players break many blocks quickly
    enable-batching: false

    # Batch size for grouping block breaks (only if batching enabled)
    batch-size: 5

# Performance Settings
performance:
  # Enable performance monitoring and statistics logging
  monitoring:
    enabled: true
    
    # How often (in seconds) to log statistics
    # Set to 0 to disable periodic logging
    log-interval: 300
    
    # Log detailed performance metrics
    detailed-logging: false
  
  # Task Processing Settings
  processing:
    # Maximum time (in milliseconds) a task should take
    # Tasks taking longer will be logged as warnings
    max-task-time: 100
    
    # Enable task timeout protection
    # Tasks that take too long will be cancelled
    timeout-protection: true
    
    # Task timeout in milliseconds
    task-timeout: 5000

# Feature Toggles
features:
  # Enable async reward calculations
  async-rewards: true
  
  # Enable async pet processing
  async-pets: true
  
  # Enable async enchantment processing
  async-enchantments: true
  
  # Enable async special effects processing
  async-effects: true
  
  # Fallback to synchronous processing if async fails
  sync-fallback: true

# Debug Settings
debug:
  # Enable debug logging
  enabled: false
  
  # Log all task submissions
  log-task-submissions: false
  
  # Log task completion times
  log-completion-times: false
  
  # Log thread pool state changes
  log-pool-state: false
  
  # Log rejected tasks
  log-rejected-tasks: true

# Advanced Settings
advanced:
  # Use virtual threads if available (Java 19+)
  # This is experimental and may not work on all systems
  use-virtual-threads: false
  
  # Enable work stealing for better load distribution
  work-stealing: false
  
  # Batch size for processing multiple events together
  # Set to 1 to disable batching
  batch-size: 1
  
  # Enable thread affinity for better CPU cache usage
  # This is platform-specific and may not work everywhere
  thread-affinity: false

# Compatibility Settings
compatibility:
  # Disable threading for specific worlds
  # Events in these worlds will always use synchronous processing
  disabled-worlds:
    - "example_world"
    # - "another_world"
  
  # Disable threading for specific block types
  # These blocks will always use synchronous processing
  disabled-blocks:
    - "BEDROCK"
    # - "BARRIER"
  
  # Minimum server TPS before disabling threading
  # If server TPS drops below this, threading will be temporarily disabled
  min-tps-threshold: 15.0
  
  # Re-enable threading when TPS goes above this threshold
  tps-recovery-threshold: 18.0
