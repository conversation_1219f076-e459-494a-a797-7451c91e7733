package it.masterzen.minebuddy;

import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.MongoDB.PlayerData;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.event.inventory.InventoryOpenEvent;
import org.bukkit.event.player.*;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.Date;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Event listener for AFK detection system.
 * Monitors player activities including movement, interactions, and chat.
 */
public class AFKDetectionListener implements Listener {
    
    private final AlphaBlockBreak plugin;
    private final AFKDetectionConfig config;
    private final Map<UUID, PlayerActivityTracker> activityTrackers;
    private final String prefix = "§e§lMINE BUDDY §8»§7 ";
    
    public AFKDetectionListener(AlphaBlockBreak plugin, AFKDetectionConfig config) {
        this.plugin = plugin;
        this.config = config;
        this.activityTrackers = new ConcurrentHashMap<>();
        
        // Start activity score decay task
        startActivityDecayTask();
    }
    
    /**
     * Handles player movement and look direction changes
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerMove(PlayerMoveEvent event) {
        if (!config.isEnabled()) return;
        
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();
        
        // Get or create activity tracker
        PlayerActivityTracker tracker = getOrCreateTracker(player);
        
        Location from = event.getFrom();
        Location to = event.getTo();
        
        if (to == null) return;
        
        // Update location tracking
        tracker.updateLocation(to);
        
        // Update look direction tracking
        tracker.updateLookDirection(to.getYaw(), to.getPitch());
        
        // Update persistent data if significant movement occurred
        if (from.distance(to) > config.getMinMovementDistance()) {
            updatePlayerDataLocation(player, to);
        }
    }
    
    /**
     * Handles player interactions with blocks
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerInteract(PlayerInteractEvent event) {
        if (!config.isEnabled()) return;
        
        Player player = event.getPlayer();
        PlayerActivityTracker tracker = getOrCreateTracker(player);
        
        // Record interaction based on action type
        switch (event.getAction()) {
            case LEFT_CLICK_BLOCK:
            case RIGHT_CLICK_BLOCK:
                tracker.recordInteraction(PlayerActivityTracker.InteractionType.BLOCK_BREAK);
                break;
            case LEFT_CLICK_AIR:
            case RIGHT_CLICK_AIR:
                if (event.getItem() != null) {
                    tracker.recordInteraction(PlayerActivityTracker.InteractionType.ITEM_USE);
                }
                break;
        }
        
        updatePlayerDataInteraction(player);
    }
    
    /**
     * Handles block breaking events
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onBlockBreak(BlockBreakEvent event) {
        if (!config.isEnabled()) return;
        
        Player player = event.getPlayer();
        PlayerActivityTracker tracker = getOrCreateTracker(player);
        
        tracker.recordInteraction(PlayerActivityTracker.InteractionType.BLOCK_BREAK);
        updatePlayerDataInteraction(player);
    }
    
    /**
     * Handles block placing events
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onBlockPlace(BlockPlaceEvent event) {
        if (!config.isEnabled()) return;
        
        Player player = event.getPlayer();
        PlayerActivityTracker tracker = getOrCreateTracker(player);
        
        tracker.recordInteraction(PlayerActivityTracker.InteractionType.BLOCK_PLACE);
        updatePlayerDataInteraction(player);
    }
    
    /**
     * Handles inventory opening events
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onInventoryOpen(InventoryOpenEvent event) {
        if (!config.isEnabled()) return;
        
        if (event.getPlayer() instanceof Player) {
            Player player = (Player) event.getPlayer();
            PlayerActivityTracker tracker = getOrCreateTracker(player);
            
            tracker.recordInteraction(PlayerActivityTracker.InteractionType.INVENTORY_OPEN);
            updatePlayerDataInteraction(player);
        }
    }
    
    /**
     * Handles chat events
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerChat(AsyncPlayerChatEvent event) {
        if (!config.isEnabled()) return;
        
        Player player = event.getPlayer();
        PlayerActivityTracker tracker = getOrCreateTracker(player);
        
        tracker.recordChat();
        
        // Update persistent data asynchronously
        new BukkitRunnable() {
            @Override
            public void run() {
                updatePlayerDataInteraction(player);
            }
        }.runTask(plugin);
    }
    
    /**
     * Handles command events
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerCommand(PlayerCommandPreprocessEvent event) {
        if (!config.isEnabled()) return;
        
        Player player = event.getPlayer();
        PlayerActivityTracker tracker = getOrCreateTracker(player);
        
        tracker.recordInteraction(PlayerActivityTracker.InteractionType.COMMAND_USE);
        updatePlayerDataInteraction(player);
    }
    
    /**
     * Handles player join events
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerJoin(PlayerJoinEvent event) {
        if (!config.isEnabled()) return;
        
        Player player = event.getPlayer();
        
        // Create new activity tracker for the player
        PlayerActivityTracker tracker = new PlayerActivityTracker(player.getUniqueId(), player.getName());
        activityTrackers.put(player.getUniqueId(), tracker);
        
        // Initialize location data
        tracker.updateLocation(player.getLocation());
        tracker.updateLookDirection(player.getLocation().getYaw(), player.getLocation().getPitch());
        
        // Load persistent data
        loadPlayerDataToTracker(player, tracker);
    }
    
    /**
     * Handles player quit events
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();
        
        // Save activity data to persistent storage
        PlayerActivityTracker tracker = activityTrackers.get(playerId);
        if (tracker != null) {
            saveTrackerToPlayerData(player, tracker);
        }
        
        // Remove from memory
        activityTrackers.remove(playerId);
    }
    
    /**
     * Gets or creates an activity tracker for a player
     */
    public PlayerActivityTracker getOrCreateTracker(Player player) {
        UUID playerId = player.getUniqueId();
        
        return activityTrackers.computeIfAbsent(playerId, k -> {
            PlayerActivityTracker tracker = new PlayerActivityTracker(playerId, player.getName());
            tracker.updateLocation(player.getLocation());
            tracker.updateLookDirection(player.getLocation().getYaw(), player.getLocation().getPitch());
            loadPlayerDataToTracker(player, tracker);
            return tracker;
        });
    }
    
    /**
     * Gets an existing activity tracker for a player
     */
    public PlayerActivityTracker getTracker(UUID playerId) {
        return activityTrackers.get(playerId);
    }
    
    /**
     * Removes a player's activity tracker
     */
    public void removeTracker(UUID playerId) {
        activityTrackers.remove(playerId);
    }
    
    /**
     * Gets all active trackers
     */
    public Map<UUID, PlayerActivityTracker> getAllTrackers() {
        return activityTrackers;
    }
    
    /**
     * Updates persistent player data with location information
     */
    private void updatePlayerDataLocation(Player player, Location location) {
        try {
            PlayerData playerData = plugin.getMongoReader().getPlayerData(player.getUniqueId());
            if (playerData != null) {
                playerData.setLastKnownLocationWorld(location.getWorld().getName());
                playerData.setLastKnownLocationX(location.getX());
                playerData.setLastKnownLocationY(location.getY());
                playerData.setLastKnownLocationZ(location.getZ());
                playerData.setLastKnownLocationYaw(location.getYaw());
                playerData.setLastKnownLocationPitch(location.getPitch());
                playerData.setLastMovementTime(new Date());
                
                // Save asynchronously to avoid blocking
                new BukkitRunnable() {
                    @Override
                    public void run() {
                        plugin.getMongoReader().savePlayerData(playerData, false);
                    }
                }.runTaskAsynchronously(plugin);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("Error updating player location data for " + player.getName() + ": " + e.getMessage());
        }
    }
    
    /**
     * Updates persistent player data with interaction information
     */
    private void updatePlayerDataInteraction(Player player) {
        try {
            PlayerData playerData = plugin.getMongoReader().getPlayerData(player.getUniqueId());
            if (playerData != null) {
                playerData.setLastInteractionTime(new Date());
                
                // Save asynchronously to avoid blocking
                new BukkitRunnable() {
                    @Override
                    public void run() {
                        plugin.getMongoReader().savePlayerData(playerData, false);
                    }
                }.runTaskAsynchronously(plugin);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("Error updating player interaction data for " + player.getName() + ": " + e.getMessage());
        }
    }
    
    /**
     * Loads persistent player data into activity tracker
     */
    private void loadPlayerDataToTracker(Player player, PlayerActivityTracker tracker) {
        try {
            PlayerData playerData = plugin.getMongoReader().getPlayerData(player.getUniqueId());
            if (playerData != null) {
                // Load activity score if available
                if (playerData.getActivityScore() != null) {
                    // Note: We don't directly set the activity score as it's managed by the tracker
                    // But we could use it to initialize the tracker's state
                }
                
                // Load warning count if available
                if (playerData.getAfkWarningsCount() != null) {
                    for (int i = 0; i < playerData.getAfkWarningsCount(); i++) {
                        tracker.addWarning();
                    }
                }
            }
        } catch (Exception e) {
            plugin.getLogger().warning("Error loading player data to tracker for " + player.getName() + ": " + e.getMessage());
        }
    }
    
    /**
     * Saves activity tracker data to persistent player data
     */
    private void saveTrackerToPlayerData(Player player, PlayerActivityTracker tracker) {
        try {
            PlayerData playerData = plugin.getMongoReader().getPlayerData(player.getUniqueId());
            if (playerData != null) {
                playerData.setActivityScore(tracker.getCurrentActivityScore());
                playerData.setAfkWarningsCount(tracker.getWarningCount());
                
                // Save synchronously since this is called on player quit
                plugin.getMongoReader().savePlayerData(playerData, false);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("Error saving tracker data to player data for " + player.getName() + ": " + e.getMessage());
        }
    }
    
    /**
     * Starts the activity score decay task
     */
    private void startActivityDecayTask() {
        new BukkitRunnable() {
            @Override
            public void run() {
                if (!config.isEnabled()) return;
                
                for (PlayerActivityTracker tracker : activityTrackers.values()) {
                    tracker.decayActivityScore();
                }
            }
        }.runTaskTimer(plugin, 600L, 600L); // Run every 30 seconds (600 ticks)
    }
    
    /**
     * Checks if a player is currently being tracked
     */
    public boolean isPlayerTracked(UUID playerId) {
        return activityTrackers.containsKey(playerId);
    }
    
    /**
     * Resets activity data for a player
     */
    public void resetPlayerActivity(UUID playerId) {
        PlayerActivityTracker tracker = activityTrackers.get(playerId);
        if (tracker != null) {
            tracker.resetActivityData();
        }
    }
    
    /**
     * Gets the number of currently tracked players
     */
    public int getTrackedPlayerCount() {
        return activityTrackers.size();
    }
    
    /**
     * Cleanup method for plugin shutdown
     */
    public void shutdown() {
        // Save all tracker data before shutdown
        for (Map.Entry<UUID, PlayerActivityTracker> entry : activityTrackers.entrySet()) {
            try {
                Player player = plugin.getServer().getPlayer(entry.getKey());
                if (player != null) {
                    saveTrackerToPlayerData(player, entry.getValue());
                }
            } catch (Exception e) {
                plugin.getLogger().warning("Error saving tracker data during shutdown: " + e.getMessage());
            }
        }
        
        activityTrackers.clear();
    }
}
