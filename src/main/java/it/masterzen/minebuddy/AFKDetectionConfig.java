package it.masterzen.minebuddy;

import org.bukkit.configuration.file.FileConfiguration;

/**
 * Configuration class for AFK detection system parameters.
 * Provides configurable thresholds and settings for the anti-AFK system.
 */
public class AFKDetectionConfig {
    
    // Default configuration values
    private static final boolean DEFAULT_ENABLED = true;
    private static final long DEFAULT_MAX_STATIONARY_TIME = 60000; // 1 minute
    private static final long DEFAULT_MAX_INACTIVITY_TIME = 120000; // 2 minutes
    private static final double DEFAULT_MIN_MOVEMENT_DISTANCE = 2.0; // blocks
    private static final double DEFAULT_MIN_ACTIVITY_SCORE = 20.0;
    private static final int DEFAULT_MAX_STATIONARY_CHECKS = 12; // 12 * 5 seconds = 1 minute
    private static final int DEFAULT_MIN_AFK_INDICATORS = 3;
    private static final double DEFAULT_MIN_LOOK_CHANGE = 10.0; // degrees
    private static final boolean DEFAULT_PATTERN_DETECTION_ENABLED = true;
    private static final boolean DEFAULT_PROGRESSIVE_PENALTIES = true;
    private static final int DEFAULT_MAX_WARNINGS = 3;
    private static final long DEFAULT_WARNING_INTERVAL = 30000; // 30 seconds
    private static final double DEFAULT_PENALTY_REDUCTION_RATE = 0.25; // 25% reduction per warning
    private static final boolean DEFAULT_ADMIN_NOTIFICATIONS = true;
    private static final boolean DEFAULT_PLAYER_NOTIFICATIONS = true;
    
    // Configuration values
    private boolean enabled;
    private long maxStationaryTime;
    private long maxInactivityTime;
    private double minMovementDistance;
    private double minActivityScore;
    private int maxStationaryChecks;
    private int minAfkIndicators;
    private double minLookChange;
    private boolean patternDetectionEnabled;
    private boolean progressivePenalties;
    private int maxWarnings;
    private long warningInterval;
    private double penaltyReductionRate;
    private boolean adminNotifications;
    private boolean playerNotifications;
    
    // Location-specific settings
    private boolean dungeonDetectionEnabled;
    private boolean mineDetectionEnabled;
    private boolean farmerDetectionEnabled;
    
    // Advanced settings
    private double activityScoreDecayRate;
    private long activityScoreDecayInterval;
    private int movementHistorySize;
    private double circularMovementThreshold;
    private double roboticYawThreshold;
    
    public AFKDetectionConfig() {
        loadDefaults();
    }
    
    /**
     * Loads configuration from FileConfiguration
     */
    public void loadFromConfig(FileConfiguration config) {
        enabled = config.getBoolean("afk-detection.enabled", DEFAULT_ENABLED);
        maxStationaryTime = config.getLong("afk-detection.max-stationary-time", DEFAULT_MAX_STATIONARY_TIME);
        maxInactivityTime = config.getLong("afk-detection.max-inactivity-time", DEFAULT_MAX_INACTIVITY_TIME);
        minMovementDistance = config.getDouble("afk-detection.min-movement-distance", DEFAULT_MIN_MOVEMENT_DISTANCE);
        minActivityScore = config.getDouble("afk-detection.min-activity-score", DEFAULT_MIN_ACTIVITY_SCORE);
        maxStationaryChecks = config.getInt("afk-detection.max-stationary-checks", DEFAULT_MAX_STATIONARY_CHECKS);
        minAfkIndicators = config.getInt("afk-detection.min-afk-indicators", DEFAULT_MIN_AFK_INDICATORS);
        minLookChange = config.getDouble("afk-detection.min-look-change", DEFAULT_MIN_LOOK_CHANGE);
        patternDetectionEnabled = config.getBoolean("afk-detection.pattern-detection-enabled", DEFAULT_PATTERN_DETECTION_ENABLED);
        progressivePenalties = config.getBoolean("afk-detection.progressive-penalties", DEFAULT_PROGRESSIVE_PENALTIES);
        maxWarnings = config.getInt("afk-detection.max-warnings", DEFAULT_MAX_WARNINGS);
        warningInterval = config.getLong("afk-detection.warning-interval", DEFAULT_WARNING_INTERVAL);
        penaltyReductionRate = config.getDouble("afk-detection.penalty-reduction-rate", DEFAULT_PENALTY_REDUCTION_RATE);
        adminNotifications = config.getBoolean("afk-detection.admin-notifications", DEFAULT_ADMIN_NOTIFICATIONS);
        playerNotifications = config.getBoolean("afk-detection.player-notifications", DEFAULT_PLAYER_NOTIFICATIONS);
        
        // Location-specific settings
        dungeonDetectionEnabled = config.getBoolean("afk-detection.locations.dungeon", true);
        mineDetectionEnabled = config.getBoolean("afk-detection.locations.mine", false);
        farmerDetectionEnabled = config.getBoolean("afk-detection.locations.farmer", false);
        
        // Advanced settings
        activityScoreDecayRate = config.getDouble("afk-detection.advanced.activity-score-decay-rate", 2.0);
        activityScoreDecayInterval = config.getLong("afk-detection.advanced.activity-score-decay-interval", 30000);
        movementHistorySize = config.getInt("afk-detection.advanced.movement-history-size", 20);
        circularMovementThreshold = config.getDouble("afk-detection.advanced.circular-movement-threshold", 0.7);
        roboticYawThreshold = config.getDouble("afk-detection.advanced.robotic-yaw-threshold", 5.0);
        
        validateConfig();
    }
    
    /**
     * Saves configuration to FileConfiguration
     */
    public void saveToConfig(FileConfiguration config) {
        config.set("afk-detection.enabled", enabled);
        config.set("afk-detection.max-stationary-time", maxStationaryTime);
        config.set("afk-detection.max-inactivity-time", maxInactivityTime);
        config.set("afk-detection.min-movement-distance", minMovementDistance);
        config.set("afk-detection.min-activity-score", minActivityScore);
        config.set("afk-detection.max-stationary-checks", maxStationaryChecks);
        config.set("afk-detection.min-afk-indicators", minAfkIndicators);
        config.set("afk-detection.min-look-change", minLookChange);
        config.set("afk-detection.pattern-detection-enabled", patternDetectionEnabled);
        config.set("afk-detection.progressive-penalties", progressivePenalties);
        config.set("afk-detection.max-warnings", maxWarnings);
        config.set("afk-detection.warning-interval", warningInterval);
        config.set("afk-detection.penalty-reduction-rate", penaltyReductionRate);
        config.set("afk-detection.admin-notifications", adminNotifications);
        config.set("afk-detection.player-notifications", playerNotifications);
        
        // Location-specific settings
        config.set("afk-detection.locations.dungeon", dungeonDetectionEnabled);
        config.set("afk-detection.locations.mine", mineDetectionEnabled);
        config.set("afk-detection.locations.farmer", farmerDetectionEnabled);
        
        // Advanced settings
        config.set("afk-detection.advanced.activity-score-decay-rate", activityScoreDecayRate);
        config.set("afk-detection.advanced.activity-score-decay-interval", activityScoreDecayInterval);
        config.set("afk-detection.advanced.movement-history-size", movementHistorySize);
        config.set("afk-detection.advanced.circular-movement-threshold", circularMovementThreshold);
        config.set("afk-detection.advanced.robotic-yaw-threshold", roboticYawThreshold);
    }
    
    /**
     * Loads default configuration values
     */
    private void loadDefaults() {
        enabled = DEFAULT_ENABLED;
        maxStationaryTime = DEFAULT_MAX_STATIONARY_TIME;
        maxInactivityTime = DEFAULT_MAX_INACTIVITY_TIME;
        minMovementDistance = DEFAULT_MIN_MOVEMENT_DISTANCE;
        minActivityScore = DEFAULT_MIN_ACTIVITY_SCORE;
        maxStationaryChecks = DEFAULT_MAX_STATIONARY_CHECKS;
        minAfkIndicators = DEFAULT_MIN_AFK_INDICATORS;
        minLookChange = DEFAULT_MIN_LOOK_CHANGE;
        patternDetectionEnabled = DEFAULT_PATTERN_DETECTION_ENABLED;
        progressivePenalties = DEFAULT_PROGRESSIVE_PENALTIES;
        maxWarnings = DEFAULT_MAX_WARNINGS;
        warningInterval = DEFAULT_WARNING_INTERVAL;
        penaltyReductionRate = DEFAULT_PENALTY_REDUCTION_RATE;
        adminNotifications = DEFAULT_ADMIN_NOTIFICATIONS;
        playerNotifications = DEFAULT_PLAYER_NOTIFICATIONS;
        
        // Location-specific defaults
        dungeonDetectionEnabled = true;
        mineDetectionEnabled = false;
        farmerDetectionEnabled = false;
        
        // Advanced defaults
        activityScoreDecayRate = 2.0;
        activityScoreDecayInterval = 30000;
        movementHistorySize = 20;
        circularMovementThreshold = 0.7;
        roboticYawThreshold = 5.0;
    }
    
    /**
     * Validates configuration values and corrects invalid ones
     */
    private void validateConfig() {
        // Ensure positive time values
        if (maxStationaryTime <= 0) maxStationaryTime = DEFAULT_MAX_STATIONARY_TIME;
        if (maxInactivityTime <= 0) maxInactivityTime = DEFAULT_MAX_INACTIVITY_TIME;
        if (warningInterval <= 0) warningInterval = DEFAULT_WARNING_INTERVAL;
        if (activityScoreDecayInterval <= 0) activityScoreDecayInterval = 30000;
        
        // Ensure reasonable distance values
        if (minMovementDistance < 0) minMovementDistance = DEFAULT_MIN_MOVEMENT_DISTANCE;
        if (minLookChange < 0) minLookChange = DEFAULT_MIN_LOOK_CHANGE;
        
        // Ensure reasonable score values
        if (minActivityScore < 0) minActivityScore = 0;
        if (minActivityScore > 100) minActivityScore = 100;
        if (activityScoreDecayRate < 0) activityScoreDecayRate = 0;
        
        // Ensure reasonable count values
        if (maxStationaryChecks <= 0) maxStationaryChecks = DEFAULT_MAX_STATIONARY_CHECKS;
        if (minAfkIndicators <= 0) minAfkIndicators = 1;
        if (minAfkIndicators > 5) minAfkIndicators = 5;
        if (maxWarnings <= 0) maxWarnings = DEFAULT_MAX_WARNINGS;
        if (movementHistorySize <= 0) movementHistorySize = 20;
        
        // Ensure reasonable percentage values
        if (penaltyReductionRate < 0) penaltyReductionRate = 0;
        if (penaltyReductionRate > 1) penaltyReductionRate = 1;
        if (circularMovementThreshold < 0) circularMovementThreshold = 0;
        if (circularMovementThreshold > 1) circularMovementThreshold = 1;
        if (roboticYawThreshold < 0) roboticYawThreshold = 0;
    }
    
    /**
     * Checks if AFK detection is enabled for a specific location type
     */
    public boolean isEnabledForLocation(MineBuddy.LocationType locationType) {
        if (!enabled) return false;
        
        switch (locationType) {
            case DUNGEON:
                return dungeonDetectionEnabled;
            case MINE:
                return mineDetectionEnabled;
            case FARMER_WARP:
                return farmerDetectionEnabled;
            default:
                return false;
        }
    }
    
    // Getters
    public boolean isEnabled() { return enabled; }
    public long getMaxStationaryTime() { return maxStationaryTime; }
    public long getMaxInactivityTime() { return maxInactivityTime; }
    public double getMinMovementDistance() { return minMovementDistance; }
    public double getMinActivityScore() { return minActivityScore; }
    public int getMaxStationaryChecks() { return maxStationaryChecks; }
    public int getMinAfkIndicators() { return minAfkIndicators; }
    public double getMinLookChange() { return minLookChange; }
    public boolean isPatternDetectionEnabled() { return patternDetectionEnabled; }
    public boolean isProgressivePenalties() { return progressivePenalties; }
    public int getMaxWarnings() { return maxWarnings; }
    public long getWarningInterval() { return warningInterval; }
    public double getPenaltyReductionRate() { return penaltyReductionRate; }
    public boolean isAdminNotifications() { return adminNotifications; }
    public boolean isPlayerNotifications() { return playerNotifications; }
    public boolean isDungeonDetectionEnabled() { return dungeonDetectionEnabled; }
    public boolean isMineDetectionEnabled() { return mineDetectionEnabled; }
    public boolean isFarmerDetectionEnabled() { return farmerDetectionEnabled; }
    public double getActivityScoreDecayRate() { return activityScoreDecayRate; }
    public long getActivityScoreDecayInterval() { return activityScoreDecayInterval; }
    public int getMovementHistorySize() { return movementHistorySize; }
    public double getCircularMovementThreshold() { return circularMovementThreshold; }
    public double getRoboticYawThreshold() { return roboticYawThreshold; }
    
    // Setters for runtime configuration changes
    public void setEnabled(boolean enabled) { this.enabled = enabled; }
    public void setMaxStationaryTime(long maxStationaryTime) { 
        this.maxStationaryTime = Math.max(1000, maxStationaryTime); 
    }
    public void setMaxInactivityTime(long maxInactivityTime) { 
        this.maxInactivityTime = Math.max(1000, maxInactivityTime); 
    }
    public void setMinMovementDistance(double minMovementDistance) { 
        this.minMovementDistance = Math.max(0, minMovementDistance); 
    }
    public void setMinActivityScore(double minActivityScore) { 
        this.minActivityScore = Math.max(0, Math.min(100, minActivityScore)); 
    }
    public void setMaxStationaryChecks(int maxStationaryChecks) { 
        this.maxStationaryChecks = Math.max(1, maxStationaryChecks); 
    }
    public void setMinAfkIndicators(int minAfkIndicators) { 
        this.minAfkIndicators = Math.max(1, Math.min(5, minAfkIndicators)); 
    }
    public void setPatternDetectionEnabled(boolean patternDetectionEnabled) { 
        this.patternDetectionEnabled = patternDetectionEnabled; 
    }
    public void setProgressivePenalties(boolean progressivePenalties) { 
        this.progressivePenalties = progressivePenalties; 
    }
    public void setMaxWarnings(int maxWarnings) { 
        this.maxWarnings = Math.max(1, maxWarnings); 
    }
    public void setPenaltyReductionRate(double penaltyReductionRate) { 
        this.penaltyReductionRate = Math.max(0, Math.min(1, penaltyReductionRate)); 
    }
    public void setDungeonDetectionEnabled(boolean dungeonDetectionEnabled) { 
        this.dungeonDetectionEnabled = dungeonDetectionEnabled; 
    }
    public void setMineDetectionEnabled(boolean mineDetectionEnabled) { 
        this.mineDetectionEnabled = mineDetectionEnabled; 
    }
    public void setFarmerDetectionEnabled(boolean farmerDetectionEnabled) { 
        this.farmerDetectionEnabled = farmerDetectionEnabled; 
    }
    
    /**
     * Creates a summary string of current configuration
     */
    public String getConfigSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("AFK Detection Config Summary:\n");
        sb.append("  Enabled: ").append(enabled).append("\n");
        sb.append("  Max Stationary Time: ").append(maxStationaryTime / 1000).append("s\n");
        sb.append("  Max Inactivity Time: ").append(maxInactivityTime / 1000).append("s\n");
        sb.append("  Min Movement Distance: ").append(minMovementDistance).append(" blocks\n");
        sb.append("  Min Activity Score: ").append(minActivityScore).append("\n");
        sb.append("  Max Stationary Checks: ").append(maxStationaryChecks).append("\n");
        sb.append("  Min AFK Indicators: ").append(minAfkIndicators).append("\n");
        sb.append("  Pattern Detection: ").append(patternDetectionEnabled).append("\n");
        sb.append("  Progressive Penalties: ").append(progressivePenalties).append("\n");
        sb.append("  Max Warnings: ").append(maxWarnings).append("\n");
        sb.append("  Locations - Dungeon: ").append(dungeonDetectionEnabled);
        sb.append(", Mine: ").append(mineDetectionEnabled);
        sb.append(", Farmer: ").append(farmerDetectionEnabled);
        return sb.toString();
    }
}
