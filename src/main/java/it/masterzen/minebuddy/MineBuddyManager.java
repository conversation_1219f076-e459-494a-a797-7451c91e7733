package it.masterzen.minebuddy;

import it.masterzen.blockbreak.AlphaBlockBreak;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;

/**
 * Manager class for Mine Buddy configuration and settings
 */
public class MineBuddyManager {

    private final AlphaBlockBreak plugin;
    private final MineBuddy mineBuddy;
    private final MineBuddyCommand command;

    // Configuration
    private File configFile;
    private FileConfiguration config;

    // Settings
    private boolean enabled = true;
    private int taskInterval = 100; // 5 seconds in ticks
    private int mineBlocksMin = 1;
    private int mineBlocksMax = 5;
    private int farmerBlocksMin = 1;
    private int farmerBlocksMax = 2;
    private int dungeonBlocksMin = 1;
    private int dungeonBlocksMax = 3;

    public MineBuddyManager(AlphaBlockBreak plugin) {
        this.plugin = plugin;
        this.mineBuddy = new MineBuddy(plugin);
        this.command = new MineBuddyCommand(plugin, this);

        setupConfig();
        loadConfig();

        // Register command
        plugin.getCommand("minebuddy").setExecutor(command);
    }

    /**
     * Starts the Mine Buddy system
     */
    public void start() {
        if (enabled) {
            mineBuddy.start();
            plugin.getLogger().info("Mine Buddy Manager started successfully");
        } else {
            plugin.getLogger().info("Mine Buddy is disabled in configuration");
        }
    }

    /**
     * Stops the Mine Buddy system
     */
    public void stop() {
        mineBuddy.stop();
        plugin.getLogger().info("Mine Buddy Manager stopped");
    }

    /**
     * Reloads the configuration
     */
    public void reload() {
        loadConfig();
        mineBuddy.setEnabled(enabled);
        plugin.getLogger().info("Mine Buddy configuration reloaded");
    }

    /**
     * Sets up the configuration file
     */
    private void setupConfig() {
        configFile = new File(plugin.getDataFolder(), "minebuddy.yml");
        if (!configFile.exists()) {
            plugin.getDataFolder().mkdirs();
            createDefaultConfig();
        }
        config = YamlConfiguration.loadConfiguration(configFile);
    }

    /**
     * Creates the default configuration file
     */
    private void createDefaultConfig() {
        try {
            configFile.createNewFile();
            FileConfiguration defaultConfig = YamlConfiguration.loadConfiguration(configFile);

            defaultConfig.set("enabled", true);
            defaultConfig.set("task-interval-ticks", 100);
            defaultConfig.set("mine.blocks-min", 1);
            defaultConfig.set("mine.blocks-max", 5);
            defaultConfig.set("farmer.blocks-min", 1);
            defaultConfig.set("farmer.blocks-max", 2);
            defaultConfig.set("dungeon.blocks-min", 1);
            defaultConfig.set("dungeon.blocks-max", 3);

            defaultConfig.save(configFile);
            plugin.getLogger().info("Created default Mine Buddy configuration file");
        } catch (IOException e) {
            plugin.getLogger().severe("Could not create Mine Buddy configuration file: " + e.getMessage());
        }
    }

    /**
     * Loads configuration from file
     */
    private void loadConfig() {
        config = YamlConfiguration.loadConfiguration(configFile);

        enabled = config.getBoolean("enabled", true);
        taskInterval = config.getInt("task-interval-ticks", 100);
        mineBlocksMin = config.getInt("mine.blocks-min", 1);
        mineBlocksMax = config.getInt("mine.blocks-max", 5);
        farmerBlocksMin = config.getInt("farmer.blocks-min", 1);
        farmerBlocksMax = config.getInt("farmer.blocks-max", 2);
        dungeonBlocksMin = config.getInt("dungeon.blocks-min", 1);
        dungeonBlocksMax = config.getInt("dungeon.blocks-max", 3);
    }

    // Getters
    public MineBuddy getMineBuddy() { return mineBuddy; }
    public boolean isEnabled() { return enabled; }
    public int getTaskInterval() { return taskInterval; }
    public int getMineBlocksMin() { return mineBlocksMin; }
    public int getMineBlocksMax() { return mineBlocksMax; }
    public int getFarmerBlocksMin() { return farmerBlocksMin; }
    public int getFarmerBlocksMax() { return farmerBlocksMax; }
    public int getDungeonBlocksMin() { return dungeonBlocksMin; }
    public int getDungeonBlocksMax() { return dungeonBlocksMax; }
}
