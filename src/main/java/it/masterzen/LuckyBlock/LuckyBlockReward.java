package it.masterzen.LuckyBlock;

import org.bukkit.Material;

public class Lucky<PERSON>lockReward {

    private int id;
    private String name;
    private String message;
    private String commandToExecute;
    private double chance;

    public LuckyBlockReward(int id, String name, String message, String commandToExecute, double chance) {
        this.id = id;
        this.name = name;
        this.message = message;
        this.commandToExecute = commandToExecute;
        this.chance = chance;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getCommandToExecute() {
        return commandToExecute;
    }

    public void setCommandToExecute(String commandToExecute) {
        this.commandToExecute = commandToExecute;
    }

    public double getChance() {
        return chance;
    }

    public void setChance(double chance) {
        this.chance = chance;
    }
}
