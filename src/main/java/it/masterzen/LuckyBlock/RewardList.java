package it.masterzen.LuckyBlock;

import java.util.ArrayList;
import java.util.List;

public class RewardList {

    public RewardList() {
        loadList();
    }

    private List<LuckyBlockReward> rewardList = new ArrayList<>();

    public void loadList() {
        //rewardList.add(new LuckyBlockReward(1, "Keys", "", "giveKeys", 10D));
        rewardList.add(new LuckyBlockReward(1, "Beacon", "", "giveBeacon", 25D));
        rewardList.add(new LuckyBlockReward(2, "Armor Points", "", "giveArmorPoints", 5D));
        rewardList.add(new LuckyBlockReward(3, "Crystals", "", "giveCrystals", 15D));
        rewardList.add(new LuckyBlockReward(4, "Spawner Shard Points", "", "giveSpawnerPoints", 25D));
        rewardList.add(new LuckyBlockReward(5, "Candys", "", "giveCandy", 5D));
        rewardList.add(new Lucky<PERSON><PERSON>Reward(6, "Farmer Points", "", "giveFarmerPoints", 7.5D));
    }

    public List<LuckyBlockReward> getRewardList() {
        return this.rewardList;
    }
}
