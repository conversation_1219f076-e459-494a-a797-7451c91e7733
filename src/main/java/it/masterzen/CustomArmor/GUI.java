package it.masterzen.CustomArmor;

import com.sk89q.worldedit.MaxChangedBlocksException;
import it.masterzen.blockbreak.SerializableItemStack;
import it.masterzen.blockbreak.XMaterial;
import net.md_5.bungee.api.chat.ClickEvent;
import org.bukkit.Bukkit;
import org.bukkit.Color;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.event.inventory.InventoryAction;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.LeatherArmorMeta;
import org.bukkit.inventory.meta.SkullMeta;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

public class GUI implements Listener {

    private final String prefix = "§e§lARMOR §8»§7 ";
    private it.masterzen.CustomArmor.Main main;

    public GUI(Main main) {
        this.main = main;
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (event.getClickedInventory() != null && event.getClickedInventory().equals(event.getView().getTopInventory())) {
            Player player = (Player) event.getWhoClicked();

            switch (event.getView().getTitle()) {
                case "§e§lARMOR §f| §7Menu": {
                    event.setCancelled(true);
                    // SE STO GUARDANDO INVENTARIO DI UN PLAYER -> NON FACCIO NULLA
                    if (event.getClickedInventory() != null && event.getClickedInventory().getItem(4) != null && !event.getClickedInventory().getItem(4).getType().equals(it.masterzen.prestigemine.XMaterial.PLAYER_HEAD.parseMaterial())) {
                        if (event.getSlot() == 13) {
                            if (event.isLeftClick()) {
                                upgradeArmor(player, "Helmet");
                            } else if (event.isRightClick()) {
                                openPreviewGUI(player, "Helmet");
                            } else if (event.getClick().equals(ClickType.MIDDLE)) {
                                prestigeArmor(player, "Helmet");
                            }
                        } else if (event.getSlot() == 22) {
                            if (event.isLeftClick()) {
                                upgradeArmor(player, "Chestplate");
                            } else if (event.isRightClick()) {
                                openPreviewGUI(player, "Chestplate");
                            } else if (event.getClick().equals(ClickType.MIDDLE)) {
                                prestigeArmor(player, "Chestplate");
                            }
                        } else if (event.getSlot() == 31) {
                            if (event.isLeftClick()) {
                                upgradeArmor(player, "Leggings");
                            } else if (event.isRightClick()) {
                                openPreviewGUI(player, "Leggings");
                            } else if (event.getClick().equals(ClickType.MIDDLE)) {
                                prestigeArmor(player, "Leggings");
                            }
                        } else if (event.getSlot() == 40) {
                            if (event.isLeftClick()) {
                                upgradeArmor(player, "Boots");
                            } else if (event.isRightClick()) {
                                openPreviewGUI(player, "Boots");
                            } else if (event.getClick().equals(ClickType.MIDDLE)) {
                                prestigeArmor(player, "Boots");
                            }
                        } else if (event.getSlot() == 15) {
                            main.getPlayerCustomArmor().get(player.getUniqueId()).setPiece("Helmet", 0);
                            openGUI(player);
                        } else if (event.getSlot() == 24) {
                            main.getPlayerCustomArmor().get(player.getUniqueId()).setPiece("Chestplate", 0);
                            openGUI(player);
                        } else if (event.getSlot() == 33) {
                            main.getPlayerCustomArmor().get(player.getUniqueId()).setPiece("Leggings", 0);
                            openGUI(player);
                        } else if (event.getSlot() == 42) {
                            main.getPlayerCustomArmor().get(player.getUniqueId()).setPiece("Boots", 0);
                            openGUI(player);
                        }
                    }

                    break;
                }
                case "§e§lARMOR §f| §7Preview": {
                    event.setCancelled(true);

                    if (event.getSlot() == 45) {
                        openGUI(player);
                        return;
                    }
                    if (event.getCurrentItem() != null && !event.getCurrentItem().getType().equals(Material.AIR) && !event.getCurrentItem().equals(XMaterial.WHITE_STAINED_GLASS_PANE.parseItem())) {
                        // SE CLICCO SU ARMATURA -> CONTROLLO PEX
                        String piece = "";
                        if (event.getCurrentItem().getItemMeta().getDisplayName().contains("Helmet")) {
                            piece = "Helmet";
                        } else if (event.getCurrentItem().getItemMeta().getDisplayName().contains("Chestplate")) {
                            piece = "Chestplate";
                        } else if (event.getCurrentItem().getItemMeta().getDisplayName().contains("Leggings")) {
                            piece = "Leggings";
                        } else if (event.getCurrentItem().getItemMeta().getDisplayName().contains("Boots")) {
                            piece = "Boots";
                        }

                        int id = 0;
                        if (event.getSlot() < 9) {
                            id = event.getSlot() / 2;
                        } else if (event.getSlot() > 17) {
                            id = event.getSlot() - 13;
                        }

                        if (player.hasPermission(main.getArmorList().getItemList(piece).get(id).getPex())) {
                            // ID + 1 ALTRIMENTI PRENDE QUELLO SBAGLIATO
                            if (event.isLeftClick() || event.isRightClick()) {
                                main.getPlayerCustomArmor().get(player.getUniqueId()).setPiece(piece, (id + 1)); // <---
                                player.sendMessage(prefix + main.getArmorList().getItemList(piece).get(id).getName() + " §7Equipped");
                                openGUI(player);
                            } else if (event.getClick().equals(ClickType.MIDDLE)) {
                                prestigeArmor(player, main.getArmorList().getItemList(piece).get(id), piece);
                            }
                        } else {
                            player.sendMessage(prefix + "§cYou need to unlock this piece first");
                        }
                    }
                    break;
                }
            }
        }
    }

    public void prestigeArmor(Player player, String piece) {
        if (main.mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()).getFromPiece(piece) > 0) {
            long playerPoint = main.mainClass.getArmorPointsSystem().getPoints(player);
            int prestigeLevel = 0;
            ItemList armor = main.mainClass.getArmorSystem().getArmorList().getItemList(piece).get(main.mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()).getFromPiece(piece) - 1);

            for (int i = 1; i <= 5; i++) {
                if (player.hasPermission(armor.getPex() + ".prestige." + i)) {
                    prestigeLevel++;
                }
            }

            if (prestigeLevel < 5) {
                if (playerPoint >= 500) {
                    main.mainClass.getArmorPointsSystem().removePoints(player, 500);
                    main.mainClass.addPex(player, armor.getPex() + ".prestige." + (prestigeLevel + 1), 0, false);
                    player.sendMessage(prefix + "§aPrestige " + (prestigeLevel + 1) + " Unlocked !");
                    openGUI(player);
                } else {
                    player.sendMessage(prefix + "§cYou don't have enough points");
                }
            } else {
                player.sendMessage(prefix + "§cThis armor is already max prestige");
            }
        }
    }

    public void prestigeArmor(Player player, ItemList armor, String piece) {
        long playerPoint = main.mainClass.getArmorPointsSystem().getPoints(player);
        int prestigeLevel = 0;

        for (int i = 1; i <= 5; i++) {
            if (player.hasPermission(armor.getPex() + ".prestige." + i)) {
                prestigeLevel++;
            }
        }

        if (prestigeLevel < 5) {
            if (playerPoint >= 500) {
                main.mainClass.getArmorPointsSystem().removePoints(player, 500);
                main.mainClass.addPex(player, armor.getPex() + ".prestige." + (prestigeLevel + 1), 0, false);
                player.sendMessage(prefix + "§aPrestige " + (prestigeLevel + 1) + " Unlocked !");
                if (player.getOpenInventory().getTopInventory().getName().contains("Menu")) {
                    openGUI(player);
                } else {
                    openPreviewGUI(player, piece);
                }
            } else {
                player.sendMessage(prefix + "§cYou don't have enough points");
            }
        } else {
            player.sendMessage(prefix + "§cThis armor is already max prestige");
        }
    }

    public void upgradeArmor(Player player, String piece) {
        int getActualLevel = 0;
        long playerPoint = main.mainClass.getArmorPointsSystem().getPoints(player);
        List<ItemList> armorList = main.getArmorList().getItemList(piece);

        for (int i = 0; i < 5; i++) {
            if (player.hasPermission(armorList.get(i).getPex())) {
                getActualLevel++;
            }
        }

        if (getActualLevel < 5) {
            if (playerPoint >= armorList.get(getActualLevel).getPrice()) {
                main.mainClass.getArmorPointsSystem().removePoints(player, armorList.get(getActualLevel).getPrice());
                main.mainClass.addPex(player, armorList.get(getActualLevel).getPex(), 0, false);
                player.sendMessage(prefix + "§cYou succesfully bought the " + armorList.get(getActualLevel).getName());
                main.getPlayerCustomArmor().get(player.getUniqueId()).setPiece(piece, getActualLevel + 1);
                openGUI(player);
            } else {
                player.sendMessage(prefix + "§cYou don't have enough points. You need of §l" + armorList.get(getActualLevel).getPrice() + "§c points");
            }
        } else {
            player.sendMessage(prefix + "§aYou have already bought all the possible upgrades");
        }
    }

    public List<String> getLoreFromArmor(ItemList armor, boolean status, Player player, boolean prestiges) {
        List<String> lore = new ArrayList<>();

        int prestigeLevel = 0;
        if (prestiges) {
            for (int i = 1; i <= 5; i++) {
                if (player.hasPermission(armor.getPex() + ".prestige." + i)) {
                    prestigeLevel++;
                }
            }
        }

        lore.add("");
        lore.add("§7Rarity: " + armor.getRarity().toUpperCase());
        lore.add("");
        if (armor.getPerks().size() > 0) {
            lore.add("§7Perks");
            List<String> perkList = new ArrayList<>();
            perkList.add("Token");
            perkList.add("Money");
            perkList.add("XP");
            perkList.add("Keys");

            /*for (PerkItem perk : armor.getPerks()) {
                lore.add("  §a+ " + (perk.getBoost() + (perk.getType().equalsIgnoreCase("Beacon") ? prestigeLevel : (prestigeLevel * 5)) + (perk.getType().equalsIgnoreCase("Beacon") ? "" : "%") + " §7" + perk.getType() + " §7§o(+" + (perk.getType().equalsIgnoreCase("Beacon") ? prestigeLevel : (prestigeLevel * 5)) + " from prestiges)"));
                perkList.remove(perk.getType());
            }*/

            for (PerkItem perk : armor.getPerks()) {
                lore.add("  §a+ " + (perk.getBoost() + (perk.getType().equalsIgnoreCase("Beacon") ? prestigeLevel : (prestigeLevel * 5))) + (perk.getType().equalsIgnoreCase("Beacon") ? "" : "%") + " §7" + perk.getType() + (prestigeLevel > 0 ? " §7§o(+" + (perk.getType().equalsIgnoreCase("Beacon") ? prestigeLevel : (prestigeLevel * 5)) + " from prestiges)" : ""));
                perkList.remove(perk.getType());
            }

            if (prestigeLevel > 0) {
                if (!armor.getPex().contains("beacon")) {
                    for (String perkFromPrestige : perkList) {
                        lore.add("  §a+ " + (prestigeLevel * 5) + "% §7" + perkFromPrestige + " §7§o(+" + (prestigeLevel * 5) + " from prestiges)");
                    }
                }
            }
        }
        if (status) {
            lore.add("§7Status: " + (player.hasPermission(armor.getPex()) ? "§a§lUNLOCKED" : "§c§lLOCKED"));
            if (!player.hasPermission(armor.getPex())) {
                if (armor.getPrice() > 0) {
                    lore.add("§7Price: §c" + armor.getPrice());
                } else if (armor.getPex().contains("alpha")) {
                    lore.add("");
                    lore.add("§7Obtainable only from §cMonthly Crates");
                }
            } else {
                if (prestiges) {
                    lore.add("");
                    lore.add("§7Prestiges");

                    StringBuilder tmp = new StringBuilder();
                    tmp.append("  ");
                    for (int i = 1; i <= 5; i++) {
                        if (player.hasPermission(armor.getPex() + ".prestige." + i)) {
                            tmp.append("§7[§a§lV§7]");
                            tmp.append(" ");
                        } else {
                            tmp.append("§7[§c§lX§7]");
                            tmp.append(" ");
                        }
                    }
                    lore.add(tmp.toString());
                    if (prestigeLevel < 5) {
                        lore.add("");
                        lore.add("§7Prestige Price: §c500");
                        lore.add("§7Middle click to prestige");
                    }
                }
                lore.add("");
                lore.add("§7Click to equip");
            }
        } else if (prestiges) {
            lore.add("");
            lore.add("§7Prestiges");

            StringBuilder tmp = new StringBuilder();
            tmp.append("  ");
            for (int i = 1; i <= 5; i++) {
                if (player.hasPermission(armor.getPex() + ".prestige." + i)) {
                    tmp.append("§7[§a§lV§7]");
                    tmp.append(" ");
                } else {
                    tmp.append("§7[§c§lX§7]");
                    tmp.append(" ");
                }
            }
            lore.add(tmp.toString());
            if (prestigeLevel < 5) {
                lore.add("");
                lore.add("§7Prestige Price: §c500");
                lore.add("§7Middle click to prestige");
            }
        }

        return lore;
    }

    public ItemStack getItemFromID(String piece, int id, boolean status, Player player, boolean prestiges) {
        List<it.masterzen.CustomArmor.ItemList> itemList = main.getArmorList().getItemList(piece);
        ItemList item = itemList.get(id - 1);

        ItemStack tmpItem = new ItemStack(item.getMaterial());
        if (item.getLeatherColor() != null) {
            LeatherArmorMeta meta = (LeatherArmorMeta) tmpItem.getItemMeta();
            meta.setColor(item.getLeatherColor());
            meta.setDisplayName(item.getName());
            List<String> lore = getLoreFromArmor(item, status, player, prestiges);
            meta.setLore(lore);
            if (player.hasPermission(item.getPex())) {
                main.mainClass.addGlowing(meta);
            }
            tmpItem.setItemMeta(meta);
        } else {
            ItemMeta meta = tmpItem.getItemMeta();
            meta.setDisplayName(item.getName());
            List<String> lore = getLoreFromArmor(item, status, player, prestiges);
            meta.setLore(lore);
            if (player.hasPermission(item.getPex())) {
                main.mainClass.addGlowing(meta);
            }
            tmpItem.setItemMeta(meta);
        }

        return tmpItem;
    }

    public void openPreviewGUI(Player player, String piece) {
        Inventory gui = Bukkit.createInventory(null, 54, "§e§lARMOR §f| §7Preview");
        List<it.masterzen.CustomArmor.ItemList> itemList = main.getArmorList().getItemList(piece);
        int currentSlot = 0;

        for (int i = 9; i < 18; i++) {
            gui.setItem(i, XMaterial.WHITE_STAINED_GLASS_PANE.parseItem());
        }

        for (ItemList item : itemList) {
            gui.setItem(currentSlot, getItemFromID(piece, item.getId(), true, player, true));
            if (currentSlot < 8) {
                currentSlot = currentSlot + 2;
            } else if (currentSlot == 8) {
                currentSlot = 18;
            } else {
                currentSlot++;
            }
        }

        ItemStack back = new ItemStack(Material.ARROW);
        ItemMeta meta = back.getItemMeta();
        meta.setDisplayName("§cBack to Main Menu");
        back.setItemMeta(meta);
        gui.setItem(45, back);

        player.openInventory(gui);
    }

    public List<String> getPerksResume(Player player) {
        List<PerkItem> perks = main.getPlayerPerks(player);
        List<String> perkResume = new ArrayList<>();

        HashMap<String, Double> tmpValues = new HashMap<>();
        for (PerkItem perk : perks) {
            if (!tmpValues.containsKey(perk.getType())) {
                tmpValues.put(perk.getType(), perk.getBoost());
            } else {
                tmpValues.replace(perk.getType(), tmpValues.get(perk.getType()), tmpValues.get(perk.getType()) + perk.getBoost());
            }
        }

        perkResume.add("");
        for (String perk : tmpValues.keySet()) {
            perkResume.add("    §a+ " + tmpValues.get(perk) + (perk.equalsIgnoreCase("Beacon") ? "" : "%") + " §7" + perk);
        }

        return perkResume;
    }

    public void viewAmor(Player player, Player playerToView) {
        Inventory gui = Bukkit.createInventory(null, 54, "§e§lARMOR §f| §7Menu");
        it.masterzen.commands.Main.FillBorder(gui);

        ItemStack noArmor = XMaterial.RED_STAINED_GLASS_PANE.parseItem();
        ItemMeta meta = noArmor.getItemMeta();
        meta.setDisplayName("§c§lLOCKED / MISSED");
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§cHe has no armor equipped");
        lore.add("");
        meta.setLore(lore);
        noArmor.setItemMeta(meta);

        ItemStack perkResume = new ItemStack(Material.BOOK);
        meta = perkResume.getItemMeta();
        meta.setDisplayName("§6§lPERK §7Resume");
        lore = getPerksResume(playerToView);
        meta.setLore(lore);
        perkResume.setItemMeta(meta);

        ItemStack owner = new ItemStack(Objects.requireNonNull(it.masterzen.prestigemine.XMaterial.PLAYER_HEAD.parseItem()));
        SkullMeta headMeta = (SkullMeta) owner.getItemMeta();
        headMeta.setOwningPlayer(Bukkit.getOfflinePlayer(playerToView.getUniqueId()));
        headMeta.setDisplayName("§7" + playerToView.getName() + " §7Armor Set");
        owner.setItemMeta(headMeta);

        gui.setItem(4, owner);
        gui.setItem(11, perkResume);
        gui.setItem(13, main.getPlayerCustomArmor().get(playerToView.getUniqueId()).getHelmet() == 0 ? noArmor : getItemFromID("Helmet", main.getPlayerCustomArmor().get(playerToView.getUniqueId()).getHelmet(), false, playerToView, true));
        gui.setItem(22, main.getPlayerCustomArmor().get(playerToView.getUniqueId()).getChestplate() == 0 ? noArmor : getItemFromID("Chestplate", main.getPlayerCustomArmor().get(playerToView.getUniqueId()).getChestplate(), false, playerToView, true));
        gui.setItem(31, main.getPlayerCustomArmor().get(playerToView.getUniqueId()).getLeggings() == 0 ? noArmor : getItemFromID("Leggings", main.getPlayerCustomArmor().get(playerToView.getUniqueId()).getLeggings(), false, playerToView, true));
        gui.setItem(40, main.getPlayerCustomArmor().get(playerToView.getUniqueId()).getBoots() == 0 ? noArmor : getItemFromID("Boots", main.getPlayerCustomArmor().get(playerToView.getUniqueId()).getBoots(), false, playerToView, true));

        player.openInventory(gui);
    }

    public void openGUI(Player player) {
        Inventory gui = Bukkit.createInventory(null, 54, "§e§lARMOR §f| §7Menu");
        it.masterzen.commands.Main.FillBorder(gui);

        ItemStack noArmor = XMaterial.RED_STAINED_GLASS_PANE.parseItem();
        ItemMeta meta = noArmor.getItemMeta();
        meta.setDisplayName("§c§lLOCKED / MISSED");
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§cYou have no armor equipped");
        lore.add("");
        lore.add("§7Left click to buy");
        lore.add("§7Right click to equip");
        meta.setLore(lore);
        noArmor.setItemMeta(meta);

        ItemStack points = new ItemStack(Material.BOOK);
        meta = points.getItemMeta();
        meta.setDisplayName("§6§lPOINTS §7Balance");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§7You got a total of §a§l" + main.mainClass.getArmorPointsSystem().getPoints(player) + "§7 points");
        meta.setLore(lore);
        points.setItemMeta(meta);

        ItemStack perkResume = new ItemStack(Material.BOOK);
        meta = perkResume.getItemMeta();
        meta.setDisplayName("§6§lPERK §7Resume");
        lore = getPerksResume(player);
        meta.setLore(lore);
        perkResume.setItemMeta(meta);

        ItemStack remove = XMaterial.RED_STAINED_GLASS_PANE.parseItem();
        meta = remove.getItemMeta();
        meta.setDisplayName("§cClick to remove");
        remove.setItemMeta(meta);

        gui.setItem(4, points);
        gui.setItem(11, perkResume);

        if (main.getPlayerCustomArmor().get(player.getUniqueId()).getHelmet() != 0) {
            gui.setItem(15, remove);
        }
        if (main.getPlayerCustomArmor().get(player.getUniqueId()).getChestplate() != 0) {
            gui.setItem(24, remove);
        }
        if (main.getPlayerCustomArmor().get(player.getUniqueId()).getLeggings() != 0) {
            gui.setItem(33, remove);
        }
        if (main.getPlayerCustomArmor().get(player.getUniqueId()).getBoots() != 0) {
            gui.setItem(42, remove);
        }

        gui.setItem(13, main.getPlayerCustomArmor().get(player.getUniqueId()).getHelmet() == 0 ? noArmor : getItemFromID("Helmet", main.getPlayerCustomArmor().get(player.getUniqueId()).getHelmet(), false, player, true));
        gui.setItem(22, main.getPlayerCustomArmor().get(player.getUniqueId()).getChestplate() == 0 ? noArmor : getItemFromID("Chestplate", main.getPlayerCustomArmor().get(player.getUniqueId()).getChestplate(), false, player, true));
        gui.setItem(31, main.getPlayerCustomArmor().get(player.getUniqueId()).getLeggings() == 0 ? noArmor : getItemFromID("Leggings", main.getPlayerCustomArmor().get(player.getUniqueId()).getLeggings(), false, player, true));
        gui.setItem(40, main.getPlayerCustomArmor().get(player.getUniqueId()).getBoots() == 0 ? noArmor : getItemFromID("Boots", main.getPlayerCustomArmor().get(player.getUniqueId()).getBoots(), false, player, true));

        player.openInventory(gui);
    }
}
