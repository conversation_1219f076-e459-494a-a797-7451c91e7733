package it.masterzen.CustomArmor;

import it.masterzen.Robot.GUI;
import it.masterzen.Robot.Robot;
import it.masterzen.blockbreak.AlphaBlockBreak;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.Listener;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

public class ArmorPoints {

    private static HashMap<UUID, Long> points = new HashMap<>();
    private final String prefix = "§e§lARMOR §8»§7 ";
    private final String name = "ArmorPoints";
    private static YamlConfiguration ymlFile;

    public final AlphaBlockBreak mainClass;

    public ArmorPoints(AlphaBlockBreak plugin) {
        mainClass = plugin;
    }

    public String getPrefix() {
        return this.prefix;
    }

    public String getName() {
        return this.name;
    }

    public void loadPoints() {
        for (Player player : Bukkit.getOnlinePlayers()) {
            loadPoints(player);
        }
    }

    public void loadPoints(Player player) {
        File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/Player" + name + ".yml");
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (Exception ex) {
            }
        }

        ymlFile = YamlConfiguration.loadConfiguration(file);
        if (!points.containsKey(player.getUniqueId()) && ymlFile.contains(player.getUniqueId() + ".points")) {
            points.put(player.getUniqueId(), ymlFile.getLong(player.getUniqueId() + ".points"));
        }
    }

    public HashMap<UUID, Long> getPoints() {
        return points;
    }

    public void savePoints() throws IOException {
        File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/Player" + name + ".yml");
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (Exception ex) {
            }
        }

        ymlFile = YamlConfiguration.loadConfiguration(file);
        if (!points.isEmpty() && ymlFile != null) {
            for (UUID player : points.keySet()) {
                savePoints(player, false);
            }
        }
    }

    public void savePoints(Player player, boolean remove) throws IOException {
        if (!points.isEmpty() && points.containsKey(player.getUniqueId())) {
            File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/Player" + name + ".yml");
            ymlFile = YamlConfiguration.loadConfiguration(file);
            ymlFile.set(player.getUniqueId() + ".points", points.get(player.getUniqueId()));
            ymlFile.save(file);
            if (remove) {
                points.remove(player.getUniqueId());
            }
        }
    }

    public void savePoints(UUID player, boolean remove) throws IOException {
        if (!points.isEmpty() && points.containsKey(player)) {
            File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/Player" + name + ".yml");
            ymlFile = YamlConfiguration.loadConfiguration(file);
            ymlFile.set(player.toString() + ".points", points.get(player));
            ymlFile.save(file);
            if (remove) {
                points.remove(player);
            }
        }
    }

    public void addPoints(Player player, long amount) {
        if (!points.containsKey(player.getUniqueId())) {
            points.put(player.getUniqueId(), amount);
        } else {
            points.replace(player.getUniqueId(), points.get(player.getUniqueId()) + amount);
        }
    }

    public void removePoints(Player player, long amount) {
        if (points.containsKey(player.getUniqueId())) {
            points.replace(player.getUniqueId(), points.get(player.getUniqueId()) - amount);
        }
    }

    public void setPoints(Player player, long amount) {
        points.remove(player.getUniqueId());
        points.put(player.getUniqueId(), amount);
    }

    public void sendPoints(Player player) {
        if (!points.containsKey(player.getUniqueId())) {
            player.sendMessage(prefix + "You got §a§l0 §7" + name);
        } else {
            player.sendMessage(prefix + "You got §a§l" + points.get(player.getUniqueId()) + " §7" + name);
        }
    }

    public long getPoints(Player player) {
        if (!points.containsKey(player.getUniqueId())) {
            return 0;
        } else {
            return points.get(player.getUniqueId());
        }
    }

    public void sendInfo(Player player) {
        player.sendMessage(prefix + "You got a total of §a§l" + getPoints(player) + "§7 " + name);
        player.sendMessage("");
        player.sendMessage("§7Command List");
        player.sendMessage("§7  /armor");
        player.sendMessage("");
    }
}
