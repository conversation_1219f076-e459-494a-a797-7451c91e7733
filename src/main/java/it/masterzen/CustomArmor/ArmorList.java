package it.masterzen.CustomArmor;

import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.blockbreak.XMaterial;
import org.bukkit.Color;
import org.bukkit.Material;

import java.util.*;

public class ArmorList {

    private final HashMap<String, List<it.masterzen.CustomArmor.ItemList>> armorList = new HashMap<>();

    public ArmorList() {
        setupArmor();
    }

    public HashMap<String, List<it.masterzen.CustomArmor.ItemList>> getArmorList() {
        return this.armorList;
    }

    public List<it.masterzen.CustomArmor.ItemList> getItemList(String piece) {
        List<it.masterzen.CustomArmor.ItemList> itemList = new ArrayList<>();

        if (armorList.containsKey(piece)) {
            itemList = armorList.get(piece);
        } else {
            AlphaBlockBreak.GetInstance().getLogger().info("§cError while finding §c§l" + piece + "§c in getItemList (ArmorList.java)");
        }

        return itemList;
    }

    public void setupArmor() {
        List<it.masterzen.CustomArmor.ItemList> itemList = new ArrayList<>();

        itemList = new ArrayList<>();
        itemList.add(new ItemList(1, Material.LEATHER_HELMET, null, "§6§lLEATHER §7Helmet", "§a§lCOMMON", new ArrayList<>(Collections.singletonList(new PerkItem("XP", 50))), "customarmor.leather.helmet", 100, 0));
        itemList.add(new ItemList(2, Material.CHAINMAIL_HELMET, null, "§7§lCHAIN §7Helmet", "§2§lUNCOMMON", new ArrayList<>(Collections.singletonList(new PerkItem("XP", 75))), "customarmor.chain.helmet", 200, 0));
        itemList.add(new ItemList(3, Material.IRON_HELMET, null, "§f§lIRON §7Helmet", "§b§lRARE", new ArrayList<>(Collections.singletonList(new PerkItem("XP", 100))), "customarmor.iron.helmet", 300, 0));
        itemList.add(new ItemList(4, Material.GOLD_HELMET, null, "§e§lGOLD §7Helmet", "§d§lEPIC", new ArrayList<>(Collections.singletonList(new PerkItem("XP", 125))), "customarmor.gold.helmet", 400, 0));
        itemList.add(new ItemList(5, Material.DIAMOND_HELMET, null, "§b§lDIAMOND §7Helmet", "§c§lLEGENDARY", new ArrayList<>(Collections.singletonList(new PerkItem("XP", 150))), "customarmor.diamond.helmet", 500, 0));
        //itemList.add(new ItemList(6, Material.LEATHER_HELMET, Color.AQUA, "§b§lBEACON §7Helmet", "§b§lRARE", new ArrayList<>(Arrays.asList(new PerkItem("XP", 50), (new PerkItem("Beacon", 5)))), "customarmor.leather.helmet", 0, 1));
        //itemList.add(new ItemList(6, Material.LEATHER_HELMET, Color.AQUA, "§b§lBEACON §7Helmet", "§d§lEPIC", new ArrayList<>(Collections.singletonList((new PerkItem("Beacon", 5)))), "customarmor.beacon.helmet", 0, 1));
        itemList.add(new ItemList(6, Material.LEATHER_HELMET, Color.fromRGB(152,205,119), "§f§lPEGASUS §7Helmet", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Money", 12.5), (new PerkItem("XP", 12.5)))), "customarmor.pegasus.helmet", 0, 25));
        itemList.add(new ItemList(7, Material.LEATHER_HELMET, Color.fromRGB(208,226,50), "§e§lGRIFFON §7Helmet", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Money", 12.5), (new PerkItem("Token", 12.5)))), "customarmor.griffon.helmet", 0, 25));
        itemList.add(new ItemList(8, Material.LEATHER_HELMET, Color.fromRGB(186,236,166), "§f§lUNICORN §7Helmet", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Money", 12.5), (new PerkItem("Keys", 12.5)))), "customarmor.unicorn.helmet", 0, 25));
        itemList.add(new ItemList(9, Material.LEATHER_HELMET, Color.fromRGB(102,208,136), "§6§lSPHINX §7Helmet", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Token", 12.5), (new PerkItem("Keys", 12.5)))), "customarmor.sphinx.helmet", 0, 25));
        itemList.add(new ItemList(10, Material.LEATHER_HELMET, Color.fromRGB(75,177,93), "§2§lBASILISK §7Helmet", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Token", 12.5), (new PerkItem("XP", 12.5)))), "customarmor.basilisk.helmet", 0, 25));
        itemList.add(new ItemList(11, Material.LEATHER_HELMET, Color.fromRGB(40,167,187), "§9§lCENTAUR §7Helmet", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("XP", 12.5), (new PerkItem("Keys", 12.5)))), "customarmor.centaur.helmet", 0, 25));
        itemList.add(new ItemList(12, Material.LEATHER_HELMET, Color.fromRGB(209,133,134), "§c§lMINOTAUR §7Helmet", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Money", 25), (new PerkItem("XP", 25)))), "customarmor.minotaur.helmet", 0, 25));
        itemList.add(new ItemList(13, Material.LEATHER_HELMET, Color.fromRGB(186,137,27), "§8§lCERBERUS §7Helmet", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Money", 25), (new PerkItem("Token", 25)))), "customarmor.cerberus.helmet", 0, 25));
        itemList.add(new ItemList(14, Material.LEATHER_HELMET, Color.fromRGB(192,202,131), "§a§lSATYR §7Helmet", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Money", 25), (new PerkItem("Keys", 25)))), "customarmor.satyr.helmet", 0, 25));
        itemList.add(new ItemList(15, Material.LEATHER_HELMET, Color.fromRGB(58,140,89), "§7§lWEREWOLF §7Helmet", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Token", 25), (new PerkItem("Keys", 25)))), "customarmor.werewolf.helmet", 0, 25));
        itemList.add(new ItemList(16, Material.LEATHER_HELMET, Color.fromRGB(117,147,147), "§6§lHARPIA §7Helmet", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Token", 25), (new PerkItem("XP", 25)))), "customarmor.harpia.helmet", 0, 25));
        itemList.add(new ItemList(17, Material.LEATHER_HELMET, Color.fromRGB(41,112,163), "§9§lMERMAID §7Helmet", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("XP", 25), (new PerkItem("Keys", 25)))), "customarmor.mermaid.helmet", 0, 25));
        itemList.add(new ItemList(18, Material.LEATHER_HELMET, Color.fromRGB(172,65,191), "§f§lANGEL §7Helmet", "§b§lRARE", new ArrayList<>(Arrays.asList(new PerkItem("Money", 25), (new PerkItem("Token", 25)), (new PerkItem("XP", 25)))), "customarmor.angel.helmet", 0, 10));
        itemList.add(new ItemList(19, Material.LEATHER_HELMET, Color.fromRGB(221,108,179), "§c§lPHOENIX §7Helmet", "§b§lRARE", new ArrayList<>(Arrays.asList(new PerkItem("Money", 25), (new PerkItem("Token", 25)), (new PerkItem("Keys", 25)))), "customarmor.phoenix.helmet", 0, 10));
        itemList.add(new ItemList(20, Material.LEATHER_HELMET, Color.AQUA, "§b§lBEACON §7Helmet", "§d§lEPIC", new ArrayList<>(Collections.singletonList((new PerkItem("Beacon", 5)))), "customarmor.beacon.helmet", 0, 5));
        itemList.add(new ItemList(21, Material.LEATHER_HELMET, Color.BLACK, "§c§lA§6§lL§e§lP§a§lH§b§lA §7Helmet", "§6§lLEGENDARY", new ArrayList<>(Arrays.asList(new PerkItem("Money", 25), (new PerkItem("Token", 25)), (new PerkItem("XP", 25)), (new PerkItem("Keys", 25)))), "customarmor.alpha.helmet", 0, 0));
        armorList.put("Helmet", itemList);

        itemList = new ArrayList<>();
        itemList.add(new ItemList(1, Material.LEATHER_CHESTPLATE, null, "§6§lLEATHER §7Chestplate", "§a§lCOMMON", new ArrayList<>(Collections.singletonList(new PerkItem("Token", 50))), "customarmor.leather.chestplate", 100, 0));
        itemList.add(new ItemList(2, Material.CHAINMAIL_CHESTPLATE, null, "§7§lCHAIN §7Chestplate", "§2§lUNCOMMON", new ArrayList<>(Collections.singletonList(new PerkItem("Token", 75))), "customarmor.chain.chestplate", 200, 0));
        itemList.add(new ItemList(3, Material.IRON_CHESTPLATE, null, "§f§lIRON §7Chestplate", "§b§lRARE", new ArrayList<>(Collections.singletonList(new PerkItem("Token", 100))), "customarmor.iron.chestplate", 300, 0));
        itemList.add(new ItemList(4, Material.GOLD_CHESTPLATE, null, "§e§lGOLD §7Chestplate", "§d§lEPIC", new ArrayList<>(Collections.singletonList(new PerkItem("Token", 125))), "customarmor.gold.chestplate", 400, 0));
        itemList.add(new ItemList(5, Material.DIAMOND_CHESTPLATE, null, "§b§lDIAMOND §7Chestplate", "§c§lLEGENDARY", new ArrayList<>(Collections.singletonList(new PerkItem("Token", 150))), "customarmor.diamond.chestplate", 500, 0));
        itemList.add(new ItemList(6, Material.LEATHER_CHESTPLATE, Color.fromRGB(152,205,119), "§f§lPEGASUS §7Chestplate", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Money", 12.5), (new PerkItem("XP", 12.5)))), "customarmor.pegasus.chestplate", 0, 25));
        itemList.add(new ItemList(7, Material.LEATHER_CHESTPLATE, Color.fromRGB(208,226,50), "§e§lGRIFFON §7Chestplate", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Money", 12.5), (new PerkItem("Token", 12.5)))), "customarmor.griffon.chestplate", 0, 25));
        itemList.add(new ItemList(8, Material.LEATHER_CHESTPLATE, Color.fromRGB(186,236,166), "§f§lUNICORN §7Chestplate", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Money", 12.5), (new PerkItem("Keys", 12.5)))), "customarmor.unicorn.chestplate", 0, 25));
        itemList.add(new ItemList(9, Material.LEATHER_CHESTPLATE, Color.fromRGB(102,208,136), "§6§lSPHINX §7Chestplate", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Token", 12.5), (new PerkItem("Keys", 12.5)))), "customarmor.sphinx.chestplate", 0, 25));
        itemList.add(new ItemList(10, Material.LEATHER_CHESTPLATE, Color.fromRGB(75,177,93), "§2§lBASILISK §7Chestplate", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Token", 12.5), (new PerkItem("XP", 12.5)))), "customarmor.basilisk.chestplate", 0, 25));
        itemList.add(new ItemList(11, Material.LEATHER_CHESTPLATE, Color.fromRGB(40,167,187), "§9§lCENTAUR §7Chestplate", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("XP", 12.5), (new PerkItem("Keys", 12.5)))), "customarmor.centaur.chestplate", 0, 25));
        itemList.add(new ItemList(12, Material.LEATHER_CHESTPLATE, Color.fromRGB(209,133,134), "§c§lMINOTAUR §7Chestplate", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Money", 25), (new PerkItem("XP", 25)))), "customarmor.minotaur.chestplate", 0, 25));
        itemList.add(new ItemList(13, Material.LEATHER_CHESTPLATE, Color.fromRGB(186,137,27), "§8§lCERBERUS §7Chestplate", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Money", 25), (new PerkItem("Token", 25)))), "customarmor.cerberus.chestplate", 0, 25));
        itemList.add(new ItemList(14, Material.LEATHER_CHESTPLATE, Color.fromRGB(192,202,131), "§a§lSATYR §7Chestplate", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Money", 25), (new PerkItem("Keys", 25)))), "customarmor.satyr.chestplate", 0, 25));
        itemList.add(new ItemList(15, Material.LEATHER_CHESTPLATE, Color.fromRGB(58,140,89), "§7§lWEREWOLF §7Chestplate", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Token", 25), (new PerkItem("Keys", 25)))), "customarmor.werewolf.chestplate", 0, 25));
        itemList.add(new ItemList(16, Material.LEATHER_CHESTPLATE, Color.fromRGB(117,147,147), "§6§lHARPIA §7Chestplate", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Token", 25), (new PerkItem("XP", 25)))), "customarmor.harpia.chestplate", 0, 25));
        itemList.add(new ItemList(17, Material.LEATHER_CHESTPLATE, Color.fromRGB(41,112,163), "§9§lMERMAID §7Chestplate", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("XP", 25), (new PerkItem("Keys", 25)))), "customarmor.mermaid.chestplate", 0, 25));
        itemList.add(new ItemList(18, Material.LEATHER_CHESTPLATE, Color.fromRGB(172,65,191), "§f§lANGEL §7Chestplate", "§b§lRARE", new ArrayList<>(Arrays.asList(new PerkItem("Money", 25), (new PerkItem("Token", 25)), (new PerkItem("XP", 25)))), "customarmor.angel.chestplate", 0, 10));
        itemList.add(new ItemList(19, Material.LEATHER_CHESTPLATE, Color.fromRGB(221,108,179), "§c§lPHOENIX §7Chestplate", "§b§lRARE", new ArrayList<>(Arrays.asList(new PerkItem("Money", 25), (new PerkItem("Token", 25)), (new PerkItem("Keys", 25)))), "customarmor.phoenix.chestplate", 0, 10));
        itemList.add(new ItemList(20, Material.LEATHER_CHESTPLATE, Color.AQUA, "§b§lBEACON §7Chestplate", "§d§lEPIC", new ArrayList<>(Collections.singletonList((new PerkItem("Beacon", 5)))), "customarmor.beacon.chestplate", 0, 5));
        itemList.add(new ItemList(21, Material.LEATHER_CHESTPLATE, Color.BLACK, "§c§lA§6§lL§e§lP§a§lH§b§lA §7Chestplate", "§6§lLEGENDARY", new ArrayList<>(Arrays.asList(new PerkItem("Money", 25), (new PerkItem("Token", 25)), (new PerkItem("XP", 25)), (new PerkItem("Keys", 25)))), "customarmor.alpha.chestplate", 0, 0));
        armorList.put("Chestplate", itemList);

        itemList = new ArrayList<>();
        itemList.add(new ItemList(1, Material.LEATHER_LEGGINGS, null, "§6§lLEATHER §7Leggings", "§a§lCOMMON", new ArrayList<>(Collections.singletonList(new PerkItem("Money", 50))), "customarmor.leather.leggings", 100, 0));
        itemList.add(new ItemList(2, Material.CHAINMAIL_LEGGINGS, null, "§7§lCHAIN §7Leggings", "§2§lUNCOMMON", new ArrayList<>(Collections.singletonList(new PerkItem("Money", 75))), "customarmor.chain.leggings", 200, 0));
        itemList.add(new ItemList(3, Material.IRON_LEGGINGS, null, "§f§lIRON §7Leggings", "§b§lRARE", new ArrayList<>(Collections.singletonList(new PerkItem("Money", 100))), "customarmor.iron.leggings", 300, 0));
        itemList.add(new ItemList(4, Material.GOLD_LEGGINGS, null, "§e§lGOLD §7Leggings", "§d§lEPIC", new ArrayList<>(Collections.singletonList(new PerkItem("Money", 125))), "customarmor.gold.leggings", 400, 0));
        itemList.add(new ItemList(5, Material.DIAMOND_LEGGINGS, null, "§b§lDIAMOND §7Leggings", "§c§lLEGENDARY", new ArrayList<>(Collections.singletonList(new PerkItem("Money", 150))), "customarmor.diamond.leggings", 500, 0));
        itemList.add(new ItemList(6, Material.LEATHER_LEGGINGS, Color.fromRGB(152,205,119), "§f§lPEGASUS §7Leggings", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Money", 12.5), (new PerkItem("XP", 12.5)))), "customarmor.pegasus.leggings", 0, 25));
        itemList.add(new ItemList(7, Material.LEATHER_LEGGINGS, Color.fromRGB(208,226,50), "§e§lGRIFFON §7Leggings", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Money", 12.5), (new PerkItem("Token", 12.5)))), "customarmor.griffon.leggings", 0, 25));
        itemList.add(new ItemList(8, Material.LEATHER_LEGGINGS, Color.fromRGB(186,236,166), "§f§lUNICORN §7Leggings", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Money", 12.5), (new PerkItem("Keys", 12.5)))), "customarmor.unicorn.leggings", 0, 25));
        itemList.add(new ItemList(9, Material.LEATHER_LEGGINGS, Color.fromRGB(102,208,136), "§6§lSPHINX §7Leggings", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Token", 12.5), (new PerkItem("Keys", 12.5)))), "customarmor.sphinx.leggings", 0, 25));
        itemList.add(new ItemList(10, Material.LEATHER_LEGGINGS, Color.fromRGB(75,177,93), "§2§lBASILISK §7Leggings", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Token", 12.5), (new PerkItem("XP", 12.5)))), "customarmor.basilisk.leggings", 0, 25));
        itemList.add(new ItemList(11, Material.LEATHER_LEGGINGS, Color.fromRGB(40,167,187), "§9§lCENTAUR §7Leggings", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("XP", 12.5), (new PerkItem("Keys", 12.5)))), "customarmor.centaur.leggings", 0, 25));
        itemList.add(new ItemList(12, Material.LEATHER_LEGGINGS, Color.fromRGB(209,133,134), "§c§lMINOTAUR §7Leggings", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Money", 25), (new PerkItem("XP", 25)))), "customarmor.minotaur.leggings", 0, 25));
        itemList.add(new ItemList(13, Material.LEATHER_LEGGINGS, Color.fromRGB(186,137,27), "§8§lCERBERUS §7Leggings", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Money", 25), (new PerkItem("Token", 25)))), "customarmor.cerberus.leggings", 0, 25));
        itemList.add(new ItemList(14, Material.LEATHER_LEGGINGS, Color.fromRGB(192,202,131), "§a§lSATYR §7Leggings", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Money", 25), (new PerkItem("Keys", 25)))), "customarmor.satyr.leggings", 0, 25));
        itemList.add(new ItemList(15, Material.LEATHER_LEGGINGS, Color.fromRGB(58,140,89), "§7§lWEREWOLF §7Leggings", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Token", 25), (new PerkItem("Keys", 25)))), "customarmor.werewolf.leggings", 0, 25));
        itemList.add(new ItemList(16, Material.LEATHER_LEGGINGS, Color.fromRGB(117,147,147), "§6§lHARPIA §7Leggings", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Token", 25), (new PerkItem("XP", 25)))), "customarmor.harpia.leggings", 0, 25));
        itemList.add(new ItemList(17, Material.LEATHER_LEGGINGS, Color.fromRGB(41,112,163), "§9§lMERMAID §7Leggings", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("XP", 25), (new PerkItem("Keys", 25)))), "customarmor.mermaid.leggings", 0, 25));
        itemList.add(new ItemList(18, Material.LEATHER_LEGGINGS, Color.fromRGB(172,65,191), "§f§lANGEL §7Leggings", "§b§lRARE", new ArrayList<>(Arrays.asList(new PerkItem("Money", 25), (new PerkItem("Token", 25)), (new PerkItem("XP", 25)))), "customarmor.angel.leggings", 0, 10));
        itemList.add(new ItemList(19, Material.LEATHER_LEGGINGS, Color.fromRGB(221,108,179), "§c§lPHOENIX §7Leggings", "§b§lRARE", new ArrayList<>(Arrays.asList(new PerkItem("Money", 25), (new PerkItem("Token", 25)), (new PerkItem("Keys", 25)))), "customarmor.phoenix.leggings", 0, 10));
        itemList.add(new ItemList(20, Material.LEATHER_LEGGINGS, Color.AQUA, "§b§lBEACON §7Leggings", "§d§lEPIC", new ArrayList<>(Collections.singletonList((new PerkItem("Beacon", 5)))), "customarmor.beacon.leggings", 0, 5));
        itemList.add(new ItemList(21, Material.LEATHER_LEGGINGS, Color.BLACK, "§c§lA§6§lL§e§lP§a§lH§b§lA §7Leggings", "§6§lLEGENDARY", new ArrayList<>(Arrays.asList(new PerkItem("Money", 25), (new PerkItem("Token", 25)), (new PerkItem("XP", 25)), (new PerkItem("Keys", 25)))), "customarmor.alpha.leggings", 0, 0));
        armorList.put("Leggings", itemList);

        itemList = new ArrayList<>();
        itemList.add(new ItemList(1, Material.LEATHER_BOOTS, null, "§6§lLEATHER §7Boots", "§a§lCOMMON", new ArrayList<>(Collections.singletonList(new PerkItem("Keys", 50))), "customarmor.leather.boots", 100, 0));
        itemList.add(new ItemList(2, Material.CHAINMAIL_BOOTS, null, "§7§lCHAIN §7Boots", "§2§lUNCOMMON", new ArrayList<>(Collections.singletonList(new PerkItem("Keys", 75))), "customarmor.chain.boots", 200, 0));
        itemList.add(new ItemList(3, Material.IRON_BOOTS, null, "§f§lIRON §7Boots", "§b§lRARE", new ArrayList<>(Collections.singletonList(new PerkItem("Keys", 100))), "customarmor.iron.boots", 300, 0));
        itemList.add(new ItemList(4, Material.GOLD_BOOTS, null, "§e§lGOLD §7Boots", "§d§lEPIC", new ArrayList<>(Collections.singletonList(new PerkItem("Keys", 125))), "customarmor.gold.boots", 400, 0));
        itemList.add(new ItemList(5, Material.DIAMOND_BOOTS, null, "§b§lDIAMOND §7Boots", "§c§lLEGENDARY", new ArrayList<>(Collections.singletonList(new PerkItem("Keys", 150))), "customarmor.diamond.boots", 500, 0));
        itemList.add(new ItemList(6, Material.LEATHER_BOOTS, Color.fromRGB(152,205,119), "§f§lPEGASUS §7Boots", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Money", 12.5), (new PerkItem("XP", 12.5)))), "customarmor.pegasus.boots", 0, 25));
        itemList.add(new ItemList(7, Material.LEATHER_BOOTS, Color.fromRGB(208,226,50), "§e§lGRIFFON §7Boots", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Money", 12.5), (new PerkItem("Token", 12.5)))), "customarmor.griffon.boots", 0, 25));
        itemList.add(new ItemList(8, Material.LEATHER_BOOTS, Color.fromRGB(186,236,166), "§f§lUNICORN §7Boots", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Money", 12.5), (new PerkItem("Keys", 12.5)))), "customarmor.unicorn.boots", 0, 25));
        itemList.add(new ItemList(9, Material.LEATHER_BOOTS, Color.fromRGB(102,208,136), "§6§lSPHINX §7Boots", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Token", 12.5), (new PerkItem("Keys", 12.5)))), "customarmor.sphinx.boots", 0, 25));
        itemList.add(new ItemList(10, Material.LEATHER_BOOTS, Color.fromRGB(75,177,93), "§2§lBASILISK §7Boots", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Token", 12.5), (new PerkItem("XP", 12.5)))), "customarmor.basilisk.boots", 0, 25));
        itemList.add(new ItemList(11, Material.LEATHER_BOOTS, Color.fromRGB(40,167,187), "§9§lCENTAUR §7Boots", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("XP", 12.5), (new PerkItem("Keys", 12.5)))), "customarmor.centaur.boots", 0, 25));
        itemList.add(new ItemList(12, Material.LEATHER_BOOTS, Color.fromRGB(209,133,134), "§c§lMINOTAUR §7Boots", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Money", 25), (new PerkItem("XP", 25)))), "customarmor.minotaur.boots", 0, 25));
        itemList.add(new ItemList(13, Material.LEATHER_BOOTS, Color.fromRGB(186,137,27), "§8§lCERBERUS §7Boots", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Money", 25), (new PerkItem("Token", 25)))), "customarmor.cerberus.boots", 0, 25));
        itemList.add(new ItemList(14, Material.LEATHER_BOOTS, Color.fromRGB(192,202,131), "§a§lSATYR §7Boots", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Money", 25), (new PerkItem("Keys", 25)))), "customarmor.satyr.boots", 0, 25));
        itemList.add(new ItemList(15, Material.LEATHER_BOOTS, Color.fromRGB(58,140,89), "§7§lWEREWOLF §7Boots", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Token", 25), (new PerkItem("Keys", 25)))), "customarmor.werewolf.boots", 0, 25));
        itemList.add(new ItemList(16, Material.LEATHER_BOOTS, Color.fromRGB(117,147,147), "§6§lHARPIA §7Boots", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("Token", 25), (new PerkItem("XP", 25)))), "customarmor.harpia.boots", 0, 25));
        itemList.add(new ItemList(17, Material.LEATHER_BOOTS, Color.fromRGB(41,112,163), "§9§lMERMAID §7Boots", "§2§lUNCOMMON", new ArrayList<>(Arrays.asList(new PerkItem("XP", 25), (new PerkItem("Keys", 25)))), "customarmor.mermaid.boots", 0, 25));
        itemList.add(new ItemList(18, Material.LEATHER_BOOTS, Color.fromRGB(172,65,191), "§f§lANGEL §7Boots", "§b§lRARE", new ArrayList<>(Arrays.asList(new PerkItem("Money", 25), (new PerkItem("Token", 25)), (new PerkItem("XP", 25)))), "customarmor.angel.boots", 0, 10));
        itemList.add(new ItemList(19, Material.LEATHER_BOOTS, Color.fromRGB(221,108,179), "§c§lPHOENIX §7Boots", "§b§lRARE", new ArrayList<>(Arrays.asList(new PerkItem("Money", 25), (new PerkItem("Token", 25)), (new PerkItem("Keys", 25)))), "customarmor.phoenix.boots", 0, 10));
        itemList.add(new ItemList(20, Material.LEATHER_BOOTS, Color.AQUA, "§b§lBEACON §7Boots", "§d§lEPIC", new ArrayList<>(Collections.singletonList((new PerkItem("Beacon", 5)))), "customarmor.beacon.boots", 0, 5));
        itemList.add(new ItemList(21, Material.LEATHER_BOOTS, Color.BLACK, "§c§lA§6§lL§e§lP§a§lH§b§lA §7Boots", "§6§lLEGENDARY", new ArrayList<>(Arrays.asList(new PerkItem("Money", 25), (new PerkItem("Token", 25)), (new PerkItem("XP", 25)), (new PerkItem("Keys", 25)))), "customarmor.alpha.boots", 0, 0));
        armorList.put("Boots", itemList);
    }
}
