package it.masterzen.CustomArmor;

import org.bukkit.Material;
import org.bukkit.entity.ArmorStand;
import org.bukkit.inventory.ItemStack;

public class ArmorManager {

    private int helmet;
    private int chestplate;
    private int leggings;
    private int boots;

    public ArmorManager() {
        helmet = 0;
        chestplate = 0;
        leggings = 0;
        boots = 0;
    }

    public ArmorManager(int helmet, int chestplate, int leggings, int boots) {
        this.helmet = helmet;
        this.chestplate = chestplate;
        this.leggings = leggings;
        this.boots = boots;
    }

    public void setPiece(String piece, int id) {
        if (piece.equalsIgnoreCase("Helmet")) {
            this.helmet = id;
        } else if (piece.equalsIgnoreCase("Chestplate")) {
            this.chestplate = id;
        } else if (piece.equalsIgnoreCase("Leggings")) {
            this.leggings = id;
        } else if (piece.equalsIgnoreCase("Boots")) {
            this.boots = id;
        }
    }

    public void setHelmet(int id) {
        this.helmet = id;
    }

    public void setChestplate(int id) {
        this.chestplate = id;
    }

    public void setLeggings(int id) {
        this.leggings = id;
    }

    public void setBoots(int id) {
        this.boots = id;
    }

    public int getHelmet() {
        return this.helmet;
    }

    public int getChestplate() {
        return this.chestplate;
    }

    public int getLeggings() {
        return this.leggings;
    }

    public int getBoots() {
        return this.boots;
    }

    public int getFromPiece(String piece) {
        if (piece.equalsIgnoreCase("Helmet")) {
            return this.helmet;
        } else if (piece.equalsIgnoreCase("ChestPlate")) {
            return this.chestplate;
        } else if (piece.equalsIgnoreCase("Leggings")) {
            return this.leggings;
        } else if (piece.equalsIgnoreCase("Boots")) {
            return this.boots;
        } else {
            return -1;
        }
    }
}
