package it.masterzen.CustomArmor;

import org.bukkit.Color;
import org.bukkit.Material;

import java.util.List;

public class ItemList {

    private int id;
    private Material material;
    private Color leatherColor;
    private String name;
    private String rarity;
    private List<PerkItem> perks;
    private String pex;
    private int price;
    private double chanceForCrate;

    public ItemList(int id, Material material, Color leatherColor, String name, String rarity, List<PerkItem> perks, String pex, int price, double chanceForCrate) {
        this.id = id;
        this.material = material;
        this.leatherColor = leatherColor;
        this.name = name;
        this.rarity = rarity;
        this.perks = perks;
        this.pex = pex;
        this.price = price;
        this.chanceForCrate = chanceForCrate;
    }

    public void setId(int id) {
        this.id = id;
    }

    public void setMaterial(Material material) {
        this.material = material;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setRarity(String rarity) {
        this.rarity = rarity;
    }

    public void setPerks(List<PerkItem> perks) {
        this.perks = perks;
    }

    public void setPex(String pex) {
        this.pex = pex;
    }

    public void setPrice(int price) {
        this.price = price;
    }

    public void setChanceForCrate(double chanceForCrate) {
        this.chanceForCrate = chanceForCrate;
    }

    public void setLeatherColor(Color leatherColor) {
        this.leatherColor = leatherColor;
    }

    public int getId() {
        return id;
    }

    public Material getMaterial() {
        return material;
    }

    public String getName() {
        return name;
    }

    public String getRarity() {
        return rarity;
    }

    public List<PerkItem> getPerks() {
        return perks;
    }

    public String getPex() {
        return pex;
    }

    public int getPrice() {
        return price;
    }

    public double getChanceForCrate() {
        return chanceForCrate;
    }

    public Color getLeatherColor() {
        return leatherColor;
    }
}
