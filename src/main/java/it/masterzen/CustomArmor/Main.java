package it.masterzen.CustomArmor;

import it.masterzen.Robot.Robot;
import it.masterzen.blockbreak.AlphaBlockBreak;
import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.Listener;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

public class Main implements CommandExecutor, Listener {

    public final AlphaBlockBreak mainClass;
    private final String prefix = "§e§lARMOR §8»§7 ";
    private final String name = "ArmorPoints";
    private static HashMap<UUID, ArmorManager> playerCustomArmor = new HashMap<>();
    private GUI gui;
    private static YamlConfiguration ymlFile;

    private ArmorList armorList;

    public Main(AlphaBlockBreak plugin) {
        mainClass = plugin;
        gui = new GUI(this);
        armorList = new ArmorList();
    }

    public GUI getGui() {
        return gui;
    }

    public HashMap<UUID, ArmorManager> getPlayerCustomArmor() {
        return playerCustomArmor;
    }

    public ArmorList getArmorList() {
        return armorList;
    }

    public void loadPlayerArmor() {
        for (Player player : Bukkit.getOnlinePlayers()) {
            loadPlayerArmor(player);
        }
    }

    public void loadPlayerArmor(Player player) {
        File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/PlayerArmor.yml");
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (Exception ex) {
            }
        }

        ymlFile = YamlConfiguration.loadConfiguration(file);
        if (!playerCustomArmor.containsKey(player.getUniqueId())) {
            playerCustomArmor.put(player.getUniqueId(), new ArmorManager(ymlFile.getInt(player.getUniqueId() + ".helmet"), ymlFile.getInt(player.getUniqueId() + ".chestplate"), ymlFile.getInt(player.getUniqueId() + ".leggings"), ymlFile.getInt(player.getUniqueId() + ".boots")));
        }
    }

    public void savePlayerArmor() throws IOException {
        File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/PlayerArmor.yml");
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (Exception ex) {
            }
        }

        ymlFile = YamlConfiguration.loadConfiguration(file);
        if (!playerCustomArmor.isEmpty() && playerCustomArmor.size() > 0) {
            for (UUID player : playerCustomArmor.keySet()) {
                savePlayerArmor(player, false);
            }
        }
    }

    public void savePlayerArmor(UUID player, boolean remove) throws IOException {
        if (!playerCustomArmor.isEmpty() && playerCustomArmor.containsKey(player)) {
            File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/PlayerArmor.yml");
            ymlFile = YamlConfiguration.loadConfiguration(file);
            ymlFile.set(player.toString() + ".helmet", playerCustomArmor.get(player).getHelmet());
            ymlFile.set(player.toString() + ".chestplate", playerCustomArmor.get(player).getChestplate());
            ymlFile.set(player.toString() + ".leggings", playerCustomArmor.get(player).getLeggings());
            ymlFile.set(player.toString() + ".boots", playerCustomArmor.get(player).getBoots());
            ymlFile.save(file);
            if (remove) {
                playerCustomArmor.remove(player);
            }
        }
    }

    public List<String> getDefaultPerkList() {
        List<String> defaultPerks = new ArrayList<>();
        defaultPerks.add("Money");
        defaultPerks.add("Token");
        defaultPerks.add("XP");
        defaultPerks.add("Keys");

        return defaultPerks;
    }

    public List<PerkItem> getPlayerPerks(Player player) {
        List<PerkItem> perks = new ArrayList<>();
        List<PerkItem> tmpPerks = new ArrayList<>();
        List<String> defaultPerks = getDefaultPerkList();
        int piecePrestige;

        if (mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()) != null &&
                mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()).getHelmet() > 0) {
            piecePrestige = 0;
            tmpPerks.addAll(mainClass.getArmorSystem().getArmorList().getItemList("Helmet").get(mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()).getHelmet() - 1).getPerks());
            for (int i = 1; i <= 5; i++) {
                if (player.hasPermission(mainClass.getArmorSystem().getArmorList().getItemList("Helmet").get(mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()).getHelmet() - 1).getPex() + ".prestige." + i)) {
                    piecePrestige++;
                }
            }
            //player.sendMessage("Helmet pex: " + mainClass.getArmorSystem().getArmorList().getItemList("Helmet").get(mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()).getHelmet() - 1).getPex());
            for (PerkItem perk : tmpPerks) {
                if (!mainClass.getArmorSystem().getArmorList().getItemList("Helmet").get(mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()).getHelmet() - 1).getPex().contains("beacon")) {
                    perks.add(new PerkItem(perk.getType(), (perk.getBoost() + (piecePrestige * 5))));
                    defaultPerks.remove(perk.getType());
                } else {
                    perks.add(new PerkItem(perk.getType(), (perk.getBoost() + piecePrestige)));
                }
            }
            if (!mainClass.getArmorSystem().getArmorList().getItemList("Helmet").get(mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()).getHelmet() - 1).getPex().contains("beacon")) {
                for (String perkMissed : defaultPerks) {
                    perks.add(new PerkItem(perkMissed, piecePrestige * 5));
                }
            }

            //for (PerkItem tmp : perks) {
            //    player.sendMessage(tmp.getType() + " boost: " + tmp.getBoost());
            //}

            //perks.addAll(tmpPerks);
        }
        if (mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()) != null &&
                mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()).getChestplate() > 0) {
            piecePrestige = 0;
            tmpPerks.clear();
            defaultPerks = getDefaultPerkList();
            tmpPerks.addAll(mainClass.getArmorSystem().getArmorList().getItemList("Chestplate").get(mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()).getChestplate() - 1).getPerks());
            for (int i = 1; i <= 5; i++) {
                if (player.hasPermission(mainClass.getArmorSystem().getArmorList().getItemList("Chestplate").get(mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()).getChestplate() - 1).getPex() + ".prestige." + i)) {
                    piecePrestige++;
                }
            }
            for (PerkItem perk : tmpPerks) {
                if (!mainClass.getArmorSystem().getArmorList().getItemList("Chestplate").get(mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()).getChestplate() - 1).getPex().contains("beacon")) {
                    perks.add(new PerkItem(perk.getType(), (perk.getBoost() + (piecePrestige * 5))));
                    defaultPerks.remove(perk.getType());
                } else {
                    perks.add(new PerkItem(perk.getType(), (perk.getBoost() + piecePrestige)));
                }
            }
            if (!mainClass.getArmorSystem().getArmorList().getItemList("Chestplate").get(mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()).getChestplate() - 1).getPex().contains("beacon")) {
                for (String perkMissed : defaultPerks) {
                    perks.add(new PerkItem(perkMissed, piecePrestige * 5));
                }
            }
            //perks.addAll(tmpPerks);
            //perks.addAll(mainClass.getArmorSystem().getArmorList().getItemList("Chestplate").get(mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()).getChestplate() - 1).getPerks());
        }
        if (mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()) != null &&
                mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()).getLeggings() > 0) {
            piecePrestige = 0;
            tmpPerks.clear();
            defaultPerks = getDefaultPerkList();
            //tmpPerks = new ArrayList<>(mainClass.getArmorSystem().getArmorList().getItemList("Leggings").get(mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()).getLeggings() - 1).getPerks());
            tmpPerks.addAll(mainClass.getArmorSystem().getArmorList().getItemList("Leggings").get(mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()).getLeggings() - 1).getPerks());
            /*for (PerkItem tmp : tmpPerks) {
                player.sendMessage(tmp.getType() + " boost: " + tmp.getBoost());
            }*/
            for (int i = 1; i <= 5; i++) {
                if (player.hasPermission(mainClass.getArmorSystem().getArmorList().getItemList("Leggings").get(mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()).getLeggings() - 1).getPex() + ".prestige." + i)) {
                    piecePrestige++;
                }
            }
            for (PerkItem perk : tmpPerks) {
                if (!mainClass.getArmorSystem().getArmorList().getItemList("Leggings").get(mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()).getLeggings() - 1).getPex().contains("beacon")) {
                    //perk.addBoost(piecePrestige * 5);
                    perks.add(new PerkItem(perk.getType(), (perk.getBoost() + (piecePrestige * 5))));
                    //player.sendMessage(perk.getType() + " boost: " + (perk.getBoost() + (piecePrestige * 5)));
                    defaultPerks.remove(perk.getType());
                } else {
                    //perk.addBoost(piecePrestige);
                    perks.add(new PerkItem(perk.getType(), (perk.getBoost() + piecePrestige)));
                }
            }
            if (!mainClass.getArmorSystem().getArmorList().getItemList("Leggings").get(mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()).getLeggings() - 1).getPex().contains("beacon")) {
                for (String perkMissed : defaultPerks) {
                    perks.add(new PerkItem(perkMissed, piecePrestige * 5));
                }
            }

            //perks.addAll(tmpPerks);
            //perks.addAll(mainClass.getArmorSystem().getArmorList().getItemList("Leggings").get(mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()).getLeggings() - 1).getPerks());
        }
        if (mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()) != null &&
                mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()).getBoots() > 0) {
            piecePrestige = 0;
            tmpPerks.clear();
            defaultPerks = getDefaultPerkList();
            tmpPerks.addAll(mainClass.getArmorSystem().getArmorList().getItemList("Boots").get(mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()).getBoots() - 1).getPerks());
            for (int i = 1; i <= 5; i++) {
                if (player.hasPermission(mainClass.getArmorSystem().getArmorList().getItemList("Boots").get(mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()).getBoots() - 1).getPex() + ".prestige." + i)) {
                    piecePrestige++;
                }
            }
            for (PerkItem perk : tmpPerks) {
                if (!mainClass.getArmorSystem().getArmorList().getItemList("Boots").get(mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()).getBoots() - 1).getPex().contains("beacon")) {
                    perks.add(new PerkItem(perk.getType(), (perk.getBoost() + (piecePrestige * 5))));
                    defaultPerks.remove(perk.getType());
                } else {
                    perks.add(new PerkItem(perk.getType(), (perk.getBoost() + piecePrestige)));
                }
            }
            if (!mainClass.getArmorSystem().getArmorList().getItemList("Boots").get(mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()).getBoots() - 1).getPex().contains("beacon")) {
                for (String perkMissed : defaultPerks) {
                    perks.add(new PerkItem(perkMissed, piecePrestige * 5));
                }
            }
            //perks.addAll(tmpPerks);
            //perks.addAll(mainClass.getArmorSystem().getArmorList().getItemList("Boots").get(mainClass.getArmorSystem().getPlayerCustomArmor().get(player.getUniqueId()).getBoots() - 1).getPerks());
        }

        return perks;
    }

    public ItemList getArmorFromPex(String pex) {
        List<ItemList> customArmors = getCustomArmor();
        customArmors.addAll(getAlphaArmor());
        ItemList armor = null;

        for (ItemList tmpArmor : customArmors) {
            if (tmpArmor.getPex().equalsIgnoreCase(pex)) {
                armor = tmpArmor;
            }
        }

        return armor;
    }

    public List<ItemList> getAlphaArmor() {
        List<ItemList> customArmor = new ArrayList<>();
        List<ItemList> tmpList;

        tmpList = mainClass.getArmorSystem().getArmorList().getItemList("Helmet");
        for (ItemList armor : tmpList) {
            if (armor.getPex().contains("alpha")) {
                customArmor.add(armor);
            }
        }

        tmpList = mainClass.getArmorSystem().getArmorList().getItemList("Chestplate");
        for (ItemList armor : tmpList) {
            if (armor.getPex().contains("alpha")) {
                customArmor.add(armor);
            }
        }

        tmpList = mainClass.getArmorSystem().getArmorList().getItemList("Leggings");
        for (ItemList armor : tmpList) {
            if (armor.getPex().contains("alpha")) {
                customArmor.add(armor);
            }
        }

        tmpList = mainClass.getArmorSystem().getArmorList().getItemList("Boots");
        for (ItemList armor : tmpList) {
            if (armor.getPex().contains("alpha")) {
                customArmor.add(armor);
            }
        }

        return customArmor;
    }

    public List<ItemList> getCustomArmor() {
        List<ItemList> customArmor = new ArrayList<>();
        List<ItemList> tmpList;

        tmpList = mainClass.getArmorSystem().getArmorList().getItemList("Helmet");
        for (ItemList armor : tmpList) {
            if (armor.getChanceForCrate() > 0) {
                customArmor.add(armor);
            }
        }

        tmpList = mainClass.getArmorSystem().getArmorList().getItemList("Chestplate");
        for (ItemList armor : tmpList) {
            if (armor.getChanceForCrate() > 0) {
                customArmor.add(armor);
            }
        }

        tmpList = mainClass.getArmorSystem().getArmorList().getItemList("Leggings");
        for (ItemList armor : tmpList) {
            if (armor.getChanceForCrate() > 0) {
                customArmor.add(armor);
            }
        }

        tmpList = mainClass.getArmorSystem().getArmorList().getItemList("Boots");
        for (ItemList armor : tmpList) {
            if (armor.getChanceForCrate() > 0) {
                customArmor.add(armor);
            }
        }

        return customArmor;
    }

    public boolean armorExists(String piece, String name) {
        boolean found = false;

        List<ItemList> armorList = mainClass.getArmorSystem().getArmorList().getItemList(piece);
        for (ItemList armor : armorList) {
            if (armor.getPex().equalsIgnoreCase("customarmor." + name + "." + piece)) {
                found = true;
                break;
            }
        }

        return found;
    }

    public void giveRandomAlphaArmor(Player player) {
        int chance = ThreadLocalRandom.current().nextInt(4);
        String pex = "";

        if (chance == 0) {
            pex = "customarmor.alpha.helmet";
        } else if (chance == 1) {
            pex = "customarmor.alpha.chestplate";
        } else if (chance == 2) {
            pex = "customarmor.alpha.leggings";
        } else {
            pex = "customarmor.alpha.boots";
        }

        if (player.hasPermission(pex)) {
            mainClass.getArmorPointsSystem().addPoints(player, 250);
            player.sendMessage(getArmorFromPex(pex).getName() + " §7Duplicate. You received 250 ArmorPoints");
        } else {
            mainClass.addPex(player, pex, 0, false);
            player.sendMessage(prefix + "You received " + getArmorFromPex(pex).getName());
        }
    }

    public void giveArmor(Player player, String piece, String name) {
        ItemList armorToAdd = null;
        List<ItemList> armorList = mainClass.getArmorSystem().getArmorList().getItemList(piece);
        for (ItemList armor : armorList) {
            if (armor.getPex().equalsIgnoreCase("customarmor." + name + "." + piece)) {
                armorToAdd = armor;
            }
        }

        if (armorToAdd != null) {
            mainClass.addPex(player, armorToAdd.getPex(), 0, false);
            player.sendMessage(prefix + "You received " + armorToAdd.getName());
        }
    }

    @Override
    public boolean onCommand(CommandSender sender, Command cmd, String label, String[] args) {
        if (cmd.getName().equalsIgnoreCase("armor")) {
            if (sender instanceof Player) {
                Player player = (Player) sender;
                if (!playerCustomArmor.containsKey(player.getUniqueId())) {
                    playerCustomArmor.put(player.getUniqueId(), new ArmorManager());
                }
                if (player.isOp() && args.length > 0 && args[0].equalsIgnoreCase("givepiece")) {
                    if (args.length == 4) {
                        Player tmpPlayer = Bukkit.getPlayerExact(args[1]);
                        if (armorExists(args[2], args[3])) {
                            giveArmor(player, args[2], args[3]);
                            player.sendMessage(prefix + "§aSuccesfully added armor to " + tmpPlayer.getName());
                        } else {
                            player.sendMessage(prefix + "§cThis armor did not exists");
                        }
                    } else if (args.length == 3 && args[2].equalsIgnoreCase("alpha")) {
                        Player tmpPlayer = Bukkit.getPlayerExact(args[1]);
                        giveRandomAlphaArmor(tmpPlayer);
                    }
                } else if (player.isOp() && args.length == 3 && args[0].equalsIgnoreCase("give")) {
                    if (Bukkit.getPlayerExact(args[1]) != null) {
                        Player tmpPlayer = Bukkit.getPlayerExact(args[1]);
                        long points = Long.parseLong(args[2]);
                        mainClass.getArmorPointsSystem().addPoints(tmpPlayer, points);
                        player.sendMessage(prefix + "§a§l" + points + " §7" + name + " §7Added to " + tmpPlayer.getName() + "'s balance §7§o(" + mainClass.getArmorPointsSystem().getPoints(tmpPlayer) + ")");
                        if (!player.getName().equals(tmpPlayer.getName())) {
                            tmpPlayer.sendMessage(prefix + "You received §a§l" + points + " §7" + name);
                        }
                    } else {
                        player.sendMessage(prefix + "§cPlayer offline");
                    }
                } else if (player.isOp() && args.length == 2 && args[0].equalsIgnoreCase("reset")) {
                    Player tmpPlayer = Bukkit.getPlayerExact(args[1]);
                    mainClass.getArmorPointsSystem().removePoints(tmpPlayer, mainClass.getArmorPointsSystem().getPoints(tmpPlayer));
                    tmpPlayer.sendMessage(prefix + "Your point balance has been resetted");
                    player.sendMessage(prefix + "You succesfully reset " + tmpPlayer.getName() + "'s point balance");
                } else if (args.length == 1 && (args[0].equalsIgnoreCase("bal") || args[0].equalsIgnoreCase("balance"))) {
                    mainClass.getArmorPointsSystem().sendPoints(player);
                } else if (args.length == 1) {
                    if (Bukkit.getPlayer(args[0]) != null && !mainClass.getEssentials().getUser(Bukkit.getPlayer(args[0])).isVanished()) {
                        gui.viewAmor(player, Bukkit.getPlayer(args[0]));
                    } else {
                        player.sendMessage(prefix + "§cPlayer Offline");
                    }
                } else {
                    gui.openGUI(player);
                }
            } else {
                if (args.length == 3 && args[2].equalsIgnoreCase("alpha")) {
                    Player tmpPlayer = Bukkit.getPlayerExact(args[1]);
                    giveRandomAlphaArmor(tmpPlayer);
                } else if (args.length == 3 && args[0].equalsIgnoreCase("give")) {
                    if (Bukkit.getPlayerExact(args[1]) != null) {
                        Player tmpPlayer = Bukkit.getPlayerExact(args[1]);
                        long points = Long.parseLong(args[2]);
                        mainClass.getArmorPointsSystem().addPoints(tmpPlayer, points);
                        mainClass.getLogger().info(prefix + "§a§l" + points + " §7" + name + " §7Added to " + tmpPlayer.getName() + "'s balance §7§o(" + mainClass.getArmorPointsSystem().getPoints(tmpPlayer) + ")");
                        tmpPlayer.sendMessage(prefix + "You received §a§l" + points + " §7" + name);
                    }
                } else if (args.length == 2 && args[0].equalsIgnoreCase("reset")) {
                    Player tmpPlayer = Bukkit.getPlayerExact(args[1]);
                    mainClass.getArmorPointsSystem().removePoints(tmpPlayer, mainClass.getArmorPointsSystem().getPoints(tmpPlayer));
                    tmpPlayer.sendMessage(prefix + "Your point balance has been resetted");
                    mainClass.getLogger().info(prefix + "You succesfully reset " + tmpPlayer.getName() + "'s point balance");
                }
            }
        }
        return false;
    }
}
