package it.masterzen.AntiCheat;

import it.masterzen.blockbreak.AlphaBlockBreak;
import me.clip.placeholderapi.PlaceholderAPI;
import net.md_5.bungee.api.chat.BaseComponent;
import net.md_5.bungee.api.chat.ComponentBuilder;
import net.md_5.bungee.api.chat.HoverEvent;
import net.md_5.bungee.api.chat.TextComponent;
import org.apache.commons.lang.StringUtils;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

public class Main implements Listener {
    public final AlphaBlockBreak mainClass;
    private final String prefix = "§e§lANTICHEAT §8»§7 ";
    private HashMap<UUID, CheatReport> playerReport = new HashMap<>();
    private HashMap<UUID, UUID> reportSendedToPlayer = new HashMap<>();

    public Main(AlphaBlockBreak plugin) {
        mainClass = plugin;
    }

    @EventHandler
    public void blockbreak(BlockBreakEvent event) {
        event.setDropItems(false);
        Player player = event.getPlayer();

        Block blockBroken = event.getBlock();
        Block target = player.getTargetBlock(null, 7);
        if (!playerReport.containsKey(player.getUniqueId())) {
            playerReport.put(player.getUniqueId(), new CheatReport(0, 1));
        }
        if (!player.hasPermission("anticheat.whitelisted") && (target == null || target.getType().equals(Material.AIR) || target.getLocation().distance(blockBroken.getLocation()) > 3)/*|| !blockBroken.getType().equals(target.getType())*/) {
            playerReport.get(player.getUniqueId()).addReport(1);
            if (playerReport.get(player.getUniqueId()).getTimeReported() > 100) {
                for (Player tmpPlayer : Bukkit.getOnlinePlayers()) {
                    if (tmpPlayer.isOp() || tmpPlayer.hasPermission("anticheat.report")) {
                    // if (tmpPlayer.getUniqueId().toString().equals("d977559d-e8a2-45e2-a01a-f4ce37276ad9")) {
                        if (!reportSendedToPlayer.containsKey(tmpPlayer.getUniqueId()) /*|| !reportSendedToPlayer.get(tmpPlayer.getUniqueId()).equals(player.getUniqueId())*/) {
                            tmpPlayer.sendMessage(prefix + player.getName() + " §cNuking§7 Report (Reported " + playerReport.get(player.getUniqueId()).getTimeReported() + " times)");
                            reportSendedToPlayer.put(tmpPlayer.getUniqueId(), player.getUniqueId());

                            new BukkitRunnable() {
                                @Override
                                public void run() {
                                    reportSendedToPlayer.remove(tmpPlayer.getUniqueId());
                                }
                            }.runTaskLater(mainClass, 150L);
                        }
                    }
                }
            }

            String playerWorld = player.getWorld().getName();
            if (playerWorld.equalsIgnoreCase("Build") || playerWorld.equals("SkyFarm")) {
                for (Player tmpPlayer : Bukkit.getOnlinePlayers()) {
                    if (tmpPlayer.hasPermission("anticheat.report")) {
                        if (!reportSendedToPlayer.containsKey(tmpPlayer.getUniqueId()) /*|| !reportSendedToPlayer.get(tmpPlayer.getUniqueId()).equals(player.getUniqueId())*/) {
                            if (StringUtils.equalsIgnoreCase(playerWorld, "Build")) {
                                tmpPlayer.sendMessage(prefix + player.getName() + " §cDungeon Nuking§7 Report (Reported " + playerReport.get(player.getUniqueId()).getTimeReported() + " times)");
                            } else {
                                tmpPlayer.sendMessage(prefix + player.getName() + " §cFarmer Nuking§7 Report (Reported " + playerReport.get(player.getUniqueId()).getTimeReported() + " times)");
                            }
                            reportSendedToPlayer.put(tmpPlayer.getUniqueId(), player.getUniqueId());

                            new BukkitRunnable() {
                                @Override
                                public void run() {
                                    reportSendedToPlayer.remove(tmpPlayer.getUniqueId());
                                }
                            }.runTaskLater(mainClass, 150L);
                        }
                    }
                }
            }
        }
        playerReport.get(player.getUniqueId()).addBlockMined(1);
    }

    public void sendReports() {
        for (Player player : Bukkit.getOnlinePlayers()) {
            if (player.isOp() || player.hasPermission("anticheat.report")) {
                sendReports(player);
            }
        }
        /*List<String> finalMessage = new ArrayList<>();
        for (UUID player : playerReport.keySet()) {
            long reportAmount = playerReport.get(player).getTimeReported();

            if (reportAmount > 9) {
                finalMessage.add("§7" + Bukkit.getOfflinePlayer(player).getName() + " reported " + getColorCodeFromReports(reportAmount) + reportAmount + " §7times for Nuker §7§o(" + playerReport.get(player).getBlockMined() + ")");
            }
        }

        for (Player player : Bukkit.getOnlinePlayers()) {
            if (player.isOp() || player.hasPermission("anticheat.report")) {
                player.sendMessage("");
                player.sendMessage("§e§lANTICHEAT §7Report");
                if (finalMessage.isEmpty()) {
                    player.sendMessage("§aNo one is cheating");
                } else {
                    for (String report : finalMessage) {
                        player.sendMessage(report);
                    }
                }
                player.sendMessage("");
            }
        }*/
    }

    public void sendReports(Player playerWhoRequestReports) {
        List<String> finalMessage = new ArrayList<>();
        boolean anyCheater = false;
        HashMap<String, Report> reports = new HashMap<>();
        reports.put("§a§l", new Report("§a§lLOW CHANCE", "", 0));
        reports.put("§6§l", new Report("§6§lMEDIUM CHANCE","", 0));
        reports.put("§c§l", new Report("§c§lHIGH CHANCE","", 0));
        reports.put("§4§l", new Report("§4§lCHEATERS","", 0));

        for (UUID player : playerReport.keySet()) {
            long reportAmount = playerReport.get(player).getTimeReported();
            long blocksMined = playerReport.get(player).getBlockMined();

            if (reportAmount > 0 || blocksMined > 1300) {
                reports.get(getColorCodeFromReports(reportAmount)).addMessage("§7" + Bukkit.getOfflinePlayer(player).getName() + " " + getColorCodeFromReports(reportAmount) + reportAmount + " §7times §7§o(" + blocksMined + ")");
                reports.get(getColorCodeFromReports(reportAmount)).addPlayer(1);
                //reports.replace(getColorCodeFromReports(reportAmount), reports.get(getColorCodeFromReports(reportAmount)), reports.get(getColorCodeFromReports(reportAmount)) + (reports.get(getColorCodeFromReports(reportAmount)).equalsIgnoreCase("") ? "" : "\n") + "§7" + Bukkit.getOfflinePlayer(player).getName() + " " + getColorCodeFromReports(reportAmount) + reportAmount + " §7times §7§o(" + blocksMined + ")");
                anyCheater = true;
                //finalMessage.add("§7" + Bukkit.getOfflinePlayer(player).getName() + " reported " + getColorCodeFromReports(reportAmount) + reportAmount + " §7times for Nuker §7§o(" + blocksMined + ")");
            }
        }

        if (playerWhoRequestReports.isOp() || playerWhoRequestReports.hasPermission("anticheat.report")) {
            playerWhoRequestReports.sendMessage("");
            playerWhoRequestReports.sendMessage("§e§lANTICHEAT §7Report");
            if (!anyCheater) {
                playerWhoRequestReports.sendMessage("§aNo one is cheating");
            } else {
                TextComponent nameFormat = new TextComponent();

                for (String report : reports.keySet()) {
                    nameFormat = new TextComponent();
                    nameFormat.setText(reports.get(report).getPrefix() + " §7§o(" + reports.get(report).getPlayerAmount() + ")");
                    nameFormat.setHoverEvent(new HoverEvent(HoverEvent.Action.SHOW_TEXT, new ComponentBuilder(reports.get(report).getMessagge()).create()));
                    playerWhoRequestReports.spigot().sendMessage(nameFormat);
                }
            }
            //if (finalMessage.isEmpty()) {
            //    playerWhoRequestReports.sendMessage("§aNo one is cheating");
            //} else {
            //    for (String report : finalMessage) {
            //        playerWhoRequestReports.sendMessage(report);
            //    }
            //}
            playerWhoRequestReports.sendMessage("");
        }
    }

    public void resetReports() {
        playerReport.clear();
    }

    public String getColorCodeFromReports(long reports) {
        String color = "";

        if (reports < 20) {
            color = "§a§l";
        } else if (reports < 50) {
            color = "§6§l";
        } else if (reports < 100) {
            color = "§c§l";
        } else {
            color = "§4§l";
        }

        return color;
    }

    public void removeBlocksMined(Player player, int amount) {
        if (playerReport.containsKey(player.getUniqueId())) {
            playerReport.get(player.getUniqueId()).removeBlockMined(amount);
        }
    }
}
