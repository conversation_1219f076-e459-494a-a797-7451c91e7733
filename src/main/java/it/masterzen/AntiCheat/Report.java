package it.masterzen.AntiCheat;

public class Report {

    private String prefix;
    private String messagge;
    private int playerAmount;

    public Report(String prefix, String messagge, int playerAmount) {
        this.prefix = prefix;
        this.messagge = messagge;
        this.playerAmount = playerAmount;
    }

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public String getMessagge() {
        return messagge;
    }

    public void setMessagge(String messagge) {
        this.messagge = messagge;
    }

    public void addMessage(String messagge) {
        this.messagge = this.messagge + (this.messagge.equalsIgnoreCase("") ? "" : "\n") + messagge;
    }

    public int getPlayerAmount() {
        return playerAmount;
    }

    public void setPlayerAmount(int playerAmount) {
        this.playerAmount = playerAmount;
    }

    public void addPlayer(int amount) {
        this.playerAmount = this.playerAmount + amount;
    }
}
