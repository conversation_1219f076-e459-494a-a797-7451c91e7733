package it.masterzen.AntiCheat;

public class CheatReport {

    private long timeReported;
    private long blockMined;

    public CheatReport(long timeReported, long blockMined) {
        this.timeReported = timeReported;
        this.blockMined = blockMined;
    }

    public long getTimeReported() {
        return timeReported;
    }

    public void setTimeReported(long timeReported) {
        this.timeReported = timeReported;
    }

    public long getBlockMined() {
        return blockMined;
    }

    public void setBlockMined(long blockMined) {
        this.blockMined = blockMined;
    }

    public void addReport(int amount) {
        this.timeReported = this.timeReported + amount;
    }

    public void addBlockMined(int amount) {
        this.blockMined = this.blockMined + amount;
    }

    public void removeReport(int amount) {
        this.timeReported = this.timeReported - amount;
    }

    public void removeBlockMined(int amount) {
        this.blockMined = this.blockMined - amount;
    }
}
