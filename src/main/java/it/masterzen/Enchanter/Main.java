package it.masterzen.Enchanter;

import it.masterzen.blockbreak.AlphaBlockBreak;
import org.apache.commons.lang.StringUtils;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.event.Listener;

public class Main implements CommandExecutor, Listener {

    private final String prefix = "§e§lENCHANTER §8»§7 ";
    public final AlphaBlockBreak mainClass;
    private GUI gui;

    public Main(AlphaBlockBreak plugin) {
        mainClass = plugin;
        gui = new GUI(this);
    }

    @Override
    public boolean onCommand(CommandSender sender, Command cmd, String label, String[] args) {
        if (cmd.getName().equalsIgnoreCase("enchanter")) {
            if (sender instanceof Player) {
                Player player = (Player) sender;
                if (!StringUtils.equals(mainClass.getPlayerVip(player), "none") || player.isOp()) {
                    gui.openGUI(player);
                } else {
                    player.sendMessage(prefix + "§cYou must have a vip to use this feature !");
                    player.sendMessage(prefix + "§7Buy your permanent vip at /buy");
                }
            }
        }

        return false;
    }
}
