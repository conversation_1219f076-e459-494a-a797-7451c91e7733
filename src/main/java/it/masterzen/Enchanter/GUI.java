package it.masterzen.Enchanter;

import com.sk89q.worldedit.MaxChangedBlocksException;
import it.masterzen.MongoDB.PlayerData;
import it.masterzen.blockbreak.EnchantList;
import org.apache.commons.lang3.time.DateUtils;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Item;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class GUI implements Listener {

    private Main main;
    private final String prefix = "§e§lENCHANTER §8»§7 ";

    public GUI(Main main) {
        this.main = main;
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) throws IOException, MaxChangedBlocksException {
        if (event.getClickedInventory() != null && event.getClickedInventory().equals(event.getView().getTopInventory())) {
            Player player = (Player) event.getWhoClicked();

            if (event.getView().getTitle().equalsIgnoreCase("§e§lENCHANTER §f| §7Menu")) {
                event.setCancelled(true);
                ItemStack clickedItem = event.getCurrentItem();
                PlayerData playerData = main.mainClass.getMongoReader().getPlayerData(player.getUniqueId());
                List<String> choosedEnchants = playerData.getEnchanterEnchantList();
                if (choosedEnchants == null) {
                    choosedEnchants = new ArrayList<>();
                }

                if (choosedEnchants.size() < 3) { // still choosing the enchants
                    if (clickedItem != null && !clickedItem.getType().equals(Material.AIR)) {
                        if (event.isLeftClick()) {
                            if (clickedItem.hasItemMeta() && clickedItem.getItemMeta().hasDisplayName()) {
                                String enchantName = main.mainClass.getBookManager().getEnchantName(clickedItem.getItemMeta().getDisplayName());
                                if (!choosedEnchants.contains(enchantName)) {
                                    if ((playerData.getPickaxeCurrentTier() == null ? 0 : playerData.getPickaxeCurrentTier()) + 1 >= main.mainClass.getEnchantManager().getEnchantList().get(enchantName).getColor() - 1) {
                                        choosedEnchants.add(enchantName);
                                        player.sendMessage(prefix + "§a" + enchantName + " has been added to the choosed enchants");
                                        playerData.setEnchanterEnchantList(choosedEnchants);
                                        openGUI(player);
                                    } else {
                                        player.sendMessage(prefix + "§cYou can only select enchants that has tier lower then §l" + ((playerData.getPickaxeCurrentTier() == null ? 0 : playerData.getPickaxeCurrentTier()) + 1));
                                    }
                                } else {
                                    player.sendMessage(prefix + "§cEnchant already selected");
                                }
                            }
                        } else {
                            player.sendMessage(prefix + "§cUse left click to choose an enchant");
                        }
                    }
                } else { // collection gui
                    if (event.getSlot() == 31) {
                        Date lastClaimDate = playerData.getEnchanterLatestClaimDate() == null ? DateUtils.addDays(new Date(), -2) : playerData.getEnchanterLatestClaimDate();
                        if (new Date().after(DateUtils.addDays(lastClaimDate, 1))) {
                            addEnchantLevels(player);
                        } else {
                            player.sendMessage(prefix + "§cYou have to wait for levels to be generated !");
                        }
                    } else if (event.getSlot() == 33) {
                        Date lastChangeDate = playerData.getEnchanterLatestChangeDate();
                        if (lastChangeDate == null || new Date().after(DateUtils.addDays(lastChangeDate, 5))) {
                            playerData.setEnchanterEnchantList(new ArrayList<>());
                            playerData.setEnchanterLatestChangeDate(new Date());
                            player.sendMessage(prefix + "§aEnchanter has been resetted");
                        } else {
                            player.sendMessage(prefix + "§cAction in cooldown. Please wait");
                        }
                    }
                }
                openGUI(player);
            }
        }
    }

    public void addEnchantLevels(Player player) {
        PlayerData playerData = main.mainClass.getMongoReader().getPlayerData(player.getUniqueId());
        List<String> choosedEnchants = playerData.getEnchanterEnchantList();
        for (String enchantName : choosedEnchants) {
            long levelsToAdd = getEnchantLevelAmount(player, enchantName);
            if (levelsToAdd > 0) {
                if (levelsToAdd > Integer.MAX_VALUE) {
                    int maxIterations = 1000000; // alto altrimenti non bastano per dare tutti i livelli che deve
                    enchantName = main.mainClass.getBookManager().getEnchantName(enchantName);
                    player.sendMessage(prefix + "You received §a§l" + levelsToAdd + " §7level/s of §a§l" + enchantName);
                    while (levelsToAdd > 0 && maxIterations > 0) {
                        if (levelsToAdd >= Integer.MAX_VALUE) {
                            main.mainClass.getEnchantManager().addEnchant(player, enchantName, Integer.MAX_VALUE, false);
                            levelsToAdd = levelsToAdd - Integer.MAX_VALUE;
                        } else {
                            main.mainClass.getEnchantManager().addEnchant(player, enchantName, (int) levelsToAdd, false);
                            levelsToAdd = 0;
                        }
                        maxIterations--;
                    }
                } else {
                    main.mainClass.getEnchantManager().addEnchant(player, enchantName, (int) levelsToAdd, true);
                }
            }
        }

        playerData.setEnchanterLatestClaimDate(new Date());
    }

    public long getEnchantLevelAmount(Player player, String enchantName) {
        EnchantList enchant = main.mainClass.getEnchantManager().getEnchantList().get(enchantName);
        long enchantMaxLevel = enchant.getMaxLevel();
        // NUOVA GESTIONE PER COMBO
        if (enchant.getBlocksNeededToUnlockNewLevels() > 0) {
            PlayerData playerData = main.mainClass.getMongoReader().getPlayerData(player.getUniqueId());
            int stepsReached = playerData.getBlocksMined() / enchant.getBlocksNeededToUnlockNewLevels();
            enchantMaxLevel = enchantMaxLevel + ((long) enchant.getLevelsToUnlock() * stepsReached);
        }
        long currentLevel = main.mainClass.getEnchantManager().getCurrentLevel(player.getInventory().getItemInMainHand().getItemMeta().getLore(), enchantName);
        String playerVip = main.mainClass.getPlayerVip(player);
        long levelToAdd = 0;

        // 1. Classic Limited Enchant
        if (enchantMaxLevel == 1) {
            levelToAdd = 1;
        } else if (!enchant.isInfinite() && enchant.getBlocksNeeded() == 0) {
            if (playerVip.equalsIgnoreCase("Alpha+") || player.isOp()) {
                levelToAdd = Math.round(enchantMaxLevel * 0.35);
            } else if (playerVip.equalsIgnoreCase("Alpha")) {
                levelToAdd = Math.round(enchantMaxLevel * 0.25);
            } else if (playerVip.equalsIgnoreCase("MVP+")) {
                levelToAdd = Math.round(enchantMaxLevel * 0.20);
            } else if (playerVip.equalsIgnoreCase("MVP")) {
                levelToAdd = Math.round(enchantMaxLevel * 0.15);
            } else if (playerVip.equalsIgnoreCase("VIP+")) {
                levelToAdd = Math.round(enchantMaxLevel * 0.10);
            } else if (playerVip.equalsIgnoreCase("VIP")) {
                levelToAdd = Math.round(enchantMaxLevel * 0.05);
            }
        } else if (enchant.isInfinite()) { // 2. Infinite Enchant
            if (playerVip.equalsIgnoreCase("Alpha+") || player.isOp()) {
                levelToAdd = currentLevel / 10;
            } else if (playerVip.equalsIgnoreCase("Alpha")) {
                levelToAdd = currentLevel / 11;
            } else if (playerVip.equalsIgnoreCase("MVP+")) {
                levelToAdd = currentLevel / 12;
            } else if (playerVip.equalsIgnoreCase("MVP")) {
                levelToAdd = currentLevel / 13;
            } else if (playerVip.equalsIgnoreCase("VIP+")) {
                levelToAdd = currentLevel / 14;
            } else if (playerVip.equalsIgnoreCase("VIP")) {
                levelToAdd = currentLevel / 15;
            }
        } else if (enchant.getBlocksNeeded() > 0) { // 3. Enchants that require blocks
            levelToAdd = 1;
        }

        // Per evitare che superino il limite degli enchant limitati
        if (!enchant.isInfinite() && currentLevel + levelToAdd > enchantMaxLevel) {
            levelToAdd = enchantMaxLevel - currentLevel;
        }

        // Sembra che se superi il limite il numero di livelli va negativo. In caso resetto
        if (levelToAdd < 0) {
            levelToAdd = 0;
        }
        return levelToAdd;
    }

    public void openGUI(Player player) {
        ItemStack itemInHand = player.getInventory().getItemInMainHand();
        if (itemInHand != null && itemInHand.getType().equals(Material.DIAMOND_PICKAXE)) {
            Inventory gui = Bukkit.createInventory(null, 54, "§e§lENCHANTER §f| §7Menu");
            it.masterzen.commands.Main.FillBorder(gui);
            List<String> pickaxeLore = player.getInventory().getItemInMainHand().getItemMeta().getLore();
            PlayerData playerData = main.mainClass.getMongoReader().getPlayerData(player.getUniqueId());
            List<String> choosedEnchants = playerData.getEnchanterEnchantList();
            if (choosedEnchants == null || choosedEnchants.size() < 3) {
                Map<String, EnchantList> enchantList = main.mainClass.getEnchantManager().getEnchanterEnchantList();
                int i = 0;
                for (String enchantName : enchantList.keySet()) {
                    ItemStack tmpEnchant = main.mainClass.getEnchantManager().getSlotItemv2(enchantName, pickaxeLore, player, false);
                    ItemMeta meta = tmpEnchant.getItemMeta();
                    List<String> lore = meta.getLore();
                    lore.add("§6§lUSAGE");
                    lore.add("§6| §fClick me to select this enchant");
                    meta.setLore(lore);
                    if (choosedEnchants != null && choosedEnchants.contains(enchantName)) {
                        main.mainClass.addGlowing(meta);
                    }
                    tmpEnchant.setItemMeta(meta);

                    switch (i) {
                        case 0:
                            gui.setItem(10, tmpEnchant);
                        case 1:
                            gui.setItem(11, tmpEnchant);
                        case 2:
                            gui.setItem(15, tmpEnchant);
                        case 3:
                            gui.setItem(16, tmpEnchant);
                        case 4:
                            gui.setItem(19, tmpEnchant);
                        case 5:
                            gui.setItem(20, tmpEnchant);
                        case 6:
                            gui.setItem(21, tmpEnchant);
                        case 7:
                            gui.setItem(23, tmpEnchant);
                        case 8:
                            gui.setItem(24, tmpEnchant);
                        case 9:
                            gui.setItem(25, tmpEnchant);
                        case 10:
                            gui.setItem(28, tmpEnchant);
                        case 11:
                            gui.setItem(29, tmpEnchant);
                        case 12:
                            gui.setItem(30, tmpEnchant);
                        case 13:
                            gui.setItem(31, tmpEnchant);
                        case 14:
                            gui.setItem(32, tmpEnchant);
                        case 15:
                            gui.setItem(33, tmpEnchant);
                        case 16:
                            gui.setItem(34, tmpEnchant);
                        case 17:
                            gui.setItem(38, tmpEnchant);
                        case 18:
                            gui.setItem(39, tmpEnchant);
                        case 19:
                            gui.setItem(40, tmpEnchant);
                        case 20:
                            gui.setItem(41, tmpEnchant);
                        case 21:
                            gui.setItem(42, tmpEnchant);
                    }
                    i++;
                }
            } else {
                it.masterzen.commands.Main.FillBorder(gui);

                int i = 0;
                boolean atLeastOneRemoved = false;
                List<String> updatedEnchantsList = new ArrayList<>(choosedEnchants);
                for (String enchantName : choosedEnchants) {
                    if (!main.mainClass.getEnchantManager().getEnchantList().get(enchantName).isEnchanter()) {
                        updatedEnchantsList.remove(enchantName);
                        atLeastOneRemoved = true;
                    }

                    ItemStack tmpEnchant = main.mainClass.getEnchantManager().getSlotItemv2(enchantName, pickaxeLore, player, false);
                    if (i == 0) {
                        gui.setItem(11, tmpEnchant);
                    } else if (i == 1) {
                        gui.setItem(13, tmpEnchant);
                    } else if (i == 2) {
                        gui.setItem(15, tmpEnchant);
                    }
                    i++;
                }

                if (atLeastOneRemoved) {
                    playerData.setEnchanterEnchantList(updatedEnchantsList);
                    openGUI(player);
                }

                Date lastClaimDate = playerData.getEnchanterLatestClaimDate() == null ? DateUtils.addDays(new Date(), -2) : playerData.getEnchanterLatestClaimDate();
                Date lastChangeDate = playerData.getEnchanterLatestChangeDate();
                ItemStack collectLevels = new ItemStack(Material.HOPPER);
                ItemMeta meta = collectLevels.getItemMeta();
                if (new Date().after(DateUtils.addDays(lastClaimDate, 1))) {
                    meta.setDisplayName("§a§lENCHANT GENERATOR");
                    List<String> lore = new ArrayList<>();
                    lore.add("");
                    lore.add("§7§lENCHANTS LEVELS AVAILABLE");
                    lore.add("§7| §f" + choosedEnchants.get(0) + ": §7" + getEnchantLevelAmount(player, choosedEnchants.get(0)) + " Levels");
                    lore.add("§7| §f" + choosedEnchants.get(1) + ": §7" + getEnchantLevelAmount(player, choosedEnchants.get(1)) + " Levels");
                    lore.add("§7| §f" + choosedEnchants.get(2) + ": §7" + getEnchantLevelAmount(player, choosedEnchants.get(2)) + " Levels");
                    lore.add("");
                    lore.add("§6§lUSAGE");
                    lore.add("§6| §fClick me to claim");
                    lore.add("");
                    meta.setLore(lore);
                    collectLevels.setItemMeta(meta);
                } else {
                    meta.setDisplayName("§c§lGENERATING...");
                    List<String> lore = new ArrayList<>();
                    lore.add("");
                    lore.add("§c§lENCHANTS NOT AVAILABLE");
                    lore.add("§c| §fPlease wait while enchants are generated...");
                    lore.add("§c| §fEstimated time: §c" + main.mainClass.formatDurationWithLetters(Duration.between(new Date().toInstant(), DateUtils.addDays(lastClaimDate, 1).toInstant())));
                    lore.add("");
                    meta.setLore(lore);
                    collectLevels.setItemMeta(meta);
                }

                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("MM-dd-yyyy HH:mm:ss");

                ItemStack changeEnchants = new ItemStack(Material.DROPPER);
                meta = changeEnchants.getItemMeta();
                meta.setDisplayName("§6§lENCHANT RESET");
                List<String> lore = new ArrayList<>();
                lore.add("");
                lore.add("§7§lCOOLDOWN");
                lore.add("§7| §f5 days");
                lore.add("§7| §fLast Change: §7" + (lastChangeDate == null ? "Never" : simpleDateFormat.format(lastChangeDate)));
                if (lastChangeDate != null) {
                    lore.add("§7| §fNext Change Date: §f" + main.mainClass.formatDurationWithLetters(Duration.between(new Date().toInstant(), DateUtils.addDays(lastChangeDate, 5).toInstant())));
                }
                lore.add("");
                lore.add("§6§lUSAGE");
                lore.add("§6| §fClick me to reset your choosed enchants");
                lore.add("");
                meta.setLore(lore);
                changeEnchants.setItemMeta(meta);

                ItemStack informations = new ItemStack(Material.BOOK);
                meta = informations.getItemMeta();
                meta.setDisplayName("§6§lINFORMATIONS");
                lore = new ArrayList<>();
                lore.add("");
                lore.add("§7| §fThe higher your VIP is the more levels");
                lore.add("§7| §fyou will receive. For the enchants that require blocks");
                lore.add("§7| §fonly 1 level will be added for all the VIPs");
                lore.add("");
                lore.add("§a§lVIP§7: §f5% §7of Limited Enchants, §f1/15 §7of Infinite Enchants");
                lore.add("§b§lVIP+§7: §f10% §7of Limited Enchants, §f1/14 §7of Infinite Enchants");
                lore.add("§6§lMVP§7: §f15% §7of Limited Enchants, §f1/13 §7of Infinite Enchants");
                lore.add("§e§lMVP+§7: §f20% §7of Limited Enchants, §f1/12 §7of Infinite Enchants");
                lore.add("§c§lA§6§lL§e§lP§a§lH§b§lA§7: §f25% §7of Limited Enchants, §f1/11 §7of Infinite Enchants");
                lore.add("§c§lA§6§lL§e§lP§a§lH§b§lA §d§l+§7: §f35% §7of Limited Enchants, §f1/10 §7of Infinite Enchants");
                lore.add("");
                meta.setLore(lore);
                informations.setItemMeta(meta);

                gui.setItem(29, informations);
                gui.setItem(31, collectLevels);
                gui.setItem(33, changeEnchants);
            }
            player.openInventory(gui);
        } else {
            player.sendMessage(prefix + "§cPlease hold your pickaxe in order to do this");
        }
    }
}
