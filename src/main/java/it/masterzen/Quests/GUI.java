package it.masterzen.Quests;

import com.sk89q.worldedit.MaxChangedBlocksException;
import it.masterzen.MongoDB.DataTypes.QuestPojo;
import it.masterzen.MongoDB.PlayerData;
import it.masterzen.MongoDB.ServerData;
import it.masterzen.blockbreak.EnchantList;
import it.masterzen.blockbreak.Utils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class GUI implements Listener {

    private Main main;
    private final String prefix = "§e§lQUESTS §8»§7 ";

    public GUI(Main main) {
        this.main = main;
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) throws IOException, MaxChangedBlocksException {
        if (event.getClickedInventory() != null && event.getClickedInventory().equals(event.getView().getTopInventory())) {
            Player player = (Player) event.getWhoClicked();

            if (event.getView().getTitle().equalsIgnoreCase("§e§lQUESTS §f| §7Reward Picker")) {
                event.setCancelled(true);

                if (event.getSlot() == 21 || event.getSlot() == 22 || event.getSlot() == 23) {
                    PlayerData data = main.mainClass.getMongoReader().getPlayerData(player.getUniqueId());
                    List<QuestPojo> questsList = data.getQuestList();
                    if (questsList != null && !questsList.isEmpty()) {
                        for (QuestPojo quest : questsList) {
                            if (StringUtils.isBlank(quest.getRewardType())) {
                                if (event.getSlot() == 21) {
                                    quest.setRewardType("Tokens");
                                    player.sendMessage(prefix + "Reward choosed: §aTokens");
                                } else if (event.getSlot() == 22) {
                                    quest.setRewardType("Money");
                                    player.sendMessage(prefix + "Reward choosed: §eMoney");
                                } else {
                                    quest.setRewardType("Keys");
                                    player.sendMessage(prefix + "Reward choosed: §6Keys");
                                }
                                break;
                            }
                        }
                    }

                    data.setQuestList(questsList);
                }

                openGUI(player);
            } else if (event.getView().getTitle().equalsIgnoreCase("§e§lQUESTS §f| §7Menu")) {
                event.setCancelled(true);
            }
        }
    }

    public void openGUI(Player player) {
        Inventory gui = Bukkit.createInventory(null, 45, "§e§lQUESTS §f| §7Menu");
        PlayerData data = main.mainClass.getMongoReader().getPlayerData(player.getUniqueId());
        boolean needToChooseReward = false;
        List<QuestPojo> questsList = data.getQuestList();

        if (questsList != null && !questsList.isEmpty()) {
            for (QuestPojo quest : questsList) {
                if (StringUtils.isBlank(quest.getRewardType())) {
                    needToChooseReward = true;
                    break;
                }
            }

            if (needToChooseReward) {
                gui = Bukkit.createInventory(null, 45, "§e§lQUESTS §f| §7Reward Picker");
                it.masterzen.commands.Main.FillBorder(gui);
                for (QuestPojo quest : questsList) {
                    if (StringUtils.isBlank(quest.getRewardType())) {

                        ItemStack questItem = new ItemStack(Material.DIAMOND);
                        ItemMeta meta = questItem.getItemMeta();
                        meta.setDisplayName("§e§lQUEST SETUP");
                        List<String> lore = new ArrayList<>();
                        lore.add("");
                        lore.add("§e§lDETAILS");
                        lore.add("§e| §fType: §e" + main.decodeType(quest.getQuestType()));
                        lore.add("§e| §fDifficult: §e" + main.decodeDifficult(quest.getDifficulty()));
                        lore.add("");
                        meta.setLore(lore);
                        questItem.setItemMeta(meta);

                        ItemStack firstReward = new ItemStack(Material.MAGMA_CREAM);
                        meta = firstReward.getItemMeta();
                        meta.setDisplayName("§a§lTOKEN REWARD");
                        lore = new ArrayList<>();
                        lore.add("");
                        lore.add("§a§lDETAILS");
                        lore.add("§a| §fAmount: §a" + main.getTokenRewardFromTypeAndDifficult(quest.getQuestType(), quest.getDifficulty()) + "% §fpick value");
                        lore.add("");
                        lore.add("§7§lDESCRIPTION");
                        lore.add("§7| §fThe reward is based on the Type");
                        lore.add("§7| §fand the Difficult of the quest");
                        lore.add("");
                        meta.setLore(lore);
                        firstReward.setItemMeta(meta);

                        ItemStack secondReward = new ItemStack(Material.GOLD_INGOT);
                        meta = secondReward.getItemMeta();
                        meta.setDisplayName("§e§lMONEY REWARD");
                        lore = new ArrayList<>();
                        lore.add("");
                        lore.add("§e§lDETAILS");
                        lore.add("§e| §fAmount: §e" + main.getMoneyRewardFromTypeAndDifficult(quest.getQuestType(), quest.getDifficulty()) + "% §fof current balance");
                        lore.add("");
                        lore.add("§7§lDESCRIPTION");
                        lore.add("§7| §fThe reward is based on the Type");
                        lore.add("§7| §fand the Difficult of the quest");
                        lore.add("");
                        meta.setLore(lore);
                        secondReward.setItemMeta(meta);

                        Pair<Integer, Integer> keysRewards = main.getKeyRewardFromTypeAndDifficult(quest.getQuestType(), quest.getDifficulty());
                        ItemStack thirdReward = new ItemStack(Material.TRIPWIRE_HOOK);
                        meta = thirdReward.getItemMeta();
                        meta.setDisplayName("§6§lKEYS REWARD");
                        lore = new ArrayList<>();
                        lore.add("");
                        lore.add("§6§lDETAILS");
                        lore.add("§6| " + main.mainClass.getKeysManager().getKeyName("Alpha") + " §fKeys: §6" + keysRewards.getKey());
                        lore.add("§6| " + main.mainClass.getKeysManager().getKeyName("Armor") + " §fKeys: §6" + keysRewards.getValue());
                        lore.add("");
                        lore.add("§7§lDESCRIPTION");
                        lore.add("§7| §fThe reward is based on the Type");
                        lore.add("§7| §fand the Difficult of the quest");
                        lore.add("");
                        meta.setLore(lore);
                        thirdReward.setItemMeta(meta);

                        gui.setItem(21, firstReward);
                        gui.setItem(22, secondReward);
                        gui.setItem(23, thirdReward);
                        gui.setItem(4, questItem);
                        break;
                    }
                }
            } else {
                int counter = 1;
                it.masterzen.commands.Main.FillBorder(gui);
                for (QuestPojo quest : questsList) {
                    Material questMaterial = null;

                    if (StringUtils.equals(quest.getRewardType(), "Tokens")) {
                        questMaterial = Material.MAGMA_CREAM;
                    } else if (StringUtils.equals(quest.getRewardType(), "Money")) {
                        questMaterial = Material.GOLD_INGOT;
                    } else if (StringUtils.equals(quest.getRewardType(), "Keys")) {
                        questMaterial = Material.TRIPWIRE_HOOK;
                    }
                    ItemStack questItem = new ItemStack(questMaterial);
                    ItemMeta meta = questItem.getItemMeta();
                    meta.setDisplayName("§6§lQUEST §f#" + counter);
                    List<String> lore = new ArrayList<>();
                    lore.add("");
                    lore.add("§e§lDETAILS");
                    lore.add("§e| §fType: §e" + main.decodeType(quest.getQuestType()));
                    lore.add("§e| §fReward Choosed: §e" + quest.getRewardType());
                    lore.add("§e| §fDifficult: §e" + main.decodeDifficult(quest.getDifficulty()));
                    lore.add("§e| §fProgress: §e" + quest.getProgress() + "/" + main.getRequestsFromTypeAndDifficult(quest.getQuestType(), quest.getDifficulty()));
                    lore.add("§e| §fExpire: §e" + quest.getExpiryDate());
                    lore.add("");
                    lore.add("§6§lREWARDS");
                    if (StringUtils.equals(quest.getRewardType(), "Tokens")) {
                        lore.add("§6| §fAmount: §6" + main.getTokenRewardFromTypeAndDifficult(quest.getQuestType(), quest.getDifficulty()) + "% §fpick value (no limit)");
                    } else if (StringUtils.equals(quest.getRewardType(), "Money")) {
                        lore.add("§e| §fAmount: §6" + main.getMoneyRewardFromTypeAndDifficult(quest.getQuestType(), quest.getDifficulty()) + "% §fof current balance");
                    } else if (StringUtils.equals(quest.getRewardType(), "Keys")) {
                        Pair<Integer, Integer> keysRewards = main.getKeyRewardFromTypeAndDifficult(quest.getQuestType(), quest.getDifficulty());
                        lore.add("§6| " + main.mainClass.getKeysManager().getKeyName("Alpha") + " §fKeys: §6" + keysRewards.getKey());
                        lore.add("§6| " + main.mainClass.getKeysManager().getKeyName("Armor") + " §fKeys: §6" + keysRewards.getValue());
                    }
                    lore.add("");
                    lore.add("§7§lDESCRIPTION");
                    if (quest.getQuestType().equals("BLOCK_MINER")) {
                        lore.add("§7| §fTo gain progress you just have to");
                        lore.add("§7| §fmine blocks in any mine");
                    } else if (quest.getQuestType().equals("DUNGEON_MINER")) {
                        lore.add("§7| §fTo gain progress you have to");
                        lore.add("§7| §fmine ores at /dungeon");
                    } else if (quest.getQuestType().equals("PLAYTIME")) {
                        lore.add("§7| §fTo gain progress you have to");
                        lore.add("§7| §fbe online for the minutes required");
                    } else if (quest.getQuestType().equals("FARMER_MINER")) {
                        lore.add("§7| §fTo gain progress you have to");
                        lore.add("§7| §fbreak fully growth wheat at /warp farmer");
                    } else if (quest.getQuestType().equals("BEACON_MINER")) {
                        lore.add("§7| §fTo gain progress you have to");
                        lore.add("§7| §fbreak beacons (raw) at /beaconmine");
                    }
                    lore.add("");
                    meta.setLore(lore);
                    questItem.setItemMeta(meta);

                    if (counter == 1) {
                        gui.setItem(19, questItem);
                    } else if (counter == 2) {
                        gui.setItem(20, questItem);
                    } else if (counter == 3) {
                        gui.setItem(21, questItem);
                    } else if (counter == 4) {
                        gui.setItem(23, questItem);
                    } else if (counter == 5) {
                        gui.setItem(24, questItem);
                    } else if (counter == 6) {
                        gui.setItem(25, questItem);
                    } else {
                        gui.addItem(questItem);
                    }
                    counter++;
                }

                ServerData serverData = main.mainClass.getMongoReader().getServerData();
                if (serverData.getLastTimeQuestAdded() == null) {
                    serverData.setLastTimeQuestAdded(new Date());
                }
                ItemStack noQuests = new ItemStack(Material.BOOK);
                ItemMeta meta = noQuests.getItemMeta();
                meta.setDisplayName("§6§lINFORMATIONS");
                List<String> lore = new ArrayList<>();
                lore.add("");
                lore.add("§6| §7Next quest at: " + Utils.getFormattedDate(DateUtils.addHours(serverData.getLastTimeQuestAdded(), 4), "yyyy/MM/dd HH:mm"));
                lore.add("§6| §7Current time: " + Utils.getFormattedDate(new Date(), "yyyy/MM/dd HH:mm"));
                lore.add("");
                meta.setLore(lore);
                noQuests.setItemMeta(meta);
                gui.setItem(4, noQuests);
            }
        } else {
            it.masterzen.commands.Main.FillBorder(gui);

            ServerData serverData = main.mainClass.getMongoReader().getServerData();
            if (serverData.getLastTimeQuestAdded() == null) {
                serverData.setLastTimeQuestAdded(new Date());
            }
            ItemStack noQuests = new ItemStack(Material.BOOK);
            ItemMeta meta = noQuests.getItemMeta();
            meta.setDisplayName("§c§lNO QUESTS");
            List<String> lore = new ArrayList<>();
            lore.add("");
            lore.add("§c| §7At the moment you don't have");
            lore.add("§c| §7any quests to do. A new quest");
            lore.add("§c| §7will be given every 4 hour");
            lore.add("§c| §7Next quest at: " + Utils.getFormattedDate(DateUtils.addHours(serverData.getLastTimeQuestAdded(), 4), "yyyy/MM/dd HH:mm"));
            lore.add("§c| §7Current time: " + Utils.getFormattedDate(new Date(), "yyyy/MM/dd HH:mm"));
            lore.add("");
            meta.setLore(lore);
            noQuests.setItemMeta(meta);
            gui.setItem(22, noQuests);
        }

        player.openInventory(gui);
    }
}
