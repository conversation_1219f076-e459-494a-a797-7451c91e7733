package it.masterzen.Quests;

import it.masterzen.MongoDB.DataTypes.QuestPojo;
import it.masterzen.MongoDB.PlayerData;
import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.blockbreak.Enums;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

public class Main {

    private final String prefix = "§e§lQUESTS §8»§7 ";
    public final AlphaBlockBreak mainClass;
    public GUI gui;

    public Main(AlphaBlockBreak plugin) {
        mainClass = plugin;
        gui = new GUI(this);
    }

    public GUI getGui() { return gui; }

    public String decodeDifficult(int difficult) {
        String decodedDifficult = "";
        if (difficult == 0) {
            decodedDifficult = "Easy";
        } else if (difficult == 1) {
            decodedDifficult = "Medium";
        } else {
            decodedDifficult = "Hard";
        }

        return decodedDifficult;
    }

    public String decodeType(String type) {
        String decodedType = "";
        if (type.equals("BLOCK_MINER")) {
            decodedType = "Block Miner";
        } else if (type.equals("DUNGEON_MINER")) {
            decodedType = "Dungeon Miner";
        } else if (type.equals("PLAYTIME")) {
            decodedType = "Playtime";
        } else if (type.equals("FARMER_MINER")) {
            decodedType = "Farmer";
        } else if (type.equals("BEACON_MINER")) {
            decodedType = "Beacon Miner";
        }

        return decodedType;
    }

    public double getTokenRewardFromTypeAndDifficult(String type, int difficult) {
        double reward = 0;

        if (type.equals("BLOCK_MINER")) {
            if (difficult == 0) {
                reward = 1.25;
            } else if (difficult == 1) {
                reward = 2.5;
            } else if (difficult == 2) {
                reward = 3.75;
            }
        } else if (type.equals("DUNGEON_MINER")) {
            if (difficult == 0) {
                reward = 2;
            } else if (difficult == 1) {
                reward = 4;
            } else if (difficult == 2) {
                reward = 6;
            }
        } else if (type.equals("PLAYTIME")) {
            if (difficult == 0) {
                reward = 2;
            } else if (difficult == 1) {
                reward = 3;
            } else if (difficult == 2) {
                reward = 4;
            }
        } else if (type.equals("FARMER_MINER")) {
            if (difficult == 0) {
                reward = 1;
            } else if (difficult == 1) {
                reward = 2.5;
            } else if (difficult == 2) {
                reward = 4;
            }
        } else if (type.equals("BEACON_MINER")) {
            if (difficult == 0) {
                reward = 1;
            } else if (difficult == 1) {
                reward = 2;
            } else if (difficult == 2) {
                reward = 3;
            }
        }

        return reward;
    }

    public double getMoneyRewardFromTypeAndDifficult(String type, int difficult) {
        double reward = 0;

        if (type.equals("BLOCK_MINER")) {
            if (difficult == 0) {
                reward = 1;
            } else if (difficult == 1) {
                reward = 2;
            } else if (difficult == 2) {
                reward = 3;
            }
        } else if (type.equals("DUNGEON_MINER")) {
            if (difficult == 0) {
                reward = 2;
            } else if (difficult == 1) {
                reward = 4;
            } else if (difficult == 2) {
                reward = 6;
            }
        } else if (type.equals("PLAYTIME")) {
            if (difficult == 0) {
                reward = 2;
            } else if (difficult == 1) {
                reward = 3;
            } else if (difficult == 2) {
                reward = 4;
            }
        } else if (type.equals("FARMER_MINER")) {
            if (difficult == 0) {
                reward = 1.5;
            } else if (difficult == 1) {
                reward = 3;
            } else if (difficult == 2) {
                reward = 4.5;
            }
        } else if (type.equals("BEACON_MINER")) {
            if (difficult == 0) {
                reward = 1;
            } else if (difficult == 1) {
                reward = 2;
            } else if (difficult == 2) {
                reward = 3;
            }
        }

        return reward;
    }

    // Alpha, Armor
    public Pair<Integer, Integer> getKeyRewardFromTypeAndDifficult(String type, int difficult) {
        Pair<Integer, Integer> reward = null;

        if (type.equals("BLOCK_MINER")) {
            if (difficult == 0) {
                reward = new ImmutablePair<>(1, 1);
            } else if (difficult == 1) {
                reward = new ImmutablePair<>(3, 2);
            } else if (difficult == 2) {
                reward = new ImmutablePair<>(5, 3);
            }
        } else if (type.equals("DUNGEON_MINER")) {
            if (difficult == 0) {
                reward = new ImmutablePair<>(2, 2);
            } else if (difficult == 1) {
                reward = new ImmutablePair<>(5, 4);
            } else if (difficult == 2) {
                reward = new ImmutablePair<>(10, 6);
            }
        } else if (type.equals("PLAYTIME")) {
            if (difficult == 0) {
                reward = new ImmutablePair<>(3, 3);
            } else if (difficult == 1) {
                reward = new ImmutablePair<>(4, 4);
            } else if (difficult == 2) {
                reward = new ImmutablePair<>(5, 5);
            }
        } else if (type.equals("FARMER_MINER")) {
            if (difficult == 0) {
                reward = new ImmutablePair<>(1, 1);
            } else if (difficult == 1) {
                reward = new ImmutablePair<>(2, 1);
            } else if (difficult == 2) {
                reward = new ImmutablePair<>(3, 2);
            }
        } else if (type.equals("BEACON_MINER")) {
            if (difficult == 0) {
                reward = new ImmutablePair<>(1, 1);
            } else if (difficult == 1) {
                reward = new ImmutablePair<>(2, 2);
            } else if (difficult == 2) {
                reward = new ImmutablePair<>(3, 3);
            }
        }

        return reward;
    }

    public double getRequestsFromTypeAndDifficult(String type, int difficult) {
        double request = 0;

        if (type.equals("BLOCK_MINER")) {
            if (difficult == 0) {
                request = 25000;
            } else if (difficult == 1) {
                request = 50000;
            } else if (difficult == 2) {
                request = 100000;
            }
        } else if (type.equals("DUNGEON_MINER")) { // ores mined
            if (difficult == 0) {
                request = 500;
            } else if (difficult == 1) {
                request = 1000;
            } else if (difficult == 2) {
                request = 1500;
            }
        } else if (type.equals("PLAYTIME")) { // minuted played
            if (difficult == 0) {
                request = 90;
            } else if (difficult == 1) {
                request = 120;
            } else if (difficult == 2) {
                request = 150;
            }
        } else if (type.equals("FARMER_MINER")) { // crops harvested
            if (difficult == 0) {
                request = 500;
            } else if (difficult == 1) {
                request = 750;
            } else if (difficult == 2) {
                request = 1000;
            }
        } else if (type.equals("BEACON_MINER")) {
            if (difficult == 0) {
                request = 100;
            } else if (difficult == 1) {
                request = 200;
            } else if (difficult == 2) {
                request = 300;
            }
        }

        return request;
    }

    public void addQuest(Player player) {
        PlayerData data = mainClass.getMongoReader().getPlayerData(player.getUniqueId());

        QuestPojo quest = new QuestPojo();
        quest.setQuestType(Enums.Quests.values()[ThreadLocalRandom.current().nextInt(Enums.Quests.values().length)].toString());
        quest.setProgress(0D);
        quest.setDifficulty(ThreadLocalRandom.current().nextInt(3));
        quest.setExpiryDate(DateUtils.addDays(new Date(), 1));
        if (data.getQuestList() == null) {
            data.setQuestList(new ArrayList<>());
        }
        data.getQuestList().add(quest);

        player.sendTitle("§e§lNEW QUEST", "§7§oYou have a new quest to do", 5, 80, 5);
        player.sendMessage(prefix + "A new quest has been added to your §f/quests");
        player.sendMessage("§7Quest Type: §f" + decodeType(quest.getQuestType()));
        player.sendMessage("§7Difficult: §f" + decodeDifficult(quest.getDifficulty()));

        checkExpiringQuests(player);
    }

    public void checkExpiringQuests(Player player) {
        PlayerData data = mainClass.getMongoReader().getPlayerData(player.getUniqueId());

        if (data.getQuestList() != null) {
            List<QuestPojo> updatedQuestList = new ArrayList<>(data.getQuestList());
            for (QuestPojo checkedQuest : data.getQuestList()) {
                if (new Date().after(DateUtils.addDays(checkedQuest.getExpiryDate(), 1))) {
                    updatedQuestList.remove(checkedQuest);
                    player.sendMessage(prefix + "§cQuest Failed");
                    player.sendMessage("§7Quest Type: §f" + checkedQuest.getQuestType());
                    player.sendMessage("§7Difficult: §f" + decodeDifficult(checkedQuest.getDifficulty()));
                    player.sendMessage("§7Progress: §f" + checkedQuest.getProgress() + "/" + getRequestsFromTypeAndDifficult(checkedQuest.getQuestType(), checkedQuest.getDifficulty()));
                }
            }

            data.setQuestList(updatedQuestList);
        }
    }

    public void addProgress(Player player, String type, double amount) {
        new BukkitRunnable() {
            @Override
            public void run() {
                PlayerData data = mainClass.getMongoReader().getPlayerData(player.getUniqueId());
                if (data.getQuestList() != null) {
                    List<QuestPojo> questList = data.getQuestList();
                    List<QuestPojo> updatedQuestList = new ArrayList<>(questList);
                    for (QuestPojo quest : questList) {
                        if (StringUtils.equalsIgnoreCase(quest.getQuestType(), type)) {
                            double totalNeeded = getRequestsFromTypeAndDifficult(type, quest.getDifficulty());
                            if (quest.getProgress() < totalNeeded) {
                                quest.addProgress(amount);
                                if (quest.getProgress() > totalNeeded) { // in caso supero il 100%
                                    quest.setProgress(totalNeeded);
                                }
                            } else { // completata
                                if (StringUtils.isNotBlank(quest.getRewardType())) {
                                    giveReward(player, quest.getQuestType(), quest.getDifficulty(), quest.getRewardType());
                                    updatedQuestList.remove(quest); // tolgo quest completata
                                } else {
                                    player.sendMessage(prefix + "You have completed a quest but you've not chosen the reward");
                                    player.sendMessage("§7Go setup the reward on /quests");
                                }
                            }
                            double percentageCompleted = quest.getProgress() / totalNeeded * 100;
                            if (percentageCompleted == 25) {
                                player.sendMessage(prefix + "You're §a25% §7done for quest §f" + decodeType(quest.getQuestType()) + " §o(" + decodeDifficult(quest.getDifficulty()) + ")");
                            } else if (percentageCompleted == 50) {
                                player.sendMessage(prefix + "You're §a50% §7done for quest §f" + decodeType(quest.getQuestType()) + " §o(" + decodeDifficult(quest.getDifficulty()) + ")");
                            } else if (percentageCompleted == 75) {
                                player.sendMessage(prefix + "You're §a75% §7done for quest §f" + decodeType(quest.getQuestType()) + " §o(" + decodeDifficult(quest.getDifficulty()) + ")");
                            }
                        }
                    }
                    data.setQuestList(updatedQuestList);
                }
            }
        }.runTask(mainClass);
    }

    public void giveReward(Player player, String type, int difficult, String rewardType) {
        if (StringUtils.equals(rewardType, "Tokens")) {
            double pickValueToAdd = getTokenRewardFromTypeAndDifficult(type, difficult);
            double playerPickValue = mainClass.getPickValue(player);

            double totalTokensToAdd = playerPickValue * (pickValueToAdd / 100);
            mainClass.getTeAPI().addTokens(player, totalTokensToAdd);
            mainClass.getResume().addValue(player, "Tokens", totalTokensToAdd);
            player.sendMessage(prefix + "You received §a" + mainClass.newFormatNumber(totalTokensToAdd) + " Tokens §7from a Quest");
        } else if (StringUtils.equals(rewardType, "Money")) {
            double percentageToAdd = getMoneyRewardFromTypeAndDifficult(type, difficult);
            double playerMoney = mainClass.getEconomy().getBalance(player);

            double totalMoneyToAdd = playerMoney * (percentageToAdd / 100);
            mainClass.getEconomy().depositPlayer(player, totalMoneyToAdd);
            mainClass.getResume().addValue(player, "Money", totalMoneyToAdd);
            player.sendMessage(prefix + "You received §e" + mainClass.newFormatNumber(totalMoneyToAdd) + " Money §7from a Quest");
        } else if (org.apache.commons.lang3.StringUtils.equals(rewardType, "Keys")) {
            Pair<Integer, Integer> keysRewards = getKeyRewardFromTypeAndDifficult(type, difficult);
            mainClass.getKeysManager().giveKeys(player, "Alpha", keysRewards.getKey(), true);
            mainClass.getKeysManager().giveKeys(player, "Armor", keysRewards.getValue(), true);
            player.sendMessage(prefix + "You received §f" + keysRewards.getKey() + " " + mainClass.getKeysManager().getKeyName("Alpha") + " §7Keys and §f" + keysRewards.getValue() + " " + mainClass.getKeysManager().getKeyName("Armor") + " §7Keys from a Quest");
        }
    }
}
