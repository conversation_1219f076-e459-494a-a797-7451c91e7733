package it.masterzen.Resume;

import java.util.*;

public class CurrencyItem {

    private HashMap<String, Double> tempMap;

    public CurrencyItem() {
        this.tempMap = new HashMap<>();
    }

    public void addItem(String type, double amount) {
        if (tempMap.containsKey(type)) {
            tempMap.put(type, tempMap.get(type) + amount);
        } else {
            tempMap.put(type, amount);
        }
    }

    public double getItem(String type) {
        return this.tempMap.get(type);
    }

    public boolean isInMap(String type) {
        return this.tempMap.containsKey(type);
    }

    public boolean isEmpty() {
        return this.tempMap.isEmpty();
    }

    //public Set<String> getTypes() {
    //    if (!this.tempMap.isEmpty()) {
    //        return this.tempMap.keySet();
    //    } else {
    //        return new HashSet<>();
    //    }
    //}
}
