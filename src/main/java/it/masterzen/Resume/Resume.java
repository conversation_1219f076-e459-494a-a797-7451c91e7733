package it.masterzen.Resume;

import it.masterzen.MongoDB.ServerData;
import it.masterzen.blockbreak.AlphaBlockBreak;
import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.event.Listener;

import javax.swing.*;
import java.util.*;

public class Resume implements CommandExecutor, Listener {

    public HashMap<UUID, CurrencyItem> resume = new HashMap<>();
    public int infiniteLoopID = 0;
    private AlphaBlockBreak mainClass;

    public Resume(AlphaBlockBreak plugin) {
        mainClass = plugin;
        for (Player player : Bukkit.getOnlinePlayers()) {
            if (!resume.containsKey(player.getUniqueId())) {
                resume.put(player.getUniqueId(), new CurrencyItem());
            }
        }
        startScheduler();
    }

    public void sendResume() {
        for (Player player : Bukkit.getOnlinePlayers()) {
            sendResume(player, true);
        }
    }

    public void sendResume(Player player, boolean remove) {
        if (resume.containsKey(player.getUniqueId()) && !resume.get(player.getUniqueId()).isEmpty()) {
            player.sendMessage("§e§lRESUME");
            player.sendMessage("§7§o(Last minute gains)");
            ServerData serverData = mainClass.getMongoReader().getServerData();
            if (serverData.getGlobalTokenBoosterExpiryDate() != null
                    && new Date().before(serverData.getGlobalTokenBoosterExpiryDate())) {
                player.sendMessage("§7§oGlobal " + serverData.getGlobalTokenBoosterMultiplier() + " Token Booster §a§oENABLED");
            }
            if (serverData.getGlobalMoneyBoosterExpiryDate() != null
                    && new Date().before(serverData.getGlobalMoneyBoosterExpiryDate())) {
                player.sendMessage("§7§oGlobal " + serverData.getGlobalMoneyBoosterMultiplier() + " Money Booster §a§oENABLED");
            }

            player.sendMessage("");
            if (resume.get(player.getUniqueId()).isInMap("Money")) {
                player.sendMessage("§7+ §e" + mainClass.newFormatNumber(resume.get(player.getUniqueId()).getItem("Money")) + " §e§lMONEY");
            }
            if (resume.get(player.getUniqueId()).isInMap("Tokens")) {
                player.sendMessage("§7+ §a" + mainClass.newFormatNumber(resume.get(player.getUniqueId()).getItem("Tokens")) + " §a§lTOKENS");
            }
            if (resume.get(player.getUniqueId()).isInMap("Blocks")) {
                player.sendMessage("§7+ §b" + Math.round(resume.get(player.getUniqueId()).getItem("Blocks")) + " §b§lBLOCKS");
            }
            player.sendMessage("");
            if (resume.get(player.getUniqueId()).isInMap("Delta")) {
                player.sendMessage("§7+ §7" + Math.round(resume.get(player.getUniqueId()).getItem("Delta")) + " §b§lDELTA §7Keys");
            }
            if (resume.get(player.getUniqueId()).isInMap("Beta")) {
                player.sendMessage("§7+ §7" + Math.round(resume.get(player.getUniqueId()).getItem("Beta")) + " §c§lBETA §7Keys");
            }
            if (resume.get(player.getUniqueId()).isInMap("Token")) {
                player.sendMessage("§7+ §7" + Math.round(resume.get(player.getUniqueId()).getItem("Token")) + " §a§lTOKEN §7Keys");
            }
            if (resume.get(player.getUniqueId()).isInMap("Random")) {
                player.sendMessage("§7+ §7" + Math.round(resume.get(player.getUniqueId()).getItem("Random")) + " §2§lRAN§a§lDOM §7Keys");
            }
            if (resume.get(player.getUniqueId()).isInMap("Gamma")) {
                player.sendMessage("§7+ §7" + Math.round(resume.get(player.getUniqueId()).getItem("Gamma")) + " §d§lGAMMA §7Keys");
            }
            if (resume.get(player.getUniqueId()).isInMap("Omega")) {
                player.sendMessage("§7+ §7" + Math.round(resume.get(player.getUniqueId()).getItem("Omega")) + " §e§lOMEGA §7Keys");
            }
            if (resume.get(player.getUniqueId()).isInMap("Armor")) {
                player.sendMessage("§7+ §7" + Math.round(resume.get(player.getUniqueId()).getItem("Armor")) + " §6§lARMOR §7Keys");
            }
            if (resume.get(player.getUniqueId()).isInMap("Alpha")) {
                player.sendMessage("§7+ §7" + Math.round(resume.get(player.getUniqueId()).getItem("Alpha")) + " §c§lA§6§lL§e§lP§a§lH§b§lA §7Keys");
            }
            player.sendMessage("");

            if (remove) {
                resume.remove(player.getUniqueId());
            }
        }
    }

    public void clearPlayer(Player player) {
        resume.remove(player.getUniqueId());
    }

    public void addValue(Player player, String type, double amount) {
        if (!resume.containsKey(player.getUniqueId())) {
            resume.put(player.getUniqueId(), new CurrencyItem());
        }
        resume.get(player.getUniqueId()).addItem(type, amount);
    }

    public void startScheduler() {
        if (infiniteLoopID == 0) {
            infiniteLoopID = Bukkit.getScheduler().scheduleSyncRepeatingTask(mainClass, new Runnable() {
                @Override
                public void run() {
                    sendResume();
                }
            }, 1200L, 1200L);
        }
    }

    @Override
    public boolean onCommand(CommandSender sender, Command cmd, String label, String[] args) {
        if (cmd.getName().equalsIgnoreCase("resume")) {
            if (sender instanceof Player) {
                Player player = (Player) sender;
                if (args.length == 0 ) {
                    sendResume(player, true);
                }
            }
        }
        return false;
    }
}
