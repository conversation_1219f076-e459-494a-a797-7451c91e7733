package it.masterzen.MobCoin;

import com.songoda.skyblock.api.SkyBlockAPI;
import com.songoda.skyblock.api.island.Island;
import com.songoda.skyblock.api.island.IslandRole;
import it.masterzen.blockbreak.AlphaBlockBreak;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.block.Chest;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Item;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.UUID;

public class Main implements CommandExecutor, Listener {

    private static HashMap<UUID, Long> mobCoins = new HashMap<>();
    private final String prefix = "§e§lMOBCOINS §8»§7 ";
    private static YamlConfiguration ymlFile;

    private GUI gui;
    public final AlphaBlockBreak mainClass;

    public Main(AlphaBlockBreak plugin) {
        mainClass = plugin;
        gui = new GUI(this);
    }

    @EventHandler
    public void onPlayerClicks(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        Action action = event.getAction();

        if (action.equals(Action.RIGHT_CLICK_AIR) || action.equals(Action.RIGHT_CLICK_BLOCK)) {
            ItemStack itemInHand = player.getInventory().getItemInMainHand();

            if (itemInHand.getType().equals(Material.DOUBLE_PLANT)) {
                event.setCancelled(true);
                for (ItemStack item : player.getInventory().getContents()) {
                    if (item != null && item.getType().equals(Material.DOUBLE_PLANT) && item.hasItemMeta()) {
                        if (ChatColor.stripColor(item.getItemMeta().getDisplayName()).equalsIgnoreCase("MOB COIN")) {
                            int amount = item.getAmount();

                            addMobCoins(player, amount);
                            //player.getInventory().remove(item);
                        } else if (ChatColor.stripColor(item.getItemMeta().getDisplayName()).contains("MOB COIN")) {
                            int amount = item.getAmount();
                            int mobcoins = Integer.parseInt(ChatColor.stripColor(item.getItemMeta().getDisplayName()).replace(" MOB COIN", ""));

                            int total = mobcoins * amount;
                            addMobCoins(player, total);

                            //player.getInventory().remove(item);
                        }
                        mainClass.removeItemFromPlayer(player, null, Material.DOUBLE_PLANT, true, true, 0);
                    }
                }
            }
            if (action.equals(Action.RIGHT_CLICK_BLOCK)) {
                if (event.getClickedBlock().getType().equals(Material.CHEST) || event.getClickedBlock().getType().equals(Material.TRAPPED_CHEST)) {
                    Island island = SkyBlockAPI.getIslandManager().getIslandAtLocation(event.getClickedBlock().getLocation());
                    Chest chest = (Chest) event.getClickedBlock().getState();
                    if (itemInHand.getType().equals(Material.STICK) && itemInHand.hasItemMeta() && itemInHand.getItemMeta().hasLore() && ChatColor.stripColor(itemInHand.getItemMeta().getDisplayName()).contains("- Collector")) {
                        event.setCancelled(true);
                        if (island != null && island.getRole(player) != null && (island.getRole(player).equals(IslandRole.MEMBER) || island.getRole(player).equals(IslandRole.OPERATOR) || island.getRole(player).equals(IslandRole.COOP) || island.getRole(player).equals(IslandRole.OWNER) || player.isOp())) {
                            if (!itemInHand.getItemMeta().getDisplayName().contains("Collector")) {
                                for (ItemStack item : chest.getInventory().getContents()) {
                                    if (item != null) {
                                        if (ChatColor.stripColor(item.getItemMeta().getDisplayName()).equalsIgnoreCase("MOB COIN") && !item.getItemMeta().getDisplayName().contains("- Collector")) {
                                            int amount = item.getAmount();

                                            addMobCoins(player, amount);
                                            chest.getInventory().remove(item);
                                        } else if (ChatColor.stripColor(item.getItemMeta().getDisplayName()).contains("MOB COIN") && !item.getItemMeta().getDisplayName().contains("- Collector")) {
                                            int amount = item.getAmount();
                                            int mobcoins = Integer.parseInt(ChatColor.stripColor(item.getItemMeta().getDisplayName()).replace(" MOB COIN", ""));

                                            int total = mobcoins * amount;
                                            addMobCoins(player, total);
                                            chest.getInventory().remove(item);
                                        }
                                    }
                                }
                            } else {
                                int level = 1;
                                if (itemInHand.getItemMeta().getDisplayName().contains("Lv")) {
                                    level = Integer.parseInt(ChatColor.stripColor(itemInHand.getItemMeta().getDisplayName()).replace("MOB COIN Lv", "").replace(" - Collector", ""));
                                }
                                for (ItemStack item : chest.getInventory().getContents()) {
                                    if (item != null && (item.hasItemMeta() && item.getItemMeta().hasDisplayName() && !item.getItemMeta().getDisplayName().contains("- Collector"))) {
                                        if (ChatColor.stripColor(item.getItemMeta().getDisplayName()).equalsIgnoreCase("MOB COIN") && !item.getItemMeta().getDisplayName().contains("- Collector")) {
                                            int amount = item.getAmount();
                                            amount = amount * level;

                                            addMobCoins(player, amount);
                                            chest.getInventory().remove(item);
                                        } else if (ChatColor.stripColor(item.getItemMeta().getDisplayName()).contains("MOB COIN") && !item.getItemMeta().getDisplayName().contains("- Collector")) {
                                            int amount = item.getAmount();
                                            int mobcoins = Integer.parseInt(ChatColor.stripColor(item.getItemMeta().getDisplayName()).replace(" MOB COIN", ""));

                                            int total = mobcoins * amount * level;
                                            addMobCoins(player, total);
                                            chest.getInventory().remove(item);
                                        }
                                    }
                                }
                            }
                        } else {
                            player.sendMessage(prefix + "§cYou can't use your stick on that island");
                        }
                    }
                }
            }
        }
    }

    public void loadPlayerMobCoins() {
        //File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/PlayerMobCoins.yml");
        //ymlFile = YamlConfiguration.loadConfiguration(file);

        for (Player player : Bukkit.getOnlinePlayers()) {
            loadPlayerMobCoins(player);
        }
    }

    public void loadPlayerMobCoins(Player player) {
        File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/PlayerMobCoins.yml");
        ymlFile = YamlConfiguration.loadConfiguration(file);

        if (!mobCoins.containsKey(player.getUniqueId())) {
            mobCoins.put(player.getUniqueId(), ymlFile.getLong(player.getUniqueId() + ".points"));
        }
    }

    public HashMap<UUID, Long> getMobCoins() {
        return mobCoins;
    }

    public void savePlayerMobCoins(Player player) throws IOException {
        File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/PlayerMobCoins.yml");
        ymlFile = YamlConfiguration.loadConfiguration(file);
        ymlFile.set(player.getUniqueId() + ".points", mobCoins.get(player.getUniqueId()));
        ymlFile.save(file);
        mobCoins.remove(player.getUniqueId());
    }

    public void addMobCoins(Player player, long amount) {
        if (!mobCoins.containsKey(player.getUniqueId())) {
            mobCoins.put(player.getUniqueId(), amount);
        } else {
            mobCoins.replace(player.getUniqueId(), mobCoins.get(player.getUniqueId()) + amount);
        }
        player.sendMessage(prefix + "You received §a§l" + amount + " §7MobCoins");
    }

    public void removeMobCoins(Player player, long amount) {
        if (mobCoins.containsKey(player.getUniqueId())) {
            mobCoins.replace(player.getUniqueId(), mobCoins.get(player.getUniqueId()) - amount);
        }
    }

    public void sendMobCoins(Player player) {
        if (!mobCoins.containsKey(player.getUniqueId())) {
            player.sendMessage(prefix + "You got §a§l0 §7MobCoins");
        } else {
            player.sendMessage(prefix + "You got §a§l" + mobCoins.get(player.getUniqueId()) + " §7MobCoins");
        }
    }

    public long getMobCoins(Player player) {
        if (!mobCoins.containsKey(player.getUniqueId())) {
            return 0;
        } else {
            return mobCoins.get(player.getUniqueId());
        }
    }

    @Override
    public boolean onCommand(CommandSender sender, Command cmd, String label, String[] args) {
        if (cmd.getName().equalsIgnoreCase("mobcoins") || cmd.getName().equalsIgnoreCase("mobcoin") || cmd.getName().equalsIgnoreCase("mc")) {
            if (sender instanceof Player) {
                Player player = (Player) sender;
                if (args.length == 3 && player.isOp()) {
                    addMobCoins(Bukkit.getPlayerExact(args[1]), Integer.parseInt(args[2]));
                    player.sendMessage(prefix + "§a§l" + Integer.parseInt(args[2]) + "§7 Mobcoins added to " + Bukkit.getPlayerExact(args[1]).getName() + "§7's balance");
                }
                gui.openGUI(player);
            } else {
                addMobCoins(Bukkit.getPlayerExact(args[1]), Integer.parseInt(args[2]));
            }
        }
        return false;
    }
}
