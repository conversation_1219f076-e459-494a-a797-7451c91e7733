package it.masterzen.MongoDB;

import java.io.IOException;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.util.Date;

public class DateAdapter extends TypeAdapter<Date> {

    @Override
    public void write(JsonWriter out, Date value) throws IOException {
        out.beginObject();
        out.name("$date");
        out.value(value.getTime());
        out.endObject();
    }

    @Override
    public Date read(JsonReader in) throws IOException {
        in.beginObject();
        in.nextName();
        long value = in.nextLong();
        in.endObject();
        return new Date(value);
    }
}
