package it.masterzen.MongoDB.DataTypes;

import com.fasterxml.jackson.annotation.JsonProperty;
import it.masterzen.blockbreak.Enums;
import org.bson.types.ObjectId;

import java.util.Date;

public class QuestPojo {

    @JsonProperty("_id")
    private ObjectId id;

    private String questType;
    private String rewardType;
    private Integer difficulty;
    private Double progress;
    private Date expiryDate;

    public ObjectId getId() {
        return id;
    }

    public void setId(ObjectId id) {
        this.id = id;
    }

    public String getQuestType() {
        return questType;
    }

    public void setQuestType(String questType) {
        this.questType = questType;
    }

    public String getRewardType() {
        return rewardType;
    }

    public void setRewardType(String rewardType) {
        this.rewardType = rewardType;
    }

    public Integer getDifficulty() {
        return difficulty;
    }

    public void setDifficulty(Integer difficulty) {
        this.difficulty = difficulty;
    }

    public synchronized Double getProgress() {
        return progress;
    }

    public synchronized void setProgress(Double progress) {
        this.progress = progress;
    }

    public synchronized void addProgress(Double progress) {
        if (this.progress == null) {
            this.progress = 0D;
        }
        this.progress = this.progress + progress;
    }

    public Date getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }
}
