package it.masterzen.MongoDB.DataTypes;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.bson.types.ObjectId;

import java.util.Date;

public class CropGeneratorPojo {

    @JsonProperty("_id")
    private ObjectId id;

    private String islandUUID;
    private Date lastClaimDate;
    private String playerUUID; // chi ha piazzato il generatore

    public ObjectId getId() {
        return id;
    }

    public void setId(ObjectId id) {
        this.id = id;
    }

    public String getIslandUUID() {
        return islandUUID;
    }

    public void setIslandUUID(String islandUUID) {
        this.islandUUID = islandUUID;
    }

    public Date getLastClaimDate() {
        return lastClaimDate;
    }

    public void setLastClaimDate(Date lastClaimDate) {
        this.lastClaimDate = lastClaimDate;
    }

    public String getPlayerUUID() {
        return playerUUID;
    }

    public void setPlayerUUID(String playerUUID) {
        this.playerUUID = playerUUID;
    }
}
