package it.masterzen.MongoDB.DataTypes;

import java.util.Date;
import java.util.Map;

public class Log<PERSON>ojo {

    private String player;
    private String playerUUID;
    private String playerUUIDSaved;
    private Date date;
    private Map<Object, Object> datas;

    public String getPlayer() {
        return player;
    }

    public void setPlayer(String player) {
        this.player = player;
    }

    public String getPlayerUUID() {
        return playerUUID;
    }

    public void setPlayerUUID(String playerUUID) {
        this.playerUUID = playerUUID;
    }

    public String getPlayerUUIDSaved() {
        return playerUUIDSaved;
    }

    public void setPlayerUUIDSaved(String playerUUIDSaved) {
        this.playerUUIDSaved = playerUUIDSaved;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Map<Object, Object> getDatas() {
        return datas;
    }

    public void setDatas(Map<Object, Object> datas) {
        this.datas = datas;
    }
}
