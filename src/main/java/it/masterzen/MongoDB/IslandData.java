package it.masterzen.MongoDB;

import com.fasterxml.jackson.annotation.JsonProperty;
import it.masterzen.MongoDB.DataTypes.IslandValueTracker;
import org.bson.types.ObjectId;

import java.util.Date;
import java.util.List;

public class IslandData {

    @JsonProperty("_id")
    private ObjectId id;
    private String uuid;

    // Spawners
    private Integer moneySpawners;
    private Integer tokenSpawners;
    private Integer silverfishSpawners;
    private Integer mobcoinsSpawners;
    private Double spawnersMoney;
    private Double spawnersTokens;
    private Integer spawnersRobot;
    private Integer spawnersPrestiges;
    private Integer spawnersMobcoins;

    private List<IslandValueTracker> memberValueDepositedList;

    private Date lastUpdate;

    public ObjectId getId() {
        return id;
    }

    public void setId(ObjectId id) {
        this.id = id;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public Integer getMoneySpawners() {
        return moneySpawners;
    }

    public void setMoneySpawners(Integer moneySpawners) {
        this.moneySpawners = moneySpawners;
    }

    public Integer getTokenSpawners() {
        return tokenSpawners;
    }

    public void setTokenSpawners(Integer tokenSpawners) {
        this.tokenSpawners = tokenSpawners;
    }

    public Integer getSilverfishSpawners() {
        return silverfishSpawners;
    }

    public void setSilverfishSpawners(Integer silverfishSpawners) {
        this.silverfishSpawners = silverfishSpawners;
    }

    public Integer getMobcoinsSpawners() {
        return mobcoinsSpawners;
    }

    public void setMobcoinsSpawners(Integer mobcoinsSpawners) {
        this.mobcoinsSpawners = mobcoinsSpawners;
    }

    public Double getSpawnersMoney() {
        return spawnersMoney;
    }

    public void setSpawnersMoney(Double spawnersMoney) {
        this.spawnersMoney = spawnersMoney;
    }

    public void addSpawnersMoney(Double spawnersMoney) {
        if (this.spawnersMoney == null) {
            this.spawnersMoney = 0D;
        }

        this.spawnersMoney = this.spawnersMoney + spawnersMoney;
    }

    public Double getSpawnersTokens() {
        return spawnersTokens;
    }

    public void setSpawnersTokens(Double spawnersTokens) {
        this.spawnersTokens = spawnersTokens;
    }

    public void addSpawnersTokens(Double spawnersTokens) {
        if (this.spawnersTokens == null) {
            this.spawnersTokens = 0D;
        }

        this.spawnersTokens = this.spawnersTokens + spawnersTokens;
    }

    public Integer getSpawnersRobot() {
        return spawnersRobot;
    }

    public void setSpawnersRobot(Integer spawnersRobot) {
        this.spawnersRobot = spawnersRobot;
    }

    public void addSpawnersRobot(Integer spawnersRobot) {
        if (this.spawnersRobot == null) {
            this.spawnersRobot = 0;
        }

        this.spawnersRobot = this.spawnersRobot + spawnersRobot;
    }

    public Integer getSpawnersPrestiges() {
        return spawnersPrestiges;
    }

    public void setSpawnersPrestiges(Integer spawnersPrestiges) {
        this.spawnersPrestiges = spawnersPrestiges;
    }

    public void addSpawnersPrestiges(Integer spawnersPrestiges) {
        if (this.spawnersPrestiges == null) {
            this.spawnersPrestiges = 0;
        }

        this.spawnersPrestiges = this.spawnersPrestiges + spawnersPrestiges;
    }

    public Integer getSpawnersMobcoins() {
        return spawnersMobcoins;
    }

    public void setSpawnersMobcoins(Integer spawnersMobcoins) {
        this.spawnersMobcoins = spawnersMobcoins;
    }

    public void addSpawnersMobcoins(Integer spawnersMobcoins) {
        if (this.spawnersMobcoins == null) {
            this.spawnersMobcoins = 0;
        }

        this.spawnersMobcoins = this.spawnersMobcoins + spawnersMobcoins;
    }

    public List<IslandValueTracker> getMemberValueDepositedList() {
        return memberValueDepositedList;
    }

    public void setMemberValueDepositedList(List<IslandValueTracker> memberValueDepositedList) {
        this.memberValueDepositedList = memberValueDepositedList;
    }

    public Date getLastUpdate() {
        return lastUpdate;
    }

    public void setLastUpdate(Date lastUpdate) {
        this.lastUpdate = lastUpdate;
    }
}
