package it.masterzen.MongoDB;

import com.fasterxml.jackson.annotation.JsonProperty;
import it.masterzen.MongoDB.DataTypes.QuestPojo;
import it.masterzen.blockbreak.AlphaBlockBreak;
import org.bson.types.ObjectId;
import org.bukkit.Bukkit;

import java.util.Date;
import java.util.List;
import java.util.UUID;

public class PlayerData {

    @JsonProperty("_id")
    private ObjectId id;
    private String uuid;
    private String name;
    private Date firstJoinDate;

    // Milestones
    private Double moneyBoosterFromMilestone;
    private Double tokenBoosterFromMilestone;
    private Double keysBoosterFromMilestone;
    private Double xpBoosterFromMilestone;

    // Custom Class
    private String classCode;
    private Date classClaimDate;

    // Vote x VoteShop
    private Integer votePoints;

    // Booster per XpShop
    private Integer xpShopBooster;
    private Date xpShopLastBoostDate;
    private Integer xpShopBlocksCounter;
    // XpShop Quests
    private Date xpShopFirstClaimOfDay;
    private Integer xpShopQuestClaimedOfDay;

    // Enchanter
    private List<String> enchanterEnchantList;                  // max 3 enchants
    private Date enchanterLatestClaimDate;                      // last time that player change enchants
    private Date enchanterLatestChangeDate;                     // last time that player change enchants

    // Pickaxe tier
    private Integer pickaxeCurrentTier;
    private Integer pickaxeCurrentXP;
    private Boolean pickaxeUpdated;
    private Integer pickaxeResetAmount;

    // Blocks counter
    private Integer rawBlocks;
    private Integer blocksMined;

    // Quests
    private List<QuestPojo> questList;

    // Discounts for Dungeon and Farmer
    private Integer dungeonDiscount;
    private Integer farmerDiscount;

    // Keys Boosters
    private Double keysMoneyBooster;
    private Double keysTokenBooster;

    // Farmer
    private Integer farmerCropsFarmed;
    private Double farmerDoublePointChance;

    // God's Hand Cooldown
    private Date godHandLastClaimDate;

    // Pick Reset Stuff
    private Integer rebirthPointsAddedLastReset;
    private Integer rebirthPointsSpended;
    private Integer farmerPointsAddedLastReset;
    private Integer farmerPointsSpended;

    private Date lastUpdate;

    public ObjectId getId() {
        return id;
    }

    public void setId(ObjectId id) {
        this.id = id;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getFirstJoinDate() {
        return firstJoinDate;
    }

    public void setFirstJoinDate(Date firstJoinDate) {
        this.firstJoinDate = firstJoinDate;
    }

    public Double getMoneyBoosterFromMilestone() {
        return moneyBoosterFromMilestone;
    }

    public void setMoneyBoosterFromMilestone(Double moneyBoosterFromMilestone) {
        this.moneyBoosterFromMilestone = moneyBoosterFromMilestone;
    }

    public void addMoneyBooster(Double amount) {
        if (this.moneyBoosterFromMilestone == null) {
            this.moneyBoosterFromMilestone = 0D;
        }
        this.moneyBoosterFromMilestone = this.moneyBoosterFromMilestone + amount;
    }

    public Double getTokenBoosterFromMilestone() {
        return tokenBoosterFromMilestone;
    }

    public void setTokenBoosterFromMilestone(Double tokenBoosterFromMilestone) {
        this.tokenBoosterFromMilestone = tokenBoosterFromMilestone;
    }

    public void addTokenBooster(Double amount) {
        if (this.tokenBoosterFromMilestone == null) {
            this.tokenBoosterFromMilestone = 0D;
        }
        this.tokenBoosterFromMilestone = this.tokenBoosterFromMilestone + amount;
    }

    public Double getKeysBoosterFromMilestone() {
        return keysBoosterFromMilestone;
    }

    public void setKeysBoosterFromMilestone(Double keysBoosterFromMilestone) {
        this.keysBoosterFromMilestone = keysBoosterFromMilestone;
    }

    public void addKeysBooster(Double amount) {
        if (this.keysBoosterFromMilestone == null) {
            this.keysBoosterFromMilestone = 0D;
        }
        this.keysBoosterFromMilestone = this.keysBoosterFromMilestone + amount;
    }

    public Double getXpBoosterFromMilestone() {
        return xpBoosterFromMilestone;
    }

    public void setXpBoosterFromMilestone(Double xpBoosterFromMilestone) {
        this.xpBoosterFromMilestone = xpBoosterFromMilestone;
    }

    public void addXpBooster(Double amount) {
        if (this.xpBoosterFromMilestone == null) {
            this.xpBoosterFromMilestone = 0D;
        }
        this.xpBoosterFromMilestone = this.xpBoosterFromMilestone + amount;
    }

    public Double getKeysMoneyBooster() {
        return keysMoneyBooster;
    }

    public void setKeysMoneyBooster(Double keysMoneyBooster) {
        this.keysMoneyBooster = keysMoneyBooster;
    }

    public Double getKeysTokenBooster() {
        return keysTokenBooster;
    }

    public void setKeysTokenBooster(Double keysTokenBooster) {
        this.keysTokenBooster = keysTokenBooster;
    }

    public Integer getFarmerCropsFarmed() {
        if (farmerCropsFarmed == null) {
            farmerCropsFarmed = 0;
        }
        return farmerCropsFarmed;
    }

    public void setFarmerCropsFarmed(Integer farmerCropsFarmed) {
        this.farmerCropsFarmed = farmerCropsFarmed;
    }

    public Double getFarmerDoublePointChance() {
        if (farmerDoublePointChance == null) {
            farmerDoublePointChance = 0D;
        }
        return farmerDoublePointChance;
    }

    public void setFarmerDoublePointChance(Double farmerDoublePointChance) {
        this.farmerDoublePointChance = farmerDoublePointChance;
    }

    public Date getGodHandLastClaimDate() {
        return godHandLastClaimDate;
    }

    public void setGodHandLastClaimDate(Date godHandLastClaimDate) {
        this.godHandLastClaimDate = godHandLastClaimDate;
    }

    public Date getLastUpdate() {
        return lastUpdate;
    }

    public void setLastUpdate(Date lastUpdate) {
        this.lastUpdate = lastUpdate;
    }

    public String getClassCode() {
        return classCode;
    }

    public void setClassCode(String classCode) {
        this.classCode = classCode;
    }

    public Date getClassClaimDate() {
        return classClaimDate;
    }

    public void setClassClaimDate(Date classClaimDate) {
        this.classClaimDate = classClaimDate;
    }

    public Integer getVotePoints() {
        return votePoints;
    }

    public void setVotePoints(Integer votePoints) {
        this.votePoints = votePoints;
    }

    public void addVotePoints(Integer amount) {
        if (votePoints == null) {
            votePoints = 0;
        }
        votePoints = votePoints + amount;
    }

    public void removeVotePoints(Integer amount) {
        if (votePoints == null) {
            votePoints = 0;
        }
        votePoints = votePoints - amount;
    }

    public Integer getXpShopBooster() {
        return xpShopBooster;
    }

    public void setXpShopBooster(Integer xpShopBooster) {
        this.xpShopBooster = xpShopBooster;
    }

    public void addXpShopBooster(Integer amount) {
        if (xpShopBooster == null) {
            xpShopBooster = 0;
        }
        xpShopBooster = xpShopBooster + amount;
    }

    public void removeXpShopBooster(Integer amount) {
        if (xpShopBooster == null) {
            xpShopBooster = 0;
        }
        xpShopBooster = xpShopBooster - amount;
    }

    public Date getXpShopLastBoostDate() {
        return xpShopLastBoostDate;
    }

    public void setXpShopLastBoostDate(Date xpShopLastBoostDate) {
        this.xpShopLastBoostDate = xpShopLastBoostDate;
    }

    public Integer getXpShopBlocksCounter() {
        return xpShopBlocksCounter;
    }

    public void setXpShopBlocksCounter(Integer xpShopBlocksCounter) {
        this.xpShopBlocksCounter = xpShopBlocksCounter;
    }

    public Date getXpShopFirstClaimOfDay() {
        return xpShopFirstClaimOfDay;
    }

    public void setXpShopFirstClaimOfDay(Date xpShopFirstClaimOfDay) {
        this.xpShopFirstClaimOfDay = xpShopFirstClaimOfDay;
    }

    public Integer getXpShopQuestClaimedOfDay() {
        return xpShopQuestClaimedOfDay;
    }

    public void setXpShopQuestClaimedOfDay(Integer xpShopQuestClaimedOfDay) {
        this.xpShopQuestClaimedOfDay = xpShopQuestClaimedOfDay;
    }

    public List<String> getEnchanterEnchantList() {
        return enchanterEnchantList;
    }

    public void setEnchanterEnchantList(List<String> enchanterEnchantList) {
        this.enchanterEnchantList = enchanterEnchantList;
    }

    public Date getEnchanterLatestClaimDate() {
        return enchanterLatestClaimDate;
    }

    public void setEnchanterLatestClaimDate(Date enchanterLatestClaimDate) {
        this.enchanterLatestClaimDate = enchanterLatestClaimDate;
    }

    public Date getEnchanterLatestChangeDate() {
        return enchanterLatestChangeDate;
    }

    public void setEnchanterLatestChangeDate(Date enchanterLatestChangeDate) {
        this.enchanterLatestChangeDate = enchanterLatestChangeDate;
    }

    public synchronized Integer getPickaxeCurrentTier() {
        return pickaxeCurrentTier;
    }

    public synchronized void setPickaxeCurrentTier(Integer pickaxeCurrentTier) {
        this.pickaxeCurrentTier = pickaxeCurrentTier;
    }

    public synchronized void addPickaxeCurrentTier(Integer pickaxeCurrentTier) {
        if (this.pickaxeCurrentTier == null) {
            this.pickaxeCurrentTier = 0;
        }
        this.pickaxeCurrentTier = this.pickaxeCurrentTier + pickaxeCurrentTier;
    }

    public synchronized Integer getPickaxeCurrentXP() {
        return pickaxeCurrentXP;
    }

    public synchronized void setPickaxeCurrentXP(Integer pickaxeCurrentXP) {
        this.pickaxeCurrentXP = pickaxeCurrentXP;
    }

    public synchronized void addPickaxeCurrentXP(Integer pickaxeCurrentXP) {
        if (this.pickaxeCurrentXP == null) {
            this.pickaxeCurrentXP = 0;
        }
        this.pickaxeCurrentXP = this.pickaxeCurrentXP + pickaxeCurrentXP;
    }

    public Boolean getPickaxeUpdated() {
        return pickaxeUpdated;
    }

    public void setPickaxeUpdated(Boolean pickaxeUpdated) {
        this.pickaxeUpdated = pickaxeUpdated;
    }

    public Integer getPickaxeResetAmount() {
        return pickaxeResetAmount;
    }

    public void setPickaxeResetAmount(Integer pickaxeResetAmount) {
        this.pickaxeResetAmount = pickaxeResetAmount;
    }

    public synchronized Integer getRawBlocks() {
        return rawBlocks;
    }

    public synchronized void setRawBlocks(Integer rawBlocks) {
        this.rawBlocks = rawBlocks;
    }

    public synchronized void addRawBlocks(Integer rawBlocks) {
        if (this.rawBlocks == null) {
            this.rawBlocks = 0;
        }
        this.rawBlocks = this.rawBlocks + rawBlocks;
    }

    public synchronized Integer getBlocksMined() {
        return blocksMined;
    }

    public synchronized void setBlocksMined(Integer blocksMined) {
        this.blocksMined = blocksMined;
    }

    public synchronized void addBlocksMined(Integer blocksMined) {
        if (this.blocksMined == null) {
            this.blocksMined = 0;
        }
        this.blocksMined = this.blocksMined + blocksMined;
    }

    public synchronized List<QuestPojo> getQuestList() {
        return questList;
    }

    public synchronized void setQuestList(List<QuestPojo> questList) {
        this.questList = questList;
    }

    public Integer getDungeonDiscount() {
        return dungeonDiscount;
    }

    public void setDungeonDiscount(Integer dungeonDiscount) {
        this.dungeonDiscount = dungeonDiscount;
    }

    public Integer getFarmerDiscount() {
        return farmerDiscount;
    }

    public void setFarmerDiscount(Integer farmerDiscount) {
        this.farmerDiscount = farmerDiscount;
    }

    public Integer getRebirthPointsSpended() {
        return rebirthPointsSpended;
    }

    public void setRebirthPointsSpended(Integer rebirthPointsSpended) {
        this.rebirthPointsSpended = rebirthPointsSpended;
    }

    public Integer getFarmerPointsSpended() {
        return farmerPointsSpended;
    }

    public void setFarmerPointsSpended(Integer farmerPointsSpended) {
        this.farmerPointsSpended = farmerPointsSpended;
    }

    // Prestige Shop: per-player dynamic Omega key price
    private Integer omegaKeyCurrentPrice;

    public Integer getOmegaKeyCurrentPrice() {
        if (omegaKeyCurrentPrice == null || omegaKeyCurrentPrice < 5000) {
            omegaKeyCurrentPrice = 5000;
        }
        return omegaKeyCurrentPrice;
    }

    public void setOmegaKeyCurrentPrice(Integer omegaKeyCurrentPrice) {
        this.omegaKeyCurrentPrice = omegaKeyCurrentPrice;
    }
}
