package it.masterzen.MongoDB;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.bson.types.ObjectId;

import java.util.Date;

/**
 * Data model for custom crops stored in MongoDB
 * Represents a planted crop with its location, ownership, and type information
 */
public class CustomCropData {

    @JsonProperty("_id")
    private ObjectId id;
    
    // Location data
    private String worldName;
    private int x;
    private int y;
    private int z;
    
    // Ownership data
    private String playerUUID;
    private String islandUUID;
    
    // Crop data
    private String cropType; // "wheat", "carrot", "potato"
    private Date plantedDate;
    private Date lastUpdate;

    public CustomCropData() {
        this.plantedDate = new Date();
        this.lastUpdate = new Date();
    }

    public CustomCropData(String worldName, int x, int y, int z, String playerUUID, String islandUUID, String cropType) {
        this.worldName = worldName;
        this.x = x;
        this.y = y;
        this.z = z;
        this.playerUUID = playerUUID;
        this.islandUUID = islandUUID;
        this.cropType = cropType;
        this.plantedDate = new Date();
        this.lastUpdate = new Date();
    }

    // Getters and Setters
    public ObjectId getId() {
        return id;
    }

    public void setId(ObjectId id) {
        this.id = id;
    }

    public String getWorldName() {
        return worldName;
    }

    public void setWorldName(String worldName) {
        this.worldName = worldName;
    }

    public int getX() {
        return x;
    }

    public void setX(int x) {
        this.x = x;
    }

    public int getY() {
        return y;
    }

    public void setY(int y) {
        this.y = y;
    }

    public int getZ() {
        return z;
    }

    public void setZ(int z) {
        this.z = z;
    }

    public String getPlayerUUID() {
        return playerUUID;
    }

    public void setPlayerUUID(String playerUUID) {
        this.playerUUID = playerUUID;
    }

    public String getIslandUUID() {
        return islandUUID;
    }

    public void setIslandUUID(String islandUUID) {
        this.islandUUID = islandUUID;
    }

    public String getCropType() {
        return cropType;
    }

    public void setCropType(String cropType) {
        this.cropType = cropType;
    }

    public Date getPlantedDate() {
        return plantedDate;
    }

    public void setPlantedDate(Date plantedDate) {
        this.plantedDate = plantedDate;
    }

    public Date getLastUpdate() {
        return lastUpdate;
    }

    public void setLastUpdate(Date lastUpdate) {
        this.lastUpdate = lastUpdate;
    }

    /**
     * Creates a location key for quick lookups
     * Format: "worldName:x:y:z"
     */
    public String getLocationKey() {
        return worldName + ":" + x + ":" + y + ":" + z;
    }

    /**
     * Checks if this crop is at the specified location
     */
    public boolean isAtLocation(String worldName, int x, int y, int z) {
        return this.worldName.equals(worldName) && this.x == x && this.y == y && this.z == z;
    }

    @Override
    public String toString() {
        return "CustomCropData{" +
                "id=" + id +
                ", worldName='" + worldName + '\'' +
                ", x=" + x +
                ", y=" + y +
                ", z=" + z +
                ", playerUUID='" + playerUUID + '\'' +
                ", islandUUID='" + islandUUID + '\'' +
                ", cropType='" + cropType + '\'' +
                ", plantedDate=" + plantedDate +
                ", lastUpdate=" + lastUpdate +
                '}';
    }
}
