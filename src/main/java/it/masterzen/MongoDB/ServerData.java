package it.masterzen.MongoDB;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.bson.types.ObjectId;

import java.util.Date;

public class ServerData {

    @JsonProperty("_id")
    private ObjectId id;
    private Date lastUpdate;

    // Farmer event
    private Boolean isFarmerEventActive;
    private Date farmerEventExpiryDate;

    // Global boosters
    private Date globalTokenBoosterExpiryDate;
    private Double globalTokenBoosterMultiplier;
    private Date globalMoneyBoosterExpiryDate;
    private Double globalMoneyBoosterMultiplier;

    private Date lastTimeQuestAdded;

    public ObjectId getId() {
        return id;
    }

    public void setId(ObjectId id) {
        this.id = id;
    }

    public Date getLastUpdate() {
        return lastUpdate;
    }

    public void setLastUpdate(Date lastUpdate) {
        this.lastUpdate = lastUpdate;
    }

    public Boolean getFarmerEventActive() {
        if (isFarmerEventActive == null) {
            isFarmerEventActive = false;
        }
        return isFarmerEventActive;
    }

    public void setFarmerEventActive(Boolean farmerEventActive) {
        isFarmerEventActive = farmerEventActive;
    }

    public Date getFarmerEventExpiryDate() {
        return farmerEventExpiryDate;
    }

    public void setFarmerEventExpiryDate(Date farmerEventExpiryDate) {
        this.farmerEventExpiryDate = farmerEventExpiryDate;
    }

    public Date getGlobalTokenBoosterExpiryDate() {
        return globalTokenBoosterExpiryDate;
    }

    public void setGlobalTokenBoosterExpiryDate(Date globalTokenBoosterExpiryDate) {
        this.globalTokenBoosterExpiryDate = globalTokenBoosterExpiryDate;
    }

    public Double getGlobalTokenBoosterMultiplier() {
        return globalTokenBoosterMultiplier;
    }

    public void setGlobalTokenBoosterMultiplier(Double globalTokenBoosterMultiplier) {
        this.globalTokenBoosterMultiplier = globalTokenBoosterMultiplier;
    }

    public Date getGlobalMoneyBoosterExpiryDate() {
        return globalMoneyBoosterExpiryDate;
    }

    public void setGlobalMoneyBoosterExpiryDate(Date globalMoneyBoosterExpiryDate) {
        this.globalMoneyBoosterExpiryDate = globalMoneyBoosterExpiryDate;
    }

    public Double getGlobalMoneyBoosterMultiplier() {
        return globalMoneyBoosterMultiplier;
    }

    public void setGlobalMoneyBoosterMultiplier(Double globalMoneyBoosterMultiplier) {
        this.globalMoneyBoosterMultiplier = globalMoneyBoosterMultiplier;
    }

    public Date getLastTimeQuestAdded() {
        return lastTimeQuestAdded;
    }

    public void setLastTimeQuestAdded(Date lastTimeQuestAdded) {
        this.lastTimeQuestAdded = lastTimeQuestAdded;
    }
}
