package it.masterzen.SkillTree;

public class PlayerSkill {

    private int xp;
    private int token;
    private int money;
    private int jackhammer;
    private int keys;

    public PlayerSkill(int xp, int token, int money, int jackhammer, int keys) {
        this.xp = xp;
        this.token = token;
        this.money = money;
        this.jackhammer = jackhammer;
        this.keys = keys;
    }

    public int getXp() {
        return xp;
    }

    public void setXp(int xp) {
        this.xp = xp;
    }

    public int getToken() {
        return token;
    }

    public void setToken(int token) {
        this.token = token;
    }

    public int getMoney() {
        return money;
    }

    public void setMoney(int money) {
        this.money = money;
    }

    public int getJackhammer() {
        return jackhammer;
    }

    public void setJackhammer(int jackhammer) {
        this.jackhammer = jackhammer;
    }

    public int getKeys() {
        return keys;
    }

    public void setKeys(int keys) {
        this.keys = keys;
    }
}
