package it.masterzen.SkillTree;

import com.sk89q.worldedit.MaxChangedBlocksException;
import com.sk89q.worldedit.world.DataException;
import com.sk89q.worldguard.protection.ApplicableRegionSet;
import com.sk89q.worldguard.protection.flags.DefaultFlag;
import com.sk89q.worldguard.protection.flags.StateFlag;
import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.blockbreak.XMaterial;
import me.clip.ezblocks.EZBlocks;
import org.bukkit.Bukkit;
import org.bukkit.block.Block;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.player.PlayerInteractEvent;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.UUID;

public class Main implements CommandExecutor, Listener {

    private static HashMap<UUID, Long> points = new HashMap<>();
    private final String prefix = "§e§lSKILLS §8»§7 ";
    private final String name = "SkillPoints";
    private static YamlConfiguration ymlFile;

    private it.masterzen.SkillTree.GUI gui;
    public final AlphaBlockBreak mainClass;

    public Main(AlphaBlockBreak plugin) {
        mainClass = plugin;
        gui = new GUI(this);
    }

    public String getPrefix() {
        return this.prefix;
    }

    public String getName() {
        return this.name;
    }

    public void loadPoints() {
        for (Player player : Bukkit.getOnlinePlayers()) {
            loadPoints(player);
        }
    }

    public void loadPoints(Player player) {
        File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/Player" + name + ".yml");
        ymlFile = YamlConfiguration.loadConfiguration(file);

        if (!points.containsKey(player.getUniqueId())) {
            points.put(player.getUniqueId(), ymlFile.getLong(player.getUniqueId() + ".points"));
        }
    }

    public HashMap<UUID, Long> getPoints() {
        return points;
    }

    public void savePoints() throws IOException {
        File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/Player" + name + ".yml");
        ymlFile = YamlConfiguration.loadConfiguration(file);

        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (Exception ex) {
            }
        }

        if (!points.isEmpty() && ymlFile != null) {
            for (UUID player : points.keySet()) {
                savePoints(player, false);
            }
        }
    }

    public void savePoints(Player player, boolean remove) throws IOException {
        File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/Player" + name + ".yml");
        ymlFile = YamlConfiguration.loadConfiguration(file);
        ymlFile.set(player.getUniqueId() + ".points", points.get(player.getUniqueId()));
        ymlFile.save(file);
        if (remove) {
            points.remove(player.getUniqueId());
        }
    }

    public void savePoints(UUID player, boolean remove) throws IOException {
        File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/Player" + name + ".yml");
        ymlFile = YamlConfiguration.loadConfiguration(file);
        ymlFile.set(player.toString() + ".points", points.get(player));
        ymlFile.save(file);
        if (remove) {
            points.remove(player);
        }
    }

    public void addPoints(Player player, long amount) {
        if (!points.containsKey(player.getUniqueId())) {
            points.put(player.getUniqueId(), amount);
        } else {
            points.replace(player.getUniqueId(), points.get(player.getUniqueId()) + amount);
        }
    }

    public void removePoints(Player player, long amount) {
        if (points.containsKey(player.getUniqueId())) {
            points.replace(player.getUniqueId(), points.get(player.getUniqueId()) - amount);
        }
    }

    public void setPoints(Player player, long amount) {
        points.remove(player.getUniqueId());
        points.put(player.getUniqueId(), amount);
    }

    public void sendPoints(Player player) {
        if (!points.containsKey(player.getUniqueId())) {
            player.sendMessage(prefix + "You got §a§l0 §7" + name);
        } else {
            player.sendMessage(prefix + "You got §a§l" + points.get(player.getUniqueId()) + " §7" + name);
        }
    }

    public long getPoints(Player player) {
        if (!points.containsKey(player.getUniqueId())) {
            return 0;
        } else {
            return points.get(player.getUniqueId());
        }
    }

    public void sendInfo(Player player) {
        player.sendMessage(prefix + "You got a total of §a§l" + getPoints(player) + "§7 " + name);
        player.sendMessage("");
        player.sendMessage("§7Command List");
        player.sendMessage("§7  /Skills");
        player.sendMessage("");
    }

    /*@EventHandler
    public void blockbreak(BlockBreakEvent event) throws IOException, DataException, MaxChangedBlocksException {
        event.setDropItems(false);
        Player player = event.getPlayer();

        if (player.getWorld().getName().equalsIgnoreCase("PrestigeMine")) {
            if (player.getInventory().getItemInMainHand().getType().equals(XMaterial.DIAMOND_PICKAXE.parseMaterial())) {
                ApplicableRegionSet set = mainClass.getWorldGuard().getRegionManager(player.getWorld()).getApplicableRegions(event.getBlock().getLocation());

                if (set.queryState(null, DefaultFlag.BLOCK_BREAK) == StateFlag.State.ALLOW) {
                    if (EZBlocks.getEZBlocks().getBlocksBroken(player) % 50000 == 0) {
                        addPoints(player, 1);
                        player.sendMessage(prefix + "You received §a§l1 §7Skill Point.");
                        player.sendMessage("§7You now got a total of §a§l" + getPoints(player) + "§7 Points. §7§o(/Skill)");
                    }
                }
            }
        }
    }*/

    @Override
    public boolean onCommand(CommandSender sender, Command cmd, String label, String[] args) {
        if (cmd.getName().equalsIgnoreCase("skill") || cmd.getName().equalsIgnoreCase("skills") || cmd.getName().equalsIgnoreCase("skilltree")  || cmd.getName().equalsIgnoreCase("st")) {
            if (sender instanceof Player) {
                Player player = (Player) sender;
                if (player.isOp() && args.length == 3 && args[0].equalsIgnoreCase("give")) {
                    if (Bukkit.getPlayerExact(args[1]) != null) {
                        Player tmpPlayer = Bukkit.getPlayerExact(args[1]);
                        long points = Long.parseLong(args[2]);
                        addPoints(tmpPlayer, points);
                        player.sendMessage(prefix + "§a§l" + points + " §7" + name + " §7Added to " + tmpPlayer.getName() + "'s balance §7§o(" + getPoints(tmpPlayer) + ")");
                        if (!player.getName().equals(tmpPlayer.getName())) {
                            tmpPlayer.sendMessage(prefix + "You received §a§l" + points + " §7" + name);
                        }
                    } else {
                        player.sendMessage(prefix + "§cPlayer offline");
                    }
                } else if (player.isOp() && args.length == 2 && args[0].equalsIgnoreCase("reset")) {
                    Player tmpPlayer = Bukkit.getPlayerExact(args[1]);
                    removePoints(tmpPlayer, getPoints(tmpPlayer));
                    tmpPlayer.sendMessage(prefix + "Your point balance has been resetted");
                    player.sendMessage(prefix + "You succesfully reset " + tmpPlayer.getName() + "'s point balance");
                } else if (args.length == 1 && (args[0].equalsIgnoreCase("bal") || args[0].equalsIgnoreCase("balance"))) {
                    sendPoints(player);
                } else {
                    gui.openGUI(player);
                }
            }
        }
        return false;
    }
}
