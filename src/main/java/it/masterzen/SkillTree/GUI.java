package it.masterzen.SkillTree;

import com.sk89q.worldedit.MaxChangedBlocksException;
import io.netty.util.internal.StringUtil;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class GUI implements Listener {

    private it.masterzen.SkillTree.Main main;
    private final String prefix = "§e§lSKILLS §8»§7 ";

    public GUI(it.masterzen.SkillTree.Main main) {
        this.main = main;
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) throws IOException, MaxChangedBlocksException {
        if (event.getClickedInventory() != null && event.getClickedInventory().equals(event.getView().getTopInventory())) {
            Player player = (Player) event.getWhoClicked();

            if (event.getView().getTitle().equalsIgnoreCase("§e§lSKILLS §f| §7Tree")) {
                event.setCancelled(true);
                ItemStack clickedItem = event.getCurrentItem();
                if (clickedItem != null && clickedItem.hasItemMeta() && clickedItem.getItemMeta().hasDisplayName()) {
                    if (!clickedItem.getItemMeta().getDisplayName().contains("Level") && !clickedItem.getType().equals(Material.ARROW)) {
                        String type = ChatColor.stripColor(clickedItem.getItemMeta().getDisplayName()).replace(" Skills", "");
                        openSkillGUI(player, type);
                    } else {
                        if (clickedItem.getType().equals(Material.ARROW)) {
                            openGUI(player);
                        } else {
                            String[] splitted = ChatColor.stripColor(clickedItem.getItemMeta().getDisplayName()).split(" ");
                            String type = splitted[0];
                            int level = Integer.parseInt(splitted[splitted.length - 1]);

                            splitted = ChatColor.stripColor(clickedItem.getItemMeta().getLore().get(2)).split(" ");
                            long price = Long.parseLong(splitted[1]);;

                            manageSkills(player, type, level, price);
                        }
                    }
                }
            }
        }
    }

    public void manageSkills(Player player, String type, int level, long price) {
        long playerPoints = main.getPoints(player);
        String previousPex = "skills." + type + "." + (level - 1);
        String pex = "skills." + type + "." + level;

        if (!player.hasPermission(pex)) {
            if (playerPoints >= price) {
                if (level > 1 && player.hasPermission(previousPex)) {
                    main.mainClass.addPex(player, pex, 0, false);
                    main.removePoints(player, price);
                    player.sendMessage(prefix + "§aYou learned a new skill !");
                    openSkillGUI(player, type);
                    main.mainClass.loadPlayerSkills(player);
                } else if (level == 1) {
                    main.mainClass.addPex(player, pex, 0, false);
                    main.removePoints(player, price);
                    player.sendMessage(prefix + "§aYou learned a new skill !");
                    openSkillGUI(player, type);
                    main.mainClass.loadPlayerSkills(player);
                } else {
                    player.sendMessage(prefix + "§cYou have to unlock the previous level first");
                }
            } else {
                player.sendMessage(prefix + "§cYou don't have enough points to unlock this Skill");
            }
        } else {
            player.sendMessage(prefix + "You already learned this Skill");
        }
    }

    public void openSkillGUI(Player player, String type) {
        Inventory gui = Bukkit.createInventory(null, (type.equalsIgnoreCase("XP") || type.equalsIgnoreCase("TOKENS") || type.equalsIgnoreCase("MONEY") || type.equalsIgnoreCase("KEYS") ? 36 : 27), "§e§lSKILLS §f| §7Tree");
        it.masterzen.commands.Main.FillBorder(gui);

        if (type.equalsIgnoreCase("XP")) {
            for (int i = 1; i <= 10; i++) {
                ItemStack item = new ItemStack(Material.EXP_BOTTLE);
                ItemMeta meta = item.getItemMeta();
                meta.setDisplayName("§b§lXP §bSkill §f| §7Level " + i);
                List<String> lore = new ArrayList<>();
                lore.add("");
                lore.add("§e§lPRICE");
                lore.add("§e| §e" + (int) (i < 5 ? Math.pow(2, i - 1) : Math.pow(2, (i - 5) - 1) + 16 ) + " §fSkill Points");
                lore.add("");
                lore.add("§6§lPERK");
                lore.add("§6| §6+" + (i * 10) + "% XP §ffrom XPGreed");
                lore.add("");
                lore.add("§7§lSTATUS");
                lore.add((player.hasPermission("skills.xp." + i) ? "§a§lUNLOCKED" : "§c§lBLOCKED"));
                lore.add("");
                if (!player.hasPermission("skills.xp." + i)) {
                    lore.add("§6§USAGE");
                    lore.add("§6| §fClick me to learn this skill");
                    lore.add("");
                } else {
                    main.mainClass.addGlowing(meta);
                }
                meta.setLore(lore);
                item.setItemMeta(meta);

                gui.setItem((i > 5 ? 14 : 10) + i, item);
            }
        } else if (type.equalsIgnoreCase("TOKENS")) {
            for (int i = 1; i <= 10; i++) {
                ItemStack item = new ItemStack(Material.MAGMA_CREAM);
                ItemMeta meta = item.getItemMeta();
                meta.setDisplayName("§a§lTOKENS §aSkills §f| §7Level " + i);
                List<String> lore = new ArrayList<>();
                lore.add("");
                lore.add("§e§lPRICE");
                lore.add("§e| §e" + (int) (i < 5 ? Math.pow(2, i - 1) : Math.pow(2, (i - 5) - 1) + 16 ) + " §fSkill Points");
                lore.add("");
                lore.add("§6§lPERK");
                lore.add("§6| §6+" + (i * 10) + "% Tokens §ffrom Token Greed");
                lore.add("");
                lore.add("§7§lSTATUS");
                lore.add((player.hasPermission("skills.tokens." + i) ? "§a§lUNLOCKED" : "§c§lBLOCKED"));
                lore.add("");
                if (!player.hasPermission("skills.tokens." + i)) {
                    lore.add("§6§USAGE");
                    lore.add("§6| §fClick me to learn this skill");
                    lore.add("");
                } else {
                    main.mainClass.addGlowing(meta);
                }
                meta.setLore(lore);
                item.setItemMeta(meta);

                gui.setItem((i > 5 ? 14 : 10) + i, item);
            }
        } else if (type.equalsIgnoreCase("MONEY")) {
            for (int i = 1; i <= 10; i++) {
                ItemStack item = new ItemStack(Material.PAPER);
                ItemMeta meta = item.getItemMeta();
                meta.setDisplayName("§e§lMONEY §eSkills §f| §7Level " + i);
                List<String> lore = new ArrayList<>();
                lore.add("");
                lore.add("§e§lPRICE");
                lore.add("§e| §e" + (int) (i < 5 ? Math.pow(2, i - 1) : Math.pow(2, (i - 5) - 1) + 16 ) + " §fSkill Points");
                lore.add("");
                lore.add("§6§lPERK");
                lore.add("§6| §6+" + (i * 10) + "% Money §ffrom AutoSell");
                lore.add("");
                lore.add("§7§lSTATUS");
                lore.add((player.hasPermission("skills.money." + i) ? "§a§lUNLOCKED" : "§c§lBLOCKED"));
                lore.add("");
                if (!player.hasPermission("skills.money." + i)) {
                    lore.add("§6§USAGE");
                    lore.add("§6| §fClick me to learn this skill");
                    lore.add("");
                } else {
                    main.mainClass.addGlowing(meta);
                }
                meta.setLore(lore);
                item.setItemMeta(meta);

                gui.setItem((i > 5 ? 14 : 10) + i, item);
            }
        } else if (type.equalsIgnoreCase("JACKHAMMER")) {
            ItemStack item = new ItemStack(Material.ANVIL);
            ItemMeta meta = item.getItemMeta();
            meta.setDisplayName("§d§lJACKHAMMER §dSkills §f| §7Level 1");
            List<String> lore = new ArrayList<>();
            lore.add("");
            lore.add("§e§lPRICE");
            lore.add("§e| §e10 §fSkill Points");
            lore.add("");
            lore.add("§6§lPERK");
            lore.add("§6| §6Obtain Tokens §ffrom JackHammer");
            lore.add("");
            lore.add("§7§lSTATUS");
            lore.add((player.hasPermission("skills.jackhammer.1") ? "§a§lUNLOCKED" : "§c§lBLOCKED"));
            lore.add("");
            if (!player.hasPermission("skills.jackhammer.1")) {
                lore.add("§6§USAGE");
                lore.add("§6| §fClick me to learn this skill");
                lore.add("");
            } else {
                main.mainClass.addGlowing(meta);
            }
            meta.setLore(lore);
            item.setItemMeta(meta);

            gui.setItem(13, item);
        } else if (type.equalsIgnoreCase("KEYS")) {
            for (int i = 1; i <= 10; i++) {
                ItemStack item = new ItemStack(Material.TRIPWIRE_HOOK);
                ItemMeta meta = item.getItemMeta();
                meta.setDisplayName("§c§lKEYS §cSkills §f| §7Level " + i);
                List<String> lore = new ArrayList<>();
                lore.add("");
                lore.add("§e§lPRICE");
                lore.add("§e| §e" + (int) (i == 1 ? 1 : (i <= 5 ? Math.pow(2, i - 1) + Math.pow(2, i - 2) : (i + 19))) + " §fSkill Points");
                lore.add("");
                lore.add("§6§lPERK");
                lore.add("§6| §6+" + (i * 10) + "% Proc Rate §fof KeyFinder");
                lore.add("");
                lore.add("§7§lSTATUS");
                lore.add((player.hasPermission("skills.keys." + i) ? "§a§lUNLOCKED" : "§c§lBLOCKED"));
                lore.add("");
                if (!player.hasPermission("skills.keys." + i)) {
                    lore.add("§6§USAGE");
                    lore.add("§6| §fClick me to learn this skill");
                    lore.add("");
                } else {
                    main.mainClass.addGlowing(meta);
                }
                meta.setLore(lore);
                item.setItemMeta(meta);

                gui.setItem((i > 5 ? 14 : 10) + i, item);
            }
        } else {
            openGUI(player);
        }

        ItemStack back = new ItemStack(Material.ARROW);
        ItemMeta meta = back.getItemMeta();
        meta.setDisplayName("§cBack to Main Menu");
        back.setItemMeta(meta);

        gui.setItem(gui.getSize() - 9, back);

        player.openInventory(gui);
    }

    public void openGUI(Player player) {
        Inventory gui = Bukkit.createInventory(null, 27, "§e§lSKILLS §f| §7Tree");
        it.masterzen.commands.Main.FillBorder(gui);

        ItemStack xp = new ItemStack(Material.EXP_BOTTLE);
        ItemMeta meta = xp.getItemMeta();
        meta.setDisplayName("§b§lXP §bSkills");
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fClick me to open the §7XP Skill Tree");
        lore.add("");
        meta.setLore(lore);
        xp.setItemMeta(meta);

        ItemStack tokens = new ItemStack(Material.MAGMA_CREAM);
        meta = tokens.getItemMeta();
        meta.setDisplayName("§a§lTOKENS §aSkills");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fClick me to open the §7TOKENS Skill Tree");
        lore.add("");
        meta.setLore(lore);
        tokens.setItemMeta(meta);

        ItemStack money = new ItemStack(Material.PAPER);
        meta = money.getItemMeta();
        meta.setDisplayName("§e§lMONEY §eSkills");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fClick me to open the §7MONEY Skill Tree");
        lore.add("");
        meta.setLore(lore);
        money.setItemMeta(meta);

        ItemStack jackhammer = new ItemStack(Material.ANVIL);
        meta = jackhammer.getItemMeta();
        meta.setDisplayName("§d§lJACKHAMMER §dSkills");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fClick me to open the §7JACKHAMMER Skill Tree");
        lore.add("");
        meta.setLore(lore);
        jackhammer.setItemMeta(meta);

        ItemStack keys = new ItemStack(Material.TRIPWIRE_HOOK);
        meta = keys.getItemMeta();
        meta.setDisplayName("§c§lKEYS §cSkills");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fClick me to open the §7KEYS Skill Tree");
        lore.add("");
        meta.setLore(lore);
        keys.setItemMeta(meta);

        ItemStack book = new ItemStack(Material.BOOK);
        meta = book.getItemMeta();
        meta.setDisplayName("§6§lINFORMATIONS");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lBALANCE");
        lore.add("§e| §e" + main.getPoints(player) + "§f Skill Points");
        lore.add("");
        lore.add("§7§lHOW IT WORKS");
        lore.add("§7| §fEvery §750k §fblocks mined you will receive §71 §fpoint");
        lore.add("");
        meta.setLore(lore);
        book.setItemMeta(meta);

        gui.setItem(4, book);
        gui.setItem(11, xp);
        gui.setItem(12, tokens);
        gui.setItem(13, money);
        gui.setItem(14, jackhammer);
        gui.setItem(15, keys);

        player.openInventory(gui);
    }

}
