package it.masterzen.commands;

import org.apache.commons.lang3.StringUtils;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.PluginCommand;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.PluginDescriptionFile;
import org.bukkit.plugin.java.JavaPlugin;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Opens an inventory-based Help GUI that lists only commands accessible to the player.
 * It reads the commands from plugin.yml via the plugin description and filters by permissions.
 */
public class HelpCommand implements CommandExecutor, Listener {

    private final JavaPlugin plugin;
    private final String titleBase = "§e§lHELP §f| §7Commands";
    private final int pageSize = 45; // 5 rows for content, last row for controls

    // Keep simple in-memory state per player for pagination
    private final Map<UUID, List<HelpEntry>> playerEntries = new HashMap<>();

    // Some commands are explicitly admin-only; hide them from players even if permission is not attached in plugin.yml
    private static final Set<String> ADMIN_KEYWORDS = new HashSet<>(Arrays.asList(
            "give", "add", "save", "align", "fix", "start", "global", "admin", "commandlog",
            "anticheat", "seasonday", "fakeleave", "test"
    ));

    // Enriched descriptions for commands (fallback to plugin.yml when not present)
    private static final Map<String, String> ENRICHED = new HashMap<>();
    static {
        // Core/help
        ENRICHED.put("help", "Open an interactive help menu with clickable commands and pagination.");
        ENRICHED.put("xpshop", "Open the XP Shop to buy special items and perks using your experience levels.");
        ENRICHED.put("multi", "Show your current AutoSell multiplier and active boosters.");
        ENRICHED.put("head", "Give yourself a custom decorative head. Staff-only on most servers.");
        ENRICHED.put("enchantgui", "Open the custom enchant GUI for your pickaxe (if available).\nUsage: /enchantGUI");
        ENRICHED.put("blockshop", "Open the Block Shop to purchase building blocks.");
        ENRICHED.put("pet", "Pet utilities and management (XP, Token, Key, Money, Beacon pets).");
        ENRICHED.put("pminelevelup", "Level up your Private Mine to unlock better ores and rates.");
        ENRICHED.put("openpmine", "Open or configure your Private Mine (taxes and access).");
        ENRICHED.put("pickvalue", "Calculate and display the total value of your pickaxe (enchants + tiers).");
        ENRICHED.put("randompet", "Receive a random pet from the /gkit system (if eligible).");
        ENRICHED.put("rebirth", "Rebirth to reset progress for powerful permanent bonuses.");
        ENRICHED.put("givereactionrewards", "Claim your ChatReaction rewards. Usually automated; use if instructed.");
        ENRICHED.put("spawnershop", "Open the Spawner Shard Finder GUI to spend shards on spawners.");
        ENRICHED.put("pointshop", "Exchange Magic Hand Points for rewards. Hold the Magic Hand stick to use.");
        ENRICHED.put("givegodshand", "Give yourself the Magic Hand stick. Staff-only.");
        ENRICHED.put("givegodshandlv2", "Give yourself the Magic Hand stick (Level 2). Staff-only.");
        ENRICHED.put("customtag", "Set a custom player chat tag (if you have permission).");
        ENRICHED.put("drop", "Toggle safe-drop to prevent losing valuable items accidentally.");
        ENRICHED.put("givedailyrewards", "Claim daily login rewards. Usually automated via GUI.");
        ENRICHED.put("randomevent", "Trigger a random economy event that affects shop prices. Staff-only.");
        ENRICHED.put("dailyboost", "Broadcast the daily shop price boost. Staff-only.");
        ENRICHED.put("startarena", "Start the Infinite Wave Arena event. Staff-only.");
        ENRICHED.put("savepick", "Save your current pickaxe to file (backup). Staff-only.");
        ENRICHED.put("givepick", "Retrieve a previously saved pickaxe. Staff-only.");
        ENRICHED.put("addmultiplier", "Grant an AutoSell multiplier from Alpha Crate. Staff-only.");
        ENRICHED.put("farmer", "Access the new Crops Farmer feature and related actions.");
        ENRICHED.put("givevoterewards", "Claim vote rewards (manual trigger). Usually automated. Staff-only.");
        ENRICHED.put("givevotepartyrewards", "Claim vote party rewards (manual trigger). Staff-only.");
        ENRICHED.put("rebirthshop", "Open the Rebirth Shop (unlocked after Rebirth 5).");
        ENRICHED.put("alphakeys", "Manage Alpha keys and open crates via GUI.");
        ENRICHED.put("worker", "Open the Worker feature to manage automated helpers.");
        ENRICHED.put("alphaenchant", "Open Alpha Prison custom enchant system for your pickaxe.");
        ENRICHED.put("pouch", "Open or redeem Token Pouches that grant dynamic tokens.");
        ENRICHED.put("beaconbackpack", "Open your Beacon Backpack to store and manage beacons.");
        ENRICHED.put("stackbeacons", "Move all beacons from your inventory into the Beacon Backpack.");
        ENRICHED.put("rebirthpoints", "Add or remove Rebirth Points (administrative). Staff-only.");
        ENRICHED.put("delete", "Delete the item in your hand. Use to clear unwanted picks. Irreversible!");
        ENRICHED.put("sg", "Open Server Goal feature to see and contribute to global objectives.");
        ENRICHED.put("key", "Open the Keys GUI to preview and open your keys.");
        ENRICHED.put("resume", "Open the Resume system showing periodic server/player summaries.");
        ENRICHED.put("withdraw", "Withdraw Money, Tokens, or XP into notes you can trade. Usage: /withdraw <money|tokens|xp> <amount>");
        ENRICHED.put("withdrawall", "Withdraw your entire balance (money, tokens, or XP) into notes.");
        ENRICHED.put("mobcoin", "Open MobCoins menu or use MobCoins utilities.");
        ENRICHED.put("prestigeshop", "Open the Prestige Points Shop and spend your prestige currency.");
        ENRICHED.put("candy", "Access the Christmas Candy Finder system and rewards.");
        ENRICHED.put("skill", "Open the Skill Tree to allocate points and unlock perks.");
        ENRICHED.put("robot", "Manage your Robots: upgrades, collection, and settings.");
        ENRICHED.put("chat", "Toggle or configure custom chat features and formats.");
        ENRICHED.put("cooldown", "Check active cooldowns for abilities and commands. Usage: /cooldown [ability]");
        ENRICHED.put("profile", "Show your player statistics and progression overview.");
        ENRICHED.put("fakeleave", "Simulate leaving the server (for staff). Staff-only.");
        ENRICHED.put("giveaway", "Give away the item in your hand to a random online player.");
        ENRICHED.put("booster", "Claim or activate personal boosters (money, tokens, nuke).");
        ENRICHED.put("feature", "Open the General Wiki to discover all server features.");
        ENRICHED.put("armor", "Open the Custom Armor GUI to view, equip, or craft armor sets.");
        ENRICHED.put("namemc", "Link your NameMC to receive in-game rewards.");
        ENRICHED.put("dungeon", "Open the Dungeon interface (Crystals). Manage and redeem crystals.");
        ENRICHED.put("seasonday", "Set the season day to today (administrative). Staff-only.");
        ENRICHED.put("discordboost", "Automatically claim Discord Boost rewards.");
        ENRICHED.put("daily", "Open the Daily Rewards GUI and claim streak bonuses.");
        ENRICHED.put("prestige", "Prestige to increase rank and unlock perks. Usage: /prestige [max]");
        ENRICHED.put("autoprestige", "Toggle Auto Prestige to rank up automatically while you mine.");
        ENRICHED.put("autorebirth", "Toggle Auto Rebirth to rebirth automatically when eligible.");
        ENRICHED.put("beaconpoints", "Manage or check Beacon Points used for Beacon pick enchants.");
        ENRICHED.put("anticheat", "AlphaPrison custom AntiCheat admin utilities. Staff-only.");
        ENRICHED.put("test", "Internal testing command. Staff-only.");
        ENRICHED.put("luckyblock", "Open LuckyBlock system to view and redeem LuckyBlock rewards.");
        ENRICHED.put("milestones", "Open Custom Milestones to track and claim progression rewards.");
        ENRICHED.put("class", "Open Class selection and class abilities management.");
        ENRICHED.put("savedata", "Manually trigger a global data save. Staff-only.");
        ENRICHED.put("aligndata", "Align/repair data inconsistencies. Staff-only.");
        ENRICHED.put("voteshop", "Open the Vote Shop and spend vote points.");
        ENRICHED.put("enchanter", "Open the Enchanter to claim free enchants if you are VIP.");
        ENRICHED.put("quest", "Open the Quests GUI to track progress and claim rewards.");
        ENRICHED.put("fixdata", "Fix pickaxe experience for legacy items (no DB). Staff-only.");
        ENRICHED.put("blockmilestone", "Recalculate/claim block milestones for EZBlocks.");
        ENRICHED.put("commandlog", "Open or manage the custom command log. Staff-only.");
        ENRICHED.put("speedlevel", "Manage your Speed effect level. Usage: /speedlevel <1-5>");
        ENRICHED.put("hastelevel", "Manage your Haste effect level. Usage: /hastelevel <1-5>");
        ENRICHED.put("maxprestige", "Shortcut for /prestige max. Immediately prestige to the maximum.");
        ENRICHED.put("fixstuff", "Utility command for staff to fix edge cases. Staff-only.");
        ENRICHED.put("globalbooster", "Activate global money/token boosters (affects entire server). Staff-only.");
        ENRICHED.put("minebomb", "Open Mine Bomb commands and utilities. Usage: /minebomb [give|list|help]");
    }

    public HelpCommand(JavaPlugin plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command cmd, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§cOnly players can use this command!");
            return true;
        }
        Player player = (Player) sender;
        openFor(player, 0);
        return true;
    }

    private void openFor(Player player, int page) {
        List<HelpEntry> entries = collectEntries(player);
        playerEntries.put(player.getUniqueId(), entries);

        int pages = Math.max(1, (int) Math.ceil(entries.size() / (double) pageSize));
        if (page < 0) page = 0;
        if (page >= pages) page = pages - 1;

        String title = titleBase + " §8(" + (page + 1) + "/" + pages + ")";
        Inventory inv = Bukkit.createInventory(null, 54, title);

        // Fill entries
        int start = page * pageSize;
        int end = Math.min(entries.size(), start + pageSize);
        for (int i = start; i < end; i++) {
            HelpEntry e = entries.get(i);
            ItemStack paper = new ItemStack(Material.PAPER);
            ItemMeta meta = paper.getItemMeta();
            meta.setDisplayName("§6/" + e.name);
            List<String> lore = new ArrayList<>();
            if (e.description != null && !e.description.isEmpty()) {
                lore.add("§7" + e.description);
            }
            if (!e.aliases.isEmpty()) {
                lore.add("§8Aliases: §7" + String.join("§8, §7", e.aliases));
            }
            lore.add("");
            lore.add("§eClick to run this command");
            meta.setLore(lore);
            paper.setItemMeta(meta);
            inv.addItem(paper);
        }

        // Navigation controls
        if (pages > 1) {
            if (page > 0) {
                ItemStack prev = new ItemStack(Material.ARROW);
                ItemMeta pm = prev.getItemMeta();
                pm.setDisplayName("§aPrevious Page");
                prev.setItemMeta(pm);
                inv.setItem(45, prev);
            }
            if (page < pages - 1) {
                ItemStack next = new ItemStack(Material.ARROW);
                ItemMeta nm = next.getItemMeta();
                nm.setDisplayName("§aNext Page");
                next.setItemMeta(nm);
                inv.setItem(53, next);
            }
        }

        player.openInventory(inv);
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        Player player = (Player) event.getWhoClicked();
        Inventory inv = event.getInventory();
        if (inv == null || inv.getTitle() == null || StringUtils.isBlank(inv.getTitle())) return;
        String title = ChatColor.stripColor(inv.getTitle());
        if (title.split(" ").length < 2) return;
        if (!ChatColor.stripColor(titleBase).equalsIgnoreCase(title.split(" ") [0] + " " + title.split(" ")[1])) {
            // crude check might fail with variations; use startsWith on stripped
            if (!ChatColor.stripColor(inv.getTitle()).startsWith(ChatColor.stripColor(titleBase))) return;
        }

        event.setCancelled(true);
        if (event.getCurrentItem() == null) return;
        ItemStack item = event.getCurrentItem();
        if (item.getType() == Material.ARROW && item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
            String name = ChatColor.stripColor(item.getItemMeta().getDisplayName()).toLowerCase(Locale.ROOT);
            int currentPage = extractPageIndex(inv.getTitle());
            if (name.contains("previous")) {
                openFor(player, Math.max(0, currentPage - 1));
            } else if (name.contains("next")) {
                openFor(player, currentPage + 1);
            }
            return;
        }
        if (item.getType() == Material.PAPER && item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
            String disp = ChatColor.stripColor(item.getItemMeta().getDisplayName());
            if (disp.startsWith("/")) disp = disp.substring(1);
            String command = disp.split(" ")[0];
            player.closeInventory();
            // Execute the command as the player
            player.performCommand(command);
        }
    }

    private int extractPageIndex(String title) {
        // title format: base (X/Y)
        try {
            int i = title.lastIndexOf('(');
            int j = title.lastIndexOf(')');
            if (i >= 0 && j > i) {
                String inside = ChatColor.stripColor(title.substring(i + 1, j));
                String[] parts = inside.split("/");
                int page = Integer.parseInt(parts[0]) - 1;
                return Math.max(0, page);
            }
        } catch (Exception ignored) {}
        return 0;
    }

    private List<HelpEntry> collectEntries(Player player) {
        PluginDescriptionFile desc = plugin.getDescription();
        Map<String, Map<String, Object>> cmds = desc.getCommands();
        if (cmds == null) return Collections.emptyList();
        List<HelpEntry> result = new ArrayList<>();

        for (Map.Entry<String, Map<String, Object>> e : cmds.entrySet()) {
            String name = e.getKey();
            if (name == null) continue;
            String lower = name.toLowerCase(Locale.ROOT);
            if (lower.equals("help")) continue; // don't include itself

            Map<String, Object> data = e.getValue();
            String baseDescription = data != null && data.containsKey("description") ? String.valueOf(data.get("description")) : "";
            List<String> aliases = new ArrayList<>();
            if (data != null && data.containsKey("aliases")) {
                Object a = data.get("aliases");
                if (a instanceof List) {
                    for (Object o : (List<?>) a) aliases.add(String.valueOf(o));
                } else if (a instanceof String) {
                    aliases.add(String.valueOf(a));
                }
            }

            // Check permissions via Bukkit command API if available
            PluginCommand pc = plugin.getCommand(name);
            if (pc != null) {
                if (!pc.testPermissionSilent(player)) {
                    continue; // player cannot use it according to permission
                }
            }

            // Heuristic blacklist: if player is not op, hide admin-flavored commands
            if (!player.isOp()) {
                if (ADMIN_KEYWORDS.stream().anyMatch(k -> lower.contains(k))) {
                    continue;
                }
                // common explicit admin commands to exclude
                if (Arrays.asList(
                        "givegodshand", "givegodshandlv2", "giverreactionrewards", "savepick", "givepick",
                        "givevoterewards", "givevotepartyrewards", "rebirthpoints", "globalbooster",
                        "fixdata", "aligndata", "savedata", "commandlog"
                ).contains(lower)) {
                    continue;
                }
            }

            String description = ENRICHED.getOrDefault(lower, baseDescription);
            result.add(new HelpEntry(name, description, aliases));
        }

        // Sort alphabetically
        return result.stream()
                .sorted(Comparator.comparing(a -> a.name))
                .collect(Collectors.toList());
    }

    private static class HelpEntry {
        final String name;
        final String description;
        final List<String> aliases;
        HelpEntry(String name, String description, List<String> aliases) {
            this.name = name;
            this.description = description == null ? "" : description;
            this.aliases = aliases == null ? Collections.emptyList() : aliases;
        }
    }
}
