package it.masterzen.commands;

import com.vk2gpz.tokenenchant.api.TokenEnchantAPI;
import it.masterzen.blockbreak.XMaterial;
import net.luckperms.api.LuckPerms;
import net.luckperms.api.cacheddata.CachedMetaData;
import net.luckperms.api.model.user.User;
import net.luckperms.api.node.types.PermissionNode;
import net.milkbowl.vault.economy.Economy;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import it.masterzen.blockbreak.AlphaBlockBreak;
import org.bukkit.inventory.meta.ItemMeta;

import java.awt.*;
import java.util.ArrayList;
import java.util.List;

public class Main implements CommandExecutor, Listener {

    private final XPShop XPShop = new XPShop(AlphaBlockBreak.GetInstance());
    //private final enchantGui enchantGui = new enchantGui(AlphaBlockBreak.GetInstance());

    protected Inventory inventory;
    protected Player player;
    protected static List<Integer> sideSlots = new ArrayList<>();

    private final String pointShop = "§e§lPOINT SHOP §8»§7 ";

    public static void FillBorder(Inventory gui) {
        int size = gui.getSize();
        int rows = size / 9;

        if (rows >= 3) {
            for (int i = 0; i <= 8; i++) {
                if (i % 2 == 0) {
                    gui.setItem(i, XMaterial.LIGHT_BLUE_STAINED_GLASS_PANE.parseItem());
                } else {
                    gui.setItem(i, XMaterial.CYAN_STAINED_GLASS_PANE.parseItem());
                }
                sideSlots.add(i);
            }

            for (int s = 8; s < (gui.getSize() - 9); s += 9) {
                int lastSlot = s + 1;
                //gui.setItem(s, XMaterial.RED_STAINED_GLASS_PANE.parseItem());
                if (lastSlot % 2 == 0) {
                    gui.setItem(lastSlot, XMaterial.LIGHT_BLUE_STAINED_GLASS_PANE.parseItem());
                } else {
                    gui.setItem(lastSlot, XMaterial.CYAN_STAINED_GLASS_PANE.parseItem());
                }

                if (s != 8) {
                    if (s % 2 == 0) {
                        gui.setItem(s, XMaterial.PURPLE_STAINED_GLASS_PANE.parseItem());
                    } else {
                        gui.setItem(s, XMaterial.MAGENTA_STAINED_GLASS_PANE.parseItem());
                    }
                }

                sideSlots.add(s);
                sideSlots.add(lastSlot);
            }

            for (int lr = (gui.getSize() - 9); lr < gui.getSize(); lr++) {
                if (lr % 2 == 0) {
                    gui.setItem(lr, XMaterial.PURPLE_STAINED_GLASS_PANE.parseItem());
                } else {
                    gui.setItem(lr, XMaterial.MAGENTA_STAINED_GLASS_PANE.parseItem());
                }
                sideSlots.add(lr);
            }
        }
    }

    @Override
    public boolean onCommand(CommandSender sender, Command cmd, String label, String[] args) {
        if (sender instanceof Player) {
            Player player = (Player) sender;
            if (cmd.getName().equalsIgnoreCase("XPShop")) {
                XPShop.XPShopInventory(player);
            } else if (cmd.getName().equalsIgnoreCase("BlockShop")) {
                BlockShop.BlockShop(player);
            } else if (cmd.getName().equalsIgnoreCase("spawnerShop")) {
                spawnerShard.spawnerShardGUI(player);
            } else if (cmd.getName().equalsIgnoreCase("pointShop")) {
                if (player.getInventory().getItemInMainHand().getType().equals(Material.STICK) && player.getInventory().getItemInMainHand().hasItemMeta() && (player.getInventory().getItemInMainHand().getItemMeta().getDisplayName().equals("§6§lGOD'S §e§lHAND") || player.getInventory().getItemInMainHand().getItemMeta().getDisplayName().equals("§6§lGOD'S §e§lHAND")) && player.getInventory().getItemInMainHand().getItemMeta().hasLore()) {
                    PointShop.pointshopGui(player);
                } else {
                    player.sendMessage(pointShop + "§cYou must hold the stick !");
                }
            } else if (cmd.getName().equalsIgnoreCase("customTag")) {
                spawnerShard.spawnerShardGUI(player);
            }
        } else {
            sender.sendMessage("§cOnly players can use this command!");
            return true;
        }
        return false;
    }

}
