package it.masterzen.commands;

import com.vk2gpz.tokenenchant.api.TokenEnchantAPI;
import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.blockbreak.XMaterial;
import net.milkbowl.vault.economy.Economy;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

public class spawnerShard implements Listener {

    private static AlphaBlockBreak getVariable;
    private TokenEnchantAPI teAPI;
    private Economy economy;
    private net.luckperms.api.LuckPerms LuckPerms;

    public spawnerShard(AlphaBlockBreak plugin) {
        this.getVariable = plugin;
        economy = getVariable.getEconomy();
        teAPI = getVariable.getTeAPI();
        LuckPerms = getVariable.getLuckPerms();
    }

    private final String prefix = "§e§lSPAWNER SHOP §8»§7 ";
    protected Player player;

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) throws InstantiationException, IllegalAccessException {
        if (event.getClickedInventory() != null && event.getClickedInventory().equals(event.getView().getTopInventory())) {
            if (event.getView().getTitle().equals("§e§lSpawner Shard §f| §7Shop")) {

                player = (Player) event.getWhoClicked();
                ItemStack clickedItem = event.getCurrentItem();

                if (clickedItem == null || event.getSlot() != 20 || event.getSlot() != 22 || event.getSlot() != 24) {
                    event.setCancelled(true);
                }

                if (event.getSlot() == 20) {
                    if (event.isLeftClick()) {
                        giveRandomSpawner(player, false);
                    } else if (event.isRightClick()) {
                        giveRandomSpawner(player, true);
                    }
                } else if (event.getSlot() == 22) {
                    if (event.isLeftClick()) {
                        giveVillagerSpawner(player, false);
                    } else if (event.isRightClick()) {
                        giveVillagerSpawner(player, true);
                    }
                } else if (event.getSlot() == 23) {
                    if (event.isLeftClick()) {
                        giveMoneyNoteSpawner(player, false);
                    } else if (event.isRightClick()) {
                        giveMoneyNoteSpawner(player, true);
                    }
                } else if (event.getSlot() == 24) {
                    if (event.isLeftClick()) {
                        giveTokenNoteSpawner(player, false);
                    } else if (event.isRightClick()) {
                        giveTokenNoteSpawner(player, true);
                    }
                }
            }
        }
    }

    public void giveRandomSpawner(Player player, boolean maxUse) {
        if (getVariable.getSpawnerShard(player) >= 5000) {
            int iterations = 1;
            if (maxUse) {
                iterations = 9999;
            }
            Map<String, Integer> spawnersToAdd = new HashMap<>();
            while (getVariable.getSpawnerShard(player) >= 5000 && iterations > 0) {
                int chance = ThreadLocalRandom.current().nextInt(4);

                if (chance == 0) {
                    if (spawnersToAdd.containsKey("Silverfish")) {
                        spawnersToAdd.put("Silverfish", spawnersToAdd.get("Silverfish") + 1);
                    } else {
                        spawnersToAdd.put("Silverfish", 1);
                    }
                } else if (chance == 1) {
                    if (spawnersToAdd.containsKey("Villager")) {
                        spawnersToAdd.put("Villager", spawnersToAdd.get("Villager") + 1);
                    } else {
                        spawnersToAdd.put("Villager", 1);
                    }
                } else if (chance == 2) {
                    if (spawnersToAdd.containsKey("Bat")) {
                        spawnersToAdd.put("Bat", spawnersToAdd.get("Bat") + 1);
                    } else {
                        spawnersToAdd.put("Bat", 1);
                    }
                } else {
                    if (spawnersToAdd.containsKey("Parrot")) {
                        spawnersToAdd.put("Parrot", spawnersToAdd.get("Parrot") + 1);
                    } else {
                        spawnersToAdd.put("Parrot", 1);
                    }
                }

                getVariable.setSpawnerShard(player, getVariable.getSpawnerShard(player) - 5000);
                iterations--;
            }

            for (String spawner : spawnersToAdd.keySet()) {
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "stacker give " + player.getName() + " spawner " + spawner + " " + spawnersToAdd.get(spawner));
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough crystals !");
        }
    }

    public void giveVillagerSpawner(Player player, boolean maxUse) {
        if (getVariable.getSpawnerShard(player) >= 7500) {
            int iterations = 1;
            int rewardsToAdd = 0;
            if (maxUse) {
                iterations = 9999;
            }
            while (getVariable.getSpawnerShard(player) >= 7500 && iterations > 0) {
                rewardsToAdd++;
                getVariable.setSpawnerShard(player, getVariable.getSpawnerShard(player) - 7500);
                iterations--;
            }

            if (rewardsToAdd > 0) {
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "stacker give " + player.getName() + " spawner Villager " + rewardsToAdd);
                player.sendMessage(prefix + "You received " + rewardsToAdd + "x §aVillager §7Spawner");
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough crystals !");
        }
    }

    public void giveMoneyNoteSpawner(Player player) {
        if (getVariable.getSpawnerShard(player) >= 7500) {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "stacker give " + player.getName() + " spawner Bat 1");
            player.sendMessage(prefix + "You received 1x §aMoney Note §7Spawner");
            getVariable.setSpawnerShard(player, getVariable.getSpawnerShard(player) - 7500);
        } else {
            player.sendMessage(prefix + "§cYou don't have enough Shards !");
        }
    }

    public void giveMoneyNoteSpawner(Player player, boolean maxUse) {
        if (getVariable.getSpawnerShard(player) >= 7500) {
            int iterations = 1;
            int rewardsToAdd = 0;
            if (maxUse) {
                iterations = 9999;
            }
            while (getVariable.getSpawnerShard(player) >= 7500 && iterations > 0) {
                rewardsToAdd++;
                getVariable.setSpawnerShard(player, getVariable.getSpawnerShard(player) - 7500);
                iterations--;
            }

            if (rewardsToAdd > 0) {
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "stacker give " + player.getName() + " spawner Bat " + rewardsToAdd);
                player.sendMessage(prefix + "You received " + rewardsToAdd + "x §aMoney Note §7Spawner");
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough crystals !");
        }
    }

    public void giveTokenNoteSpawner(Player player, boolean maxUse) {
        if (getVariable.getSpawnerShard(player) >= 7500) {
            int iterations = 1;
            int rewardsToAdd = 0;
            if (maxUse) {
                iterations = 9999;
            }
            while (getVariable.getSpawnerShard(player) >= 7500 && iterations > 0) {
                rewardsToAdd++;
                getVariable.setSpawnerShard(player, getVariable.getSpawnerShard(player) - 7500);
                iterations--;
            }

            if (rewardsToAdd > 0) {
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "stacker give " + player.getName() + " spawner Parrot " + rewardsToAdd);
                player.sendMessage(prefix + "You received " + rewardsToAdd + "x §aToken Note §7Spawner");
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough crystals !");
        }
    }

    public static void spawnerShardGUI(Player player) {
        Inventory gui = Bukkit.createInventory(null, 45, "§e§lSpawner Shard §f| §7Shop");
        Main.FillBorder(gui);
        player.updateInventory();

        ItemMeta meta;

        List<String> Lore = new ArrayList<>();
        ItemStack randomSpawner = XMaterial.SPAWNER.parseItem();
        assert randomSpawner != null;
        meta = randomSpawner.getItemMeta();
        meta.setDisplayName("§6§lRANDOM §f| §eSpawner");
        Lore.add("");
        Lore.add("§e§lPRICE");
        Lore.add("§e| §e5.000 §fShards");
        Lore.add("§e| §fBalance: §e" + getVariable.getSpawnerShard(player) + " §7Shards");
        Lore.add("");
        Lore.add("§7§lPOSSIBLE REWARDS");
        Lore.add("§7| §fSilverfish Spawner");
        Lore.add("§7| §fMobCoins Spawner");
        Lore.add("§7| §fMoney Note Spawner");
        Lore.add("§7| §fToken Note Spawner");
        Lore.add("");
        Lore.add("§6§lUSAGE");
        Lore.add("§6| §fLeft click: 1x");
        Lore.add("§6| §fRight click: Max Use");
        Lore.add("");
        meta.setLore(Lore);
        randomSpawner.setItemMeta(meta);

        Lore = new ArrayList<>();
        ItemStack villagerSpawner = XMaterial.SPAWNER.parseItem();
        assert villagerSpawner != null;
        meta = villagerSpawner.getItemMeta();
        meta.setDisplayName("§6§lMOBCOINS §f| §eSpawner");
        Lore.add("");
        Lore.add("§e§lPRICE");
        Lore.add("§e| §e7.500 §fShards");
        Lore.add("§e| §fBalance: §e" + getVariable.getSpawnerShard(player) + " §7Shards");
        Lore.add("");
        Lore.add("§6§lUSAGE");
        Lore.add("§6| §fLeft click: 1x");
        Lore.add("§6| §fRight click: Max Use");
        Lore.add("");
        meta.setLore(Lore);
        villagerSpawner.setItemMeta(meta);

        Lore = new ArrayList<>();
        ItemStack moneyNoteSpawner = XMaterial.SPAWNER.parseItem();
        assert moneyNoteSpawner != null;
        meta = moneyNoteSpawner.getItemMeta();
        meta.setDisplayName("§6§lMONEY NOTE §f| §eSpawner");
        Lore.add("");
        Lore.add("§e§lPRICE");
        Lore.add("§e| §e7.500 §fShards");
        Lore.add("§e| §fBalance: §e" + getVariable.getSpawnerShard(player) + " §7Shards");
        Lore.add("");
        Lore.add("§6§lUSAGE");
        Lore.add("§6| §fLeft click: 1x");
        Lore.add("§6| §fRight click: Max Use");
        Lore.add("");
        meta.setLore(Lore);
        moneyNoteSpawner.setItemMeta(meta);

        Lore = new ArrayList<>();
        ItemStack tokenNoteSpawner = XMaterial.SPAWNER.parseItem();
        assert tokenNoteSpawner != null;
        meta = tokenNoteSpawner.getItemMeta();
        meta.setDisplayName("§6§lTOKEN NOTE §f| §eSpawner");
        Lore.add("");
        Lore.add("§e§lPRICE");
        Lore.add("§e| §e7.500 §fShards");
        Lore.add("§e| §fBalance: §e" + getVariable.getSpawnerShard(player) + " §7Shards");
        Lore.add("");
        Lore.add("§6§lUSAGE");
        Lore.add("§6| §fLeft click: 1x");
        Lore.add("§6| §fRight click: Max Use");
        Lore.add("");
        meta.setLore(Lore);
        tokenNoteSpawner.setItemMeta(meta);

        gui.setItem(20, randomSpawner);
        gui.setItem(22, villagerSpawner);
        gui.setItem(23, moneyNoteSpawner);
        gui.setItem(24, tokenNoteSpawner);

        player.openInventory(gui);
    }

}
