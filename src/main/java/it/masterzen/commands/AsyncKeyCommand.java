package it.masterzen.commands;

import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.blockbreak.AsyncKeyOpeningManager;
import it.masterzen.blockbreak.KeyOpeningPoolStats;
import it.masterzen.blockbreak.KeyOpeningTask;
import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

/**
 * Admin command for managing async key opening system.
 * Provides monitoring, configuration, and control capabilities.
 */
public class AsyncKeyCommand implements CommandExecutor {

    private final AlphaBlockBreak plugin;
    private final String prefix = "§e§lASYNC KEYS §8»§7 ";

    public AsyncKeyCommand(AlphaBlockBreak plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!sender.hasPermission("asynckeys.admin")) {
            sender.sendMessage(prefix + "§cYou don't have permission to use this command.");
            return true;
        }

        if (args.length == 0) {
            sendHelp(sender);
            return true;
        }

        AsyncKeyOpeningManager manager = plugin.getAsyncKeyOpeningManager();
        if (manager == null) {
            sender.sendMessage(prefix + "§cAsync key opening system is not initialized.");
            return true;
        }

        switch (args[0].toLowerCase()) {
            case "stats":
                showStats(sender, manager);
                break;

            case "active":
                showActiveTasks(sender, manager);
                break;

            case "cancel":
                if (args.length < 2) {
                    sender.sendMessage(prefix + "§cUsage: /asynckeys cancel <player>");
                    return true;
                }
                cancelPlayerTask(sender, manager, args[1]);
                break;

            case "config":
                showConfig(sender, manager);
                break;

            case "reload":
                reloadConfig(sender, manager);
                break;

            case "test":
                if (args.length < 4) {
                    sender.sendMessage(prefix + "§cUsage: /asynckeys test <player> <keyType> <amount>");
                    return true;
                }
                testKeyOpening(sender, args[1], args[2], args[3]);
                break;

            default:
                sendHelp(sender);
                break;
        }

        return true;
    }

    private void sendHelp(CommandSender sender) {
        sender.sendMessage("§e§lAsync Keys Admin Commands:");
        sender.sendMessage("§7/asynckeys stats §8- §fShow thread pool statistics");
        sender.sendMessage("§7/asynckeys active §8- §fShow active key opening tasks");
        sender.sendMessage("§7/asynckeys cancel <player> §8- §fCancel a player's key opening");
        sender.sendMessage("§7/asynckeys config §8- §fShow current configuration");
        sender.sendMessage("§7/asynckeys reload §8- §fReload configuration");
        sender.sendMessage("§7/asynckeys test <player> <type> <amount> §8- §fTest async key opening");
    }

    private void showStats(CommandSender sender, AsyncKeyOpeningManager manager) {
        KeyOpeningPoolStats stats = manager.getPoolStats();

        sender.sendMessage("§e§lAsync Key Opening Statistics:");
        sender.sendMessage("§7Thread Pool:");
        sender.sendMessage("  §fActive Threads: §a" + stats.getActiveThreads() + "§7/§a" + stats.getMaxThreads() +
                          " §7(§a" + String.format("%.1f", stats.getThreadUtilization()) + "%§7)");
        sender.sendMessage("  §fQueue Size: §a" + stats.getQueueSize() + "§7/§a" + stats.getMaxQueueSize() +
                          " §7(§a" + String.format("%.1f", stats.getQueueUtilization()) + "%§7)");
        sender.sendMessage("§7Tasks:");
        sender.sendMessage("  §fSubmitted: §a" + stats.getTasksSubmitted());
        sender.sendMessage("  §fCompleted: §a" + stats.getTasksCompleted() +
                          " §7(§a" + String.format("%.1f", stats.getCompletionRate()) + "%§7)");
        sender.sendMessage("  §fRejected: §c" + stats.getTasksRejected() +
                          " §7(§c" + String.format("%.1f", stats.getRejectionRate()) + "%§7)");
        sender.sendMessage("  §fAvg Processing Time: §a" + stats.getAvgProcessingTimeMs() + "ms");
        sender.sendMessage("§7Active Operations: §a" + manager.getActiveTaskCount());
    }

    private void showActiveTasks(CommandSender sender, AsyncKeyOpeningManager manager) {
        sender.sendMessage("§e§lActive Key Opening Tasks:");

        if (manager.getActiveTaskCount() == 0) {
            sender.sendMessage("§7No active tasks.");
            return;
        }

        for (Player player : Bukkit.getOnlinePlayers()) {
            if (manager.hasActiveKeyOpening(player)) {
                KeyOpeningTask task = manager.getActiveTask(player);
                if (task != null) {
                    int progress = task.getProcessedKeys();
                    int total = task.getTotalKeys();
                    double percentage = total > 0 ? (double) progress / total * 100 : 0;

                    sender.sendMessage("  §f" + player.getName() + "§7: §a" + progress + "§7/§a" + total +
                                     " §7" + task.getKeyType() + " keys §7(§a" + String.format("%.1f", percentage) + "%§7)");
                }
            }
        }
    }

    private void cancelPlayerTask(CommandSender sender, AsyncKeyOpeningManager manager, String playerName) {
        Player target = Bukkit.getPlayer(playerName);
        if (target == null) {
            sender.sendMessage(prefix + "§cPlayer not found: " + playerName);
            return;
        }

        if (manager.cancelKeyOpening(target)) {
            sender.sendMessage(prefix + "§aCancelled key opening for " + target.getName());
        } else {
            sender.sendMessage(prefix + "§c" + target.getName() + " doesn't have an active key opening operation.");
        }
    }

    private void showConfig(CommandSender sender, AsyncKeyOpeningManager manager) {
        sender.sendMessage("§e§lAsync Key Opening Configuration:");
        sender.sendMessage("§7Threading:");
        sender.sendMessage("  §fEnabled: " + (manager.getConfig().isAsyncEnabled() ? "§aYes" : "§cNo"));
        sender.sendMessage("  §fCore Pool Size: §a" + manager.getConfig().getCorePoolSize());
        sender.sendMessage("  §fMax Pool Size: §a" + manager.getConfig().getMaximumPoolSize());
        sender.sendMessage("  §fQueue Capacity: §a" + manager.getConfig().getQueueCapacity());
        sender.sendMessage("§7Batch Processing:");
        sender.sendMessage("  §fBatch Size: §a" + manager.getConfig().getBatchSize());
        sender.sendMessage("  §fBatch Delay: §a" + manager.getConfig().getBatchDelay() + "ms");
        sender.sendMessage("  §fMax Concurrent Batches: §a" + manager.getConfig().getMaxConcurrentBatches());
        sender.sendMessage("§7Rate Limiting:");
        sender.sendMessage("  §fEnabled: " + (manager.getConfig().isRateLimitingEnabled() ? "§aYes" : "§cNo"));
        sender.sendMessage("  §fMax Keys/Second: §a" + manager.getConfig().getMaxKeysPerSecond());
        sender.sendMessage("  §fMax Keys/Player: §a" + manager.getConfig().getMaxKeysPerPlayer());
        sender.sendMessage("§7Progress:");
        sender.sendMessage("  §fEnabled: " + (manager.getConfig().isProgressFeedbackEnabled() ? "§aYes" : "§cNo"));
        sender.sendMessage("  §fMin Keys for Progress: §a" + manager.getConfig().getMinKeysForProgress());
    }

    private void reloadConfig(CommandSender sender, AsyncKeyOpeningManager manager) {
        try {
            manager.getConfig().loadConfiguration();
            sender.sendMessage(prefix + "§aConfiguration reloaded successfully.");
        } catch (Exception e) {
            sender.sendMessage(prefix + "§cFailed to reload configuration: " + e.getMessage());
            plugin.getLogger().warning("Failed to reload async key opening configuration: " + e.getMessage());
        }
    }

    private void testKeyOpening(CommandSender sender, String playerName, String keyType, String amountStr) {
        Player target = Bukkit.getPlayer(playerName);
        if (target == null) {
            sender.sendMessage(prefix + "§cPlayer not found: " + playerName);
            return;
        }

        int amount;
        try {
            amount = Integer.parseInt(amountStr);
            if (amount <= 0 || amount > 100000) {
                sender.sendMessage(prefix + "§cAmount must be between 1 and 100,000");
                return;
            }
        } catch (NumberFormatException e) {
            sender.sendMessage(prefix + "§cInvalid amount: " + amountStr);
            return;
        }

        // Test the async key opening
        plugin.getKeysManager().openKeys(target, keyType, amount, true);
        sender.sendMessage(prefix + "§aStarted test key opening for " + target.getName() +
                          " (§f" + amount + " " + keyType + " keys§a)");
    }
}