package it.masterzen.commands;

import com.vk2gpz.tokenenchant.api.TokenEnchantAPI;
import it.masterzen.MongoDB.PlayerData;
import it.masterzen.Resume.Resume;
import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.blockbreak.XMaterial;
import net.luckperms.api.cacheddata.CachedMetaData;
import net.luckperms.api.model.user.User;
import net.luckperms.api.node.Node;
import net.milkbowl.vault.economy.Economy;
import org.apache.commons.lang3.time.DateUtils;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;

public class PointShop implements Listener {

    private static AlphaBlockBreak getVariable;
    private TokenEnchantAPI teAPI;
    private Economy economy;
    private net.luckperms.api.LuckPerms LuckPerms;
    private Resume resume;

    public PointShop(AlphaBlockBreak plugin) {
        this.getVariable = plugin;
        economy = getVariable.getEconomy();
        teAPI = getVariable.getTeAPI();
        LuckPerms = getVariable.getLuckPerms();
        resume = getVariable.getResume();
    }

    private final String prefix = "§e§lPOINTS SHOP §8»§7 ";

    //private static int prestigeLevel = 1;

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) throws InstantiationException, IllegalAccessException {
        if (event.getClickedInventory() != null && event.getClickedInventory().equals(event.getView().getTopInventory())) {
            if (event.getView().getTitle().equals("§e§lPOINTS §f| §7Shop")) {
                Player player = (Player) event.getWhoClicked();
                event.setCancelled(true);

                if (event.getSlot() == 21) {
                    giveMoneyBooster(player);
                } else if (event.getSlot() == 23) {
                    giveTokenBooster(player);
                }
            }
        }
    }

    public static boolean checkExchange(Player player) {
        String tmp;
        ArrayList<String> tmpLore = new ArrayList<>();
        int points = 0;
        boolean canExchange = false;

        ItemMeta tmpItemMeta = player.getInventory().getItemInMainHand().getItemMeta();
        for (String line : player.getInventory().getItemInMainHand().getItemMeta().getLore()) {
            if (line.contains("POINTS")) {
                tmp = ChatColor.stripColor(line);
                tmp = tmp.replace("POINTS » ", "");
                points = Integer.parseInt(tmp);
                if (points > 1000) {
                    tmpLore.add(line.replace(" " + points, " " + (points - 1000)));
                    canExchange = true;
                } else {
                    tmpLore.add(line);
                }
                break;
            } else {
                tmpLore.add(line);
            }
        }
        tmpItemMeta.setLore(tmpLore);
        player.getInventory().getItemInMainHand().setItemMeta(tmpItemMeta);

        return canExchange;
    }

    public void giveRandomReward(Player player) {
        boolean canExchange = false;

        canExchange = checkExchange(player);
        long prestigeLevel = getPrestigeLevel(player);

        if (canExchange) {
            int chance = ThreadLocalRandom.current().nextInt(100);

            if (chance <= 10) {
                giveRandomSpawner(player);
            } else if (chance <= 25) {
                chance = ThreadLocalRandom.current().nextInt(2);
                if (chance == 0) {
                    giveMoney(player, prestigeLevel);
                } else {
                    giveTokens(player, prestigeLevel);
                }
            } else {
                chance = ThreadLocalRandom.current().nextInt(2);
                if (chance == 0) {
                    giveXP(player);
                }/* else if (chance == 1) {
                    giveKeys(player);
                }*/ else {
                    giveRobots(player);
                }
            }

            pointshopGui(player);
        } else {
            player.sendMessage(prefix + "§cYou need at least 1000 Points !");
        }
    }

    public long getPrestigeLevel(Player player) {
        String tmp;
        long prestigeLevel = 1;

        CachedMetaData metaData = getVariable.getLuckPerms().getPlayerAdapter(Player.class).getMetaData(player);
        String prefix = metaData.getPrefix();
        assert prefix != null;
        if (prefix.contains("Lv")) {
            tmp = prefix.replace("§bLv ", "");
            tmp = tmp.replace(" ", "");
            tmp = ChatColor.stripColor(tmp);
            prestigeLevel = Long.parseLong(tmp);
        }

        return prestigeLevel;
    }

    public void giveMoney(Player player, long prestigeLevel) {
        getVariable.getEconomy().depositPlayer(player, getMoney(prestigeLevel));
        resume.addValue(player, "Money", getMoney(prestigeLevel));
        player.sendMessage(prefix + "You received §a§l" + getVariable.newFormatNumber(getMoney(prestigeLevel)) + " §7Money");
    }

    public void giveTokens(Player player, long prestigeLevel) {
        getVariable.getTeAPI().addTokens(player, getTokens(prestigeLevel));
        resume.addValue(player, "Tokens", getTokens(prestigeLevel));
        player.sendMessage(prefix + "You received §a§l" + getVariable.newFormatNumber(getTokens(prestigeLevel)) + " §7Tokens");
    }

    public void giveKeys(Player player) {
        int chance = ThreadLocalRandom.current().nextInt(4);

        if (chance == 0) {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "adminkey give " + player.getName() + " Mine 5000");
            player.sendMessage(prefix + "You received §a§l5000 §7Mine Keys");
        } else if (chance == 1) {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "adminkey give " + player.getName() + " Rare 2500");
            player.sendMessage(prefix + "You received §a§l2500 §7Rare Keys");
        } else if (chance == 2) {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "adminkey give " + player.getName() + " Legendary 1000");
            player.sendMessage(prefix + "You received §a§l1000 §7Legendary Keys");
        } else {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "cc give p Alpha 5 " + player.getName());
            player.sendMessage(prefix + "You received §a§l5 §7Alpha Keys");
        }
    }

    public void giveXP(Player player) {
        double xp = 5000000 + ThreadLocalRandom.current().nextInt(5000000);
        player.giveExp((int) xp);
        player.sendMessage(prefix + "You received §a§l" + getVariable.newFormatNumber(xp) + " §7XP");
    }

    public void giveRandomSpawner(Player player) {
        int chance = ThreadLocalRandom.current().nextInt(4);

        if (chance == 0) {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "stacker give " + player.getName() + " spawner Silverfish 1");
            player.sendMessage(prefix + "You received 1x §aSilverfish §7Spawner");
        } else if (chance == 1) {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "stacker give " + player.getName() + " spawner Villager 1");
            player.sendMessage(prefix + "You received 1x §aVillager §7Spawner");
        } else if (chance == 2) {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "stacker give " + player.getName() + " spawner Bat 1");
            player.sendMessage(prefix + "You received 1x §aMoney Note §7Spawner");
        } else {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "stacker give " + player.getName() + " spawner Parrot 1");
            player.sendMessage(prefix + "You received 1x §aToken Note §7Spawner");
        }
    }

    public void giveRobots(Player player) {
        int robots = (1 + ThreadLocalRandom.current().nextInt(100));
        //AlphaBlockBreak.GetInstance().getRobotSystem().addPoints(player, robots); DA SISTEMARE
        //Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "robotadmin give " + player.getName() + " " + robots);
        player.sendMessage(prefix + "You received §a§l" + robots + " §7Robots");
    }

    public static double getMoney(long prestigeLevel) {
        double baseValue = 500000000000L;
        return (prestigeLevel * baseValue);
    }

    public static double getTokens(long prestigeLevel) {
        double baseValue = 50000L;
        return (prestigeLevel * baseValue);
    }

    public void giveMoneyBooster(Player player) {
        boolean canExchange = false;

        PlayerData data = getVariable.getMongoReader().getPlayerData(player.getUniqueId());
        if (data.getGodHandLastClaimDate() == null || new Date().after(DateUtils.addMinutes(data.getGodHandLastClaimDate(), 15))) {
            canExchange = checkExchange(player);
            if (canExchange) {
                data.setKeysMoneyBooster((data.getKeysMoneyBooster() == null ? 0 : data.getKeysMoneyBooster()) + 5);
                getVariable.getMongoReader().setPlayerMoneyBoosterInMap(player, data.getKeysMoneyBooster());
                data.setGodHandLastClaimDate(new Date());
                player.sendMessage(prefix + "You received a §f5% Money Booster");
                pointshopGui(player);
            }
        } else {
            player.sendMessage(prefix + "§cYou can claim the booster every §l15 §cminutes");
            player.sendMessage("§7Last claim date: §f" + data.getGodHandLastClaimDate());
        }
    }

    public void giveTokenBooster(Player player) {
        boolean canExchange = false;

        PlayerData data = getVariable.getMongoReader().getPlayerData(player.getUniqueId());
        if (data.getGodHandLastClaimDate() == null || new Date().after(DateUtils.addMinutes(data.getGodHandLastClaimDate(), 15))) {
            canExchange = checkExchange(player);
            if (canExchange) {
                data.setKeysTokenBooster((data.getKeysTokenBooster() == null ? 0 : data.getKeysTokenBooster()) + 2.5);
                getVariable.getMongoReader().setPlayerTokenBoosterInMap(player, data.getKeysTokenBooster());
                data.setGodHandLastClaimDate(new Date());
                player.sendMessage(prefix + "You received a §f2.5% Token Booster");
                pointshopGui(player);
            }
        } else {
            player.sendMessage(prefix + "§cYou can claim the booster every §l15 §cminutes");
            player.sendMessage(prefix + "Last claim date: §f" + data.getGodHandLastClaimDate());
        }
    }

    public static void pointshopGui(Player player) {
        Inventory gui = Bukkit.createInventory(null, 45, "§e§lPOINTS §f| §7Shop");
        Main.FillBorder(gui);
        player.updateInventory();

        ItemMeta meta;
        String tmp;
        int points = 0;
        long prestigeLevel = 1;

        if (player.getInventory().getItemInMainHand().getItemMeta().hasLore()) {
            for (String line : player.getInventory().getItemInMainHand().getItemMeta().getLore()) {
                if (line.contains("POINTS")) {
                    tmp = ChatColor.stripColor(line);
                    tmp = tmp.replace("POINTS » ", "");
                    points = Integer.parseInt(tmp);
                    break;
                }
            }
        }

        //User user = getVariable.getLuckPerms().getPlayerAdapter(Player.class).getUser(player);
        CachedMetaData metaData = getVariable.getLuckPerms().getPlayerAdapter(Player.class).getMetaData(player);
        String prefix = metaData.getPrefix();
        assert prefix != null;
        if (prefix.contains("Lv")) {
            tmp = prefix.replace("§bLv ", "");
            tmp = tmp.replace(" ", "");
            tmp = ChatColor.stripColor(tmp);
            prestigeLevel = Long.parseLong(tmp);
        }

        List<String> lore = new ArrayList<>();
        ItemStack moneyBoost = new ItemStack(Material.GOLD_INGOT);
        meta = moneyBoost.getItemMeta();
        meta.setDisplayName("§e§lMONEY §7Booster");
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e1.000 §fPoints");
        lore.add("§e| §fBalance: §e" + points + " §fPoints");
        lore.add("");
        lore.add("§7§lREWARD");
        lore.add("§7| §f5% Money §7booster");
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §7This booster stacks with the keys booster (/boosters).");
        lore.add("§7| §7You can claim a reward every §f15 §7minutes");
        lore.add("");
        meta.setLore(lore);
        moneyBoost.setItemMeta(meta);

        lore = new ArrayList<>();
        ItemStack tokenBoost = new ItemStack(Material.MAGMA_CREAM);
        meta = tokenBoost.getItemMeta();
        meta.setDisplayName("§a§lTOKEN §7Booster");
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e1.000 §fPoints");
        lore.add("§e| §fBalance: §e" + points + " §fPoints");
        lore.add("");
        lore.add("§7§lREWARD");
        lore.add("§7| §f2.5% Token §7booster");
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §7This booster stacks with the keys booster (/boosters).");
        lore.add("§7| §7You can claim a reward every §f15 §7minutes");
        lore.add("");
        meta.setLore(lore);
        tokenBoost.setItemMeta(meta);

        gui.setItem(21, moneyBoost);
        gui.setItem(23, tokenBoost);

        player.openInventory(gui);
        //animatedGui(player, gui, meta);
    }

    public static void animatedGui(Player player, Inventory gui, ItemMeta meta) {
        new BukkitRunnable() {
            @Override
            public void run() {
                if (player.isOnline() && player.getOpenInventory() != null && player.getOpenInventory().getTitle().equals("§e§lPOINTS §f| §7Shop")) {
                    Random random = new Random();
                    ItemStack randomReward;
                    Material tmpGuiMaterial = gui.getItem(22).getType();

                    if (tmpGuiMaterial.equals(Material.GOLD_NUGGET)) {
                        randomReward = XMaterial.MAGMA_CREAM.parseItem();
                        assert randomReward != null;
                        randomReward.setItemMeta(meta);
                        gui.setItem(22, randomReward);
                    } else if (tmpGuiMaterial.equals(Material.MAGMA_CREAM)) {
                        randomReward = XMaterial.EXPERIENCE_BOTTLE.parseItem();
                        assert randomReward != null;
                        randomReward.setItemMeta(meta);
                        gui.setItem(22, randomReward);
                    }/* else if (tmpGuiMaterial.equals(Material.TRIPWIRE_HOOK)) {
                        randomReward = XMaterial.EXPERIENCE_BOTTLE.parseItem();
                        randomReward.setItemMeta(meta);
                        gui.setItem(22, randomReward);
                    }*/ else if (tmpGuiMaterial.equals(XMaterial.EXPERIENCE_BOTTLE.parseMaterial())) {
                        randomReward = XMaterial.SPAWNER.parseItem();
                        assert randomReward != null;
                        randomReward.setItemMeta(meta);
                        gui.setItem(22, randomReward);
                    } else if (tmpGuiMaterial.equals(XMaterial.SPAWNER.parseMaterial())) {
                        randomReward = XMaterial.ZOMBIE_SPAWN_EGG.parseItem();
                        assert randomReward != null;
                        randomReward.setItemMeta(meta);
                        gui.setItem(22, randomReward);
                    } else if (tmpGuiMaterial.equals(XMaterial.ZOMBIE_SPAWN_EGG.parseMaterial())) {
                        randomReward = XMaterial.GOLD_NUGGET.parseItem();
                        assert randomReward != null;
                        randomReward.setItemMeta(meta);
                        gui.setItem(22, randomReward);
                    }
                } else {
                    this.cancel();
                }
            }
        }.runTaskTimer(getVariable, 20L, 20L);
    }

}
