package it.masterzen.commands;

import com.vk2gpz.tokenenchant.api.TokenEnchantAPI;
import it.masterzen.blockbreak.AlphaBlockBreak;
import net.milkbowl.vault.economy.Economy;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;

public class CustomTag {

    private static AlphaBlockBreak getVariable;
    private TokenEnchantAPI teAPI;
    private Economy economy;
    private net.luckperms.api.LuckPerms LuckPerms;

    public CustomTag(AlphaBlockBreak plugin) {
        this.getVariable = plugin;
        economy = getVariable.getEconomy();
        teAPI = getVariable.getTeAPI();
        LuckPerms = getVariable.getLuckPerms();
    }

    private final String prefix = "§e§lSPAWNER SHOP §8»§7 ";
    protected Player player;

    public static void changeCustomTag(Player player, String newTagDesc) {

    }
}
