package it.masterzen.commands;

import com.vk2gpz.tokenenchant.api.TokenEnchantAPI;
import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.blockbreak.XMaterial;
import net.milkbowl.vault.economy.Economy;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;

public class BlockShop implements Listener {

    private AlphaBlockBreak getVariable;
    private final String prefix = "§e§lBLOCKSHOP §8»§7 ";
    protected Player player;

    //@EventHandler
    //public void onInventoryClick(InventoryClickEvent event) throws InstantiationException, IllegalAccessException {
    //    if (event.getView().getTitle().equals("§e§lBLOCK §f| §7Shop")) {
//
    //        player = (Player) event.getWhoClicked();
    //        ItemStack clickedItem = event.getCurrentItem();
//
    //        if (clickedItem == null || event.getSlot() != 20 || event.getSlot() != 22 || event.getSlot() != 24) {
    //            event.setCancelled(true);
    //        }
//
    //        if (event.getSlot() == 20) {
    //            giveSpawner(player);
    //        } else if (event.getSlot() == 24) {
    //            //giveVip(player);
    //        }
    //    }
    //}

    public BlockShop(AlphaBlockBreak plugin) {
        this.getVariable = plugin;
        economy = getVariable.getEconomy();
        teAPI = getVariable.getTeAPI();
        LuckPerms = getVariable.getLuckPerms();
    }

    private TokenEnchantAPI teAPI;
    private Economy economy;
    private net.luckperms.api.LuckPerms LuckPerms;

    public static void BlockShop(Player player) {
        Inventory gui = Bukkit.createInventory(null, 45, "§e§lBLOCK §f| §7Shop");
        Main.FillBorder(gui);

        ItemMeta meta;

        List<String> Lore = new ArrayList<>();
        ItemStack paper = XMaterial.PAPER.parseItem();
        assert paper != null;
        meta = paper.getItemMeta();
        Lore.add("§7§o(( Claim this voucher to");

        if (player.hasPermission("essentials.warps.mvp+")) {
            meta.setDisplayName("§a§lALPHA §f| §lRank Voucher");
            Lore.add("§7§oobtain §d§lALPHA §7§orank ))");
        } else if (player.hasPermission("essentials.warps.mvp")) {
            meta.setDisplayName("§a§lMVP+ §f| §lRank Voucher");
            Lore.add("§7§oobtain §d§lMVP+ §7§orank ))");
        } else if (player.hasPermission("essentials.warps.vip+")) {
            meta.setDisplayName("§a§lMVP §f| §lRank Voucher");
            Lore.add("§7§oobtain §d§lMVP §7§orank ))");
        } else if (player.hasPermission("essentials.warps.vip")) {
            meta.setDisplayName("§a§lVIP+ §f| §lRank Voucher");
            Lore.add("§7§oobtain §d§lVIP+ §7§orank ))");
        } else {
            meta.setDisplayName("§a§lVIP §f| §lRank Voucher");
            Lore.add("§7§oobtain §d§lVIP §7§orank ))");
        }
        Lore.add("");
        Lore.add("§6§l* §eRight click to claim");
        Lore.add("");
        if (player.hasPermission("essentials.warps.mvp+")) {
            Lore.add("§7Price: §c3.000.000 §7Blocks");
        } else if (player.hasPermission("essentials.warps.mvp")) {
            Lore.add("§7Price: §c2.500.000 §7Blocks");
        } else if (player.hasPermission("essentials.warps.vip+")) {
            Lore.add("§7Price: §c2.000.000 §7Blocks");
        } else if (player.hasPermission("essentials.warps.vip")) {
            Lore.add("§7Price: §c1.500.000 §7Blocks");
        } else {
            Lore.add("§7Price: §c1.000.000 §7Blocks");
        }
        meta.setLore(Lore);
        paper.setItemMeta(meta);

        Lore = new ArrayList<>();
        ItemStack spawner = XMaterial.SPAWNER.parseItem();
        assert spawner != null;
        meta = spawner.getItemMeta();
        meta.setDisplayName("§6x1 §c§lR§6§lA§e§lI§a§lN§b§lB§9§lO§5§lW §7Spawner - Villager");
        Lore.add("§7Price: §c200.000 §7Blocks");
        Lore.add("");
        Lore.add("§7The Villagers will drop coins for /MobCoin");
        meta.setLore(Lore);
        spawner.setItemMeta(meta);

        Lore = new ArrayList<>();
        ItemStack godHand = XMaterial.SPAWNER.parseItem();
        assert godHand != null;
        meta = godHand.getItemMeta();
        meta.setDisplayName("§6§lGOD'S §e§lHAND");
        Lore.add("§7Price: §c1.500.000 §7Blocks");
        Lore.add("");
        Lore.add("§7This special item will let you explode the mine while right clicking !");
        Lore.add("§7You will gain Points to get Rewards from /PointShop");
        meta.setLore(Lore);
        godHand.setItemMeta(meta);

        gui.setItem(20, spawner);
        gui.setItem(22, godHand);
        gui.setItem(24, paper);

        player.openInventory(gui);
    }

}
