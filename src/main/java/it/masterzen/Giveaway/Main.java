package it.masterzen.Giveaway;

import com.boydti.fawe.bukkit.chat.FancyMessage;
import it.masterzen.Robot.GUI;
import it.masterzen.Robot.Robot;
import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.blockbreak.XMaterial;
import net.md_5.bungee.api.chat.ClickEvent;
import net.md_5.bungee.api.chat.TextComponent;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.OfflinePlayer;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.Listener;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitRunnable;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;

public class Main implements CommandExecutor, Listener {
    private static HashMap<UUID, Robot> points = new HashMap<>();
    private final String prefix = "§e§lGIVEAWAY §8»§7 ";
    private final String name = "Giveaway";

    public final AlphaBlockBreak mainClass;

    private static HashMap<UUID, Giveaway> giveAwayList = new HashMap<>();

    public Main(AlphaBlockBreak plugin) {
        mainClass = plugin;
    }

    public String getPrefix() {
        return this.prefix;
    }

    public String getName() {
        return this.name;
    }

    public void sendUsage(Player player) {
        player.sendMessage("§e§l" + name.toUpperCase() + "§f | §7Command Usage");
        player.sendMessage("    §7/giveaway [minutes] §7§o(from 1 to 5)");
    }

    public void cancelAllGiveaways() {
        for (UUID host : giveAwayList.keySet()) {
            cancelGiveaway(host, true);
        }
    }

    public void cancelGiveaway(UUID host, boolean sendMessage) {
        if (giveAwayList.containsKey(host)) {
            Bukkit.getPlayer(host).getInventory().addItem(giveAwayList.get(host).getItem());
            giveAwayList.remove(host);
            if (sendMessage) {
                Bukkit.broadcastMessage(prefix + "§f" + Bukkit.getPlayer(host).getName() + "§7's Giveaway has been cancelled");
            }
        }
    }

    public void giveItemAway(UUID host) {
        if (giveAwayList.containsKey(host)) {
            if (giveAwayList.get(host).getEntrants().size() > 0) {
                List<UUID> entrants = giveAwayList.get(host).getEntrants();

                UUID winner = entrants.get(ThreadLocalRandom.current().nextInt(entrants.size()));
                Player hostPlayer = Bukkit.getPlayer(host);
                Player winnerPlayer = Bukkit.getPlayer(winner);

                winnerPlayer.getInventory().addItem(giveAwayList.get(host).getItem());
                Bukkit.broadcastMessage(prefix + "§f" + winnerPlayer.getName() + "§7 won §f" + hostPlayer.getName() + "§7's Giveaway");
                giveAwayList.remove(host);
            } else {
                cancelGiveaway(host, false);
                Bukkit.getPlayer(host).sendMessage(prefix + "No one joined your giveaway. It has been cancelled");
            }
        } else {
            mainClass.getLogger().info("§cError on giveItemAway, host not in the list " + host.toString());
        }
    }

    public void startGiveaway(Player player, int minutes) {
        if (!giveAwayList.containsKey(player.getUniqueId())) {
            if (!player.getInventory().getItemInMainHand().getType().equals(XMaterial.DIAMOND_PICKAXE.parseMaterial())) {
                giveAwayList.put(player.getUniqueId(), new Giveaway(player.getInventory().getItemInMainHand()));
                mainClass.removeItemFromPlayer(player, giveAwayList.get(player.getUniqueId()).getItem(), null, false, true, 0);
                Bukkit.broadcastMessage(prefix + player.getName() + " started a Giveaway for §f" + minutes + " minutes §7for the following item");

                //ItemStack item = player.getInventory().getItemInMainHand();
                for (Player tmpPlayer : Bukkit.getOnlinePlayers()) {
                    sendItem(player.getUniqueId(), tmpPlayer);
                    TextComponent message = new TextComponent("§aClick me to join !");
                    message.setClickEvent(new ClickEvent(ClickEvent.Action.RUN_COMMAND, "/giveaway join " + player.getName()));
                    tmpPlayer.spigot().sendMessage(message);
                }

                new BukkitRunnable() {
                    @Override
                    public void run() {
                        giveItemAway(player.getUniqueId());
                    }
                }.runTaskLater(mainClass, (1200L * minutes)); // (1200L * minutes)
            } else {
                player.sendMessage(prefix + "§cYou can't giveaway pickaxes");
            }
        } else {
            player.sendMessage(prefix + "§cYou're already running a giveaway");
        }
    }

    public void sendItem(UUID host, Player player) {
        ItemStack item = giveAwayList.get(host).getItem();

        new FancyMessage((item.hasItemMeta() && item.getItemMeta().hasDisplayName()) ? "    §8[ §f" + item.getItemMeta().getDisplayName() + "§8 ]§7 x" + item.getAmount() : "    §8[ §f" + item.getType() + "§8 ]§7 x" + item.getAmount())
                .color(ChatColor.GOLD)
                .style(ChatColor.UNDERLINE)
                .color(ChatColor.BLUE)
                .itemTooltip(item)
                .send(player);
    }

    public void sendEntrants(UUID host, Player player) {
        if (giveAwayList.containsKey(host)) {
            List<UUID> entrants = new ArrayList<>(giveAwayList.get(host).getEntrants());

            player.sendMessage("§e§lGIVEAWAY §f| §7Entrants §7§o(" + entrants.size() + ")");
            for (UUID entrant : entrants) {
                OfflinePlayer tmpPlayer = Bukkit.getOfflinePlayer(entrant);
                player.sendMessage("    " + tmpPlayer.getName());
            }
            player.sendMessage("");
        } else {
            player.sendMessage(prefix + "§cYou're not hosting any giveaway");
        }
    }

    @Override
    public boolean onCommand(CommandSender sender, Command cmd, String label, String[] args) {
        if (cmd.getName().equalsIgnoreCase("giveaway")) {
            if (sender instanceof Player) {
                Player player = (Player) sender;
                if (args.length == 0) {
                    sendUsage(player);
                } else if (args.length == 1 && player.hasPermission("group.mvp+")) {
                    if (args[0].equalsIgnoreCase("cancel")) {
                        cancelGiveaway(player.getUniqueId(), true);
                    } else if (args[0].equalsIgnoreCase("entrants")) {
                        sendEntrants(player.getUniqueId(), player);
                    } else if (mainClass.isNumber(args[0])) {
                        int minutes = Integer.parseInt(args[0]);
                        if (minutes > 0 && minutes < 6) {
                            if (!player.getInventory().getItemInMainHand().getType().equals(Material.AIR)) {
                                startGiveaway(player, Integer.parseInt(args[0]));
                            } else {
                                player.sendMessage(prefix + "§cYou can't giveaway this item");
                            }
                        } else {
                            sendUsage(player);
                        }
                    } else {
                        sendUsage(player);
                    }
                } else if (args.length == 1 ) {
                    player.sendMessage(prefix + "§cYou need to be at least MVP+");
                } else if (args.length == 2) {
                    if (args[0].equalsIgnoreCase("join")) {
                        OfflinePlayer tmpPlayer = Bukkit.getOfflinePlayer(args[1]);
                        if (giveAwayList.containsKey(tmpPlayer.getUniqueId())) {
                            if (!player.getUniqueId().equals(tmpPlayer.getUniqueId())) {
                                if (!giveAwayList.get(tmpPlayer.getUniqueId()).getEntrants().contains(player.getUniqueId())) {
                                    giveAwayList.get(tmpPlayer.getUniqueId()).addEntrant(player.getUniqueId());
                                    player.sendMessage(prefix + "You joined §a" + tmpPlayer.getName() + "§7's giveaway for the following item");
                                    sendItem(tmpPlayer.getUniqueId(), player);
                                } else {
                                    player.sendMessage(prefix + "§cYou already joined this giveaway");
                                }
                            } else {
                                player.sendMessage(prefix + "§cYou cannot join your giveaway");
                            }
                        } else {
                            player.sendMessage(prefix + "§f" + tmpPlayer.getName() + " §cis not hosting any giveaway");
                        }
                    }
                } else {
                    sendUsage(player);
                }
            }
        }
        return false;
    }

}
