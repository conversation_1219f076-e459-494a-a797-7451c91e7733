package it.masterzen.Giveaway;

import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class Giveaway {

    private ItemStack item;
    private List<UUID> entrants;

    public Giveaway(ItemStack item) {
        this.item = item;
        entrants = new ArrayList<>();
    }

    public ItemStack getItem() {
        return this.item;
    }

    public List<UUID> getEntrants() {
        return this.entrants;
    }

    public void addEntrant(UUID player) {
        if (!this.entrants.contains(player)) {
            this.entrants.add(player);
        }
    }

    public void removeEntrant(UUID player) {
        this.entrants.remove(player);
    }

}
