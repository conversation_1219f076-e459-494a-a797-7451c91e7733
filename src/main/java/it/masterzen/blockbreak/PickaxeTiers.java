package it.masterzen.blockbreak;

import it.masterzen.MongoDB.PlayerData;
import org.apache.commons.lang3.StringUtils;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;

public class PickaxeTiers {

    private final String prefix = "§e§lTIERS §8»§7 ";
    public final AlphaBlockBreak mainClass;
    private static final int MAX_TIER = 4;

    public PickaxeTiers(AlphaBlockBreak plugin) {
        mainClass = plugin;
    }

    public int getXPNeededForTier(int tier) {
        if (tier == 0) {
            return 10000; // 50000
        } else if (tier == 1) {
            return 25000; // 200000
        } else if (tier == 2) {
            return 50000; // 500000
        } else {
            return 100000; // 1000000
        }
    }

    public void addInitialLore(Player player) {
        ItemStack itemInHand = player.getInventory().getItemInMainHand();
        if (itemInHand.hasItemMeta() && itemInHand.getItemMeta().hasLore()) {
            ItemMeta meta = itemInHand.getItemMeta();
            List<String> lore = meta.getLore();

            if (!lore.get(2).contains("Level-UP")) {
                PlayerData data = mainClass.getMongoReader().getPlayerData(player.getUniqueId());
                int currentTier = data.getPickaxeCurrentTier() == null ? 0 : data.getPickaxeCurrentTier();
                int currentXP = data.getPickaxeCurrentXP() == null ? 0 : data.getPickaxeCurrentXP();

                // TOCCA ANCHE SOTTO NEL CODICE !
                List<String> newLore = new ArrayList<>();
                newLore.add("");
                newLore.add(Utils.getCenteredMessage("§7Tier: §f" + currentTier + "/" + MAX_TIER));
                newLore.add(Utils.getCenteredMessage("§7Level-UP Progress"));
                newLore.add(Utils.getCenteredMessage(Utils.getProgressBar(currentXP, getXPNeededForTier(currentTier), 50, '|', "§a", "§7")));
                newLore.add(Utils.getCenteredMessage("§7§o" + currentXP + "/" + getXPNeededForTier(currentTier)));
                newLore.add("");
                newLore.addAll(lore);
                meta.setLore(newLore);
            }
            itemInHand.setItemMeta(meta);
        }
    }

    public void addInitialLoreTmp(Player player) {
        ItemStack itemInHand = player.getInventory().getItemInMainHand();
        if (itemInHand.hasItemMeta() && itemInHand.getItemMeta().hasLore()) {
            ItemMeta meta = itemInHand.getItemMeta();
            List<String> lore = meta.getLore();

            PlayerData data = mainClass.getMongoReader().getPlayerData(player.getUniqueId());
            int currentTier = data.getPickaxeCurrentTier() == null ? 0 : data.getPickaxeCurrentTier();
            int currentXP = data.getPickaxeCurrentXP() == null ? 0 : data.getPickaxeCurrentXP();

            // TOCCA ANCHE SOTTO NEL CODICE !
            List<String> newLore = new ArrayList<>();
            newLore.add("");
            newLore.add(Utils.getCenteredMessage("§7Tier: §f" + currentTier + "/" + MAX_TIER));
            newLore.add(Utils.getCenteredMessage("§7Level-UP Progress"));
            newLore.add(Utils.getCenteredMessage(Utils.getProgressBar(currentXP, getXPNeededForTier(currentTier), 50, '|', "§a", "§7")));
            newLore.add(Utils.getCenteredMessage("§7§o" + currentXP + "/" + getXPNeededForTier(currentTier)));
            newLore.add("");
            newLore.addAll(lore);
            meta.setLore(newLore);
            itemInHand.setItemMeta(meta);
        }
    }

    public void updatePickaxeLevel(Player player, int xpToAdd) {
        ItemStack itemInHand = player.getInventory().getItemInMainHand();
        PlayerData data = mainClass.getMongoReader().getPlayerData(player.getUniqueId());

        //player.sendMessage("object: " + data.toString() + " - " + data);

        if (itemInHand.hasItemMeta() && itemInHand.getItemMeta().hasLore()) {
            int currentTier = data.getPickaxeCurrentTier() == null ? 0 : data.getPickaxeCurrentTier();
            int currentXP = data.getPickaxeCurrentXP() == null ? 0 : data.getPickaxeCurrentXP();
            if (currentTier < MAX_TIER) {
                int xpNeededToLevelUp = getXPNeededForTier(currentTier);
                if (currentXP + xpToAdd >= xpNeededToLevelUp) {
                    currentTier++;
                    player.sendTitle("§e§lPICKAXE TIER", "§7§oTier " + currentTier + " reached !", 5, 80, 5);
                    data.addPickaxeCurrentTier(1);
                }

                // Update database
                //data.setPickaxeCurrentTier(currentTier);
                data.addPickaxeCurrentXP(xpToAdd);

                updatePickaxeLore(player, currentTier, currentXP);
            } else if (currentXP < getXPNeededForTier(currentTier) || currentTier > MAX_TIER) { // la prima volta è 999.999
                int xpNeededToLevelUp = getXPNeededForTier(currentTier);
                currentTier = MAX_TIER;
                currentXP = xpNeededToLevelUp;

                // Update database
                data.setPickaxeCurrentTier(currentTier);
                data.setPickaxeCurrentXP(currentXP);

                // -1 per ultimo blocco dal 999.999
                updatePickaxeLore(player, currentTier, (currentXP - 1));
            } else {
                updatePickaxeLore(player, currentTier, currentXP);
            }
        }
    }

    public void updatePickaxeLore(Player player, int currentTier, int currentXP) {
        int xpNeededToLevelUp = getXPNeededForTier(currentTier);
        ItemStack itemInHand = player.getInventory().getItemInMainHand();
        ItemMeta meta = itemInHand.getItemMeta();
        List<String> lore = meta.getLore();

        if (currentXP < xpNeededToLevelUp || pickaxeLoreNeedFix(player)) {
            // gestione 999.999
            if (currentXP == xpNeededToLevelUp - 1) {
                currentXP++;
            }

            if (!lore.get(2).contains("Level-UP")) {
                addInitialLore(player);
            }

            lore.set(1, Utils.getCenteredMessage("§7Tier: §f" + currentTier + "/" + MAX_TIER));
            lore.set(3, Utils.getCenteredMessage(Utils.getProgressBar(Math.toIntExact(Math.round(currentXP)), Math.toIntExact(Math.round(xpNeededToLevelUp)), 50, '|', "§a", "§7")));
            lore.set(4, Utils.getCenteredMessage("§7§o" + currentXP + "/" + xpNeededToLevelUp));

            List<String> finalLore = new ArrayList<>();
            finalLore.add(lore.get(0));
            finalLore.add(lore.get(1));
            finalLore.add(lore.get(2));
            finalLore.add(lore.get(3));
            finalLore.add(lore.get(4));
            finalLore.add("");
            for (int i = 5; i < lore.size(); i++) {
                String line = lore.get(i);
                if (StringUtils.startsWith(ChatColor.stripColor(line), "▍")
                        || (StringUtils.startsWith(ChatColor.stripColor(line), " ")
                        && !StringUtils.startsWith(ChatColor.stripColor(line), "  "))) {
                    finalLore.add(line);
                }
            }

            meta.setLore(finalLore);
            itemInHand.setItemMeta(meta);
        }

        /*if (player.isOp()) {
            if (finalLore.size() > (mainClass.getEnchantManager().getEnchantList().size() + 5)) {
                player.sendMessage("Fixing lore");
                finalLore = new ArrayList<>();
                finalLore.add("");
                finalLore.add(Utils.getCenteredMessage("§7Tier: §f" + currentTier + "/" + MAX_TIER));
                finalLore.add(Utils.getCenteredMessage("§7Level-UP Progress"));
                finalLore.add(Utils.getCenteredMessage(Utils.getProgressBar(currentXP, getXPNeededForTier(currentTier), 50, '|', "§a", "§7")));
                finalLore.add(Utils.getCenteredMessage("§7§o" + currentXP + "/" + getXPNeededForTier(currentTier)));
                finalLore.add("");
                for (int i = 5; i < lore.size(); i++) {
                    String line = lore.get(i);
                    if (StringUtils.startsWith(ChatColor.stripColor(line), "▍")
                            || (StringUtils.startsWith(ChatColor.stripColor(line), " ")
                            && !StringUtils.startsWith(ChatColor.stripColor(line), "  "))) {
                        finalLore.add(line);
                    }
                }

                meta.setLore(finalLore);
                itemInHand.setItemMeta(meta);
            }
        }*/
        player.updateInventory();
    }

    public boolean pickaxeLoreNeedFix(Player player) {
        ItemStack itemInHand = player.getInventory().getItemInMainHand();
        ItemMeta meta = itemInHand.getItemMeta();
        List<String> lore = meta.getLore();

        return !StringUtils.startsWith(ChatColor.stripColor(lore.get(6)), "▍");
    }
}
