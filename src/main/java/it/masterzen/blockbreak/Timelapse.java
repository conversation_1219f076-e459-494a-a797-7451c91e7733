package it.masterzen.blockbreak;

import net.luckperms.api.model.user.User;
import net.luckperms.api.node.Node;
import org.bukkit.entity.Player;

import java.time.Duration;

public class Timelapse {

    public Timelapse(Player player) {
        this.player = player;
    }

    private Player player;
    private int blocks;
    private double money;
    private double tokens;

    public int getBlock() {
        return this.blocks;
    }

    public void addBlock(int amount) {
        this.blocks = this.blocks + amount;
    }

    public void setBlock(int amount) {
        this.blocks = amount;
    }

    public double getMoney() {
        return this.money;
    }

    public void addMoney(double amount) {
        this.money = this.money + amount;
    }

    public void setMoney(double amount) {
        this.money = amount;
    }

    public double getTokens() {
        return this.tokens;
    }

    public void addTokens(double amount) {
        this.tokens = this.tokens + amount;
    }

    public void setTokens(double amount) {
        this.tokens = amount;
    }

    public void addDelay(Player player, int timelapseLevel) {
        User user = AlphaBlockBreak.GetInstance().getLuckPerms().getPlayerAdapter(Player.class).getUser(player);
        Node node = Node.builder("timelapse.cooldown").expiry(Duration.ofMinutes(15 - timelapseLevel)).build();
        user.data().add(node);
        AlphaBlockBreak.GetInstance().getLuckPerms().getUserManager().saveUser(user);
    }

}
