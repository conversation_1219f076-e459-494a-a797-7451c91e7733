package it.masterzen.blockbreak;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.bukkit.Location;

public class RobotManager {

    private final Map<UUID, Robot> robotMap = new HashMap<>();

    public Robot getRobotFromLocation(Location loc) {
        return robotMap.values().stream().filter(Robot -> Robot.getLocation().equals(loc)).findFirst().orElse(null);
    }

    public void createRobot(final UUID owner, final Location loc) {
        this.robotMap.put(owner, new Robot(loc, 1, owner));
    }

}
