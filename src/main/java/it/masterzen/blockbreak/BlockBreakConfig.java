package it.masterzen.blockbreak;

import org.bukkit.Material;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.plugin.Plugin;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.logging.Level;

/**
 * Configuration manager for the BlockBreak threading system.
 * Handles loading, validation, and access to configuration settings.
 */
public class BlockBreakConfig {
    
    private final Plugin plugin;
    private final File configFile;
    private FileConfiguration config;
    
    // Cached configuration values for performance
    private boolean threadingEnabled;
    private int corePoolSize;
    private int maximumPoolSize;
    private long keepAliveTime;
    private int queueCapacity;
    private int threadPriority;
    
    private boolean monitoringEnabled;
    private int logInterval;
    private boolean detailedLogging;
    
    private int maxTaskTime;
    private boolean timeoutProtection;
    private int taskTimeout;
    
    private boolean asyncRewards;
    private boolean asyncPets;
    private boolean asyncEnchantments;
    private boolean asyncEffects;
    private boolean syncFallback;
    
    private boolean debugEnabled;
    private boolean logTaskSubmissions;
    private boolean logCompletionTimes;
    private boolean logPoolState;
    private boolean logRejectedTasks;
    
    private Set<String> disabledWorlds;
    private Set<Material> disabledBlocks;
    private double minTpsThreshold;
    private double tpsRecoveryThreshold;
    
    public BlockBreakConfig(Plugin plugin) {
        this.plugin = plugin;
        this.configFile = new File(plugin.getDataFolder(), "blockbreak-config.yml");
        this.disabledWorlds = new HashSet<>();
        this.disabledBlocks = new HashSet<>();
        
        loadConfig();
    }
    
    /**
     * Loads the configuration from file, creating default if it doesn't exist.
     */
    public void loadConfig() {
        try {
            // Create plugin data folder if it doesn't exist
            if (!plugin.getDataFolder().exists()) {
                plugin.getDataFolder().mkdirs();
            }
            
            // Copy default config if it doesn't exist
            if (!configFile.exists()) {
                copyDefaultConfig();
            }
            
            // Load configuration
            config = YamlConfiguration.loadConfiguration(configFile);
            
            // Cache configuration values
            cacheConfigValues();
            
            // Validate configuration
            validateConfig();
            
            plugin.getLogger().info("BlockBreak configuration loaded successfully");
            
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to load BlockBreak configuration", e);
            // Use default values if config loading fails
            setDefaultValues();
        }
    }
    
    /**
     * Copies the default configuration file from resources.
     */
    private void copyDefaultConfig() throws IOException {
        try (InputStream defaultConfig = plugin.getResource("blockbreak-config.yml")) {
            if (defaultConfig != null) {
                Files.copy(defaultConfig, configFile.toPath());
                plugin.getLogger().info("Created default BlockBreak configuration file");
            } else {
                // Create a minimal config if resource is not found
                createMinimalConfig();
            }
        }
    }
    
    /**
     * Creates a minimal configuration file with default values.
     */
    private void createMinimalConfig() throws IOException {
        config = new YamlConfiguration();
        setDefaultValues();
        config.save(configFile);
        plugin.getLogger().info("Created minimal BlockBreak configuration file");
    }
    
    /**
     * Caches configuration values for better performance.
     */
    private void cacheConfigValues() {
        // Threading settings
        threadingEnabled = config.getBoolean("threading.enabled", true);
        corePoolSize = config.getInt("threading.thread-pool.core-pool-size", 4);
        maximumPoolSize = config.getInt("threading.thread-pool.maximum-pool-size", 8);
        keepAliveTime = config.getLong("threading.thread-pool.keep-alive-time", 60);
        queueCapacity = config.getInt("threading.thread-pool.queue-capacity", 1000);
        threadPriority = config.getInt("threading.thread-pool.thread-priority", 5);
        
        // Performance settings
        monitoringEnabled = config.getBoolean("performance.monitoring.enabled", true);
        logInterval = config.getInt("performance.monitoring.log-interval", 300);
        detailedLogging = config.getBoolean("performance.monitoring.detailed-logging", false);
        
        maxTaskTime = config.getInt("performance.processing.max-task-time", 100);
        timeoutProtection = config.getBoolean("performance.processing.timeout-protection", true);
        taskTimeout = config.getInt("performance.processing.task-timeout", 5000);
        
        // Feature toggles
        asyncRewards = config.getBoolean("features.async-rewards", true);
        asyncPets = config.getBoolean("features.async-pets", true);
        asyncEnchantments = config.getBoolean("features.async-enchantments", true);
        asyncEffects = config.getBoolean("features.async-effects", true);
        syncFallback = config.getBoolean("features.sync-fallback", true);
        
        // Debug settings
        debugEnabled = config.getBoolean("debug.enabled", false);
        logTaskSubmissions = config.getBoolean("debug.log-task-submissions", false);
        logCompletionTimes = config.getBoolean("debug.log-completion-times", false);
        logPoolState = config.getBoolean("debug.log-pool-state", false);
        logRejectedTasks = config.getBoolean("debug.log-rejected-tasks", true);
        
        // Compatibility settings
        List<String> worldList = config.getStringList("compatibility.disabled-worlds");
        disabledWorlds = new HashSet<>(worldList);
        
        List<String> blockList = config.getStringList("compatibility.disabled-blocks");
        disabledBlocks.clear();
        for (String blockName : blockList) {
            try {
                Material material = Material.valueOf(blockName.toUpperCase());
                disabledBlocks.add(material);
            } catch (IllegalArgumentException e) {
                plugin.getLogger().warning("Invalid block type in config: " + blockName);
            }
        }
        
        minTpsThreshold = config.getDouble("compatibility.min-tps-threshold", 15.0);
        tpsRecoveryThreshold = config.getDouble("compatibility.tps-recovery-threshold", 18.0);
    }
    
    /**
     * Validates configuration values and corrects invalid ones.
     */
    private void validateConfig() {
        boolean configChanged = false;
        
        // Validate thread pool sizes
        if (corePoolSize < 1) {
            plugin.getLogger().warning("Invalid core-pool-size, setting to 1");
            corePoolSize = 1;
            configChanged = true;
        }
        
        if (maximumPoolSize < corePoolSize) {
            plugin.getLogger().warning("maximum-pool-size cannot be less than core-pool-size, adjusting");
            maximumPoolSize = corePoolSize * 2;
            configChanged = true;
        }
        
        if (queueCapacity < 1) {
            plugin.getLogger().warning("Invalid queue-capacity, setting to 100");
            queueCapacity = 100;
            configChanged = true;
        }
        
        // Validate thread priority
        if (threadPriority < 1 || threadPriority > 10) {
            plugin.getLogger().warning("Invalid thread-priority, setting to 5");
            threadPriority = 5;
            configChanged = true;
        }
        
        // Validate timeouts
        if (taskTimeout < 1000) {
            plugin.getLogger().warning("task-timeout too low, setting to 1000ms");
            taskTimeout = 1000;
            configChanged = true;
        }
        
        // Validate TPS thresholds
        if (minTpsThreshold < 5.0 || minTpsThreshold > 20.0) {
            plugin.getLogger().warning("Invalid min-tps-threshold, setting to 15.0");
            minTpsThreshold = 15.0;
            configChanged = true;
        }
        
        if (tpsRecoveryThreshold <= minTpsThreshold) {
            plugin.getLogger().warning("tps-recovery-threshold must be higher than min-tps-threshold, adjusting");
            tpsRecoveryThreshold = minTpsThreshold + 3.0;
            configChanged = true;
        }
        
        // Save config if changes were made
        if (configChanged) {
            try {
                saveConfig();
            } catch (IOException e) {
                plugin.getLogger().log(Level.WARNING, "Failed to save corrected configuration", e);
            }
        }
    }
    
    /**
     * Sets default configuration values.
     */
    private void setDefaultValues() {
        threadingEnabled = true;
        corePoolSize = 4;
        maximumPoolSize = 8;
        keepAliveTime = 60;
        queueCapacity = 1000;
        threadPriority = 5;
        
        monitoringEnabled = true;
        logInterval = 300;
        detailedLogging = false;
        
        maxTaskTime = 100;
        timeoutProtection = true;
        taskTimeout = 5000;
        
        asyncRewards = true;
        asyncPets = true;
        asyncEnchantments = true;
        asyncEffects = true;
        syncFallback = true;
        
        debugEnabled = false;
        logTaskSubmissions = false;
        logCompletionTimes = false;
        logPoolState = false;
        logRejectedTasks = true;
        
        disabledWorlds.clear();
        disabledBlocks.clear();
        minTpsThreshold = 15.0;
        tpsRecoveryThreshold = 18.0;
    }
    
    /**
     * Saves the current configuration to file.
     */
    public void saveConfig() throws IOException {
        if (config != null) {
            config.save(configFile);
        }
    }
    
    /**
     * Reloads the configuration from file.
     */
    public void reloadConfig() {
        loadConfig();
    }
    
    // Getter methods for configuration values
    public boolean isThreadingEnabled() { return threadingEnabled; }
    public int getCorePoolSize() { return corePoolSize; }
    public int getMaximumPoolSize() { return maximumPoolSize; }
    public long getKeepAliveTime() { return keepAliveTime; }
    public int getQueueCapacity() { return queueCapacity; }
    public int getThreadPriority() { return threadPriority; }
    
    public boolean isMonitoringEnabled() { return monitoringEnabled; }
    public int getLogInterval() { return logInterval; }
    public boolean isDetailedLogging() { return detailedLogging; }
    
    public int getMaxTaskTime() { return maxTaskTime; }
    public boolean isTimeoutProtection() { return timeoutProtection; }
    public int getTaskTimeout() { return taskTimeout; }
    
    public boolean isAsyncRewards() { return asyncRewards; }
    public boolean isAsyncPets() { return asyncPets; }
    public boolean isAsyncEnchantments() { return asyncEnchantments; }
    public boolean isAsyncEffects() { return asyncEffects; }
    public boolean isSyncFallback() { return syncFallback; }
    
    public boolean isDebugEnabled() { return debugEnabled; }
    public boolean isLogTaskSubmissions() { return logTaskSubmissions; }
    public boolean isLogCompletionTimes() { return logCompletionTimes; }
    public boolean isLogPoolState() { return logPoolState; }
    public boolean isLogRejectedTasks() { return logRejectedTasks; }
    
    public Set<String> getDisabledWorlds() { return new HashSet<>(disabledWorlds); }
    public Set<Material> getDisabledBlocks() { return new HashSet<>(disabledBlocks); }
    public double getMinTpsThreshold() { return minTpsThreshold; }
    public double getTpsRecoveryThreshold() { return tpsRecoveryThreshold; }
    
    /**
     * Checks if threading is disabled for a specific world.
     */
    public boolean isWorldDisabled(String worldName) {
        return disabledWorlds.contains(worldName);
    }
    
    /**
     * Checks if threading is disabled for a specific block type.
     */
    public boolean isBlockDisabled(Material blockType) {
        return disabledBlocks.contains(blockType);
    }
}
