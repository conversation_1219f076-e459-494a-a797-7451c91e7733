package it.masterzen.blockbreak;

import it.masterzen.Keys.ItemList;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;

/**
 * Manager for async key opening operations.
 * Handles task submission, progress tracking, and integration with the existing keys system.
 */
public class AsyncKeyOpeningManager {

    private final Plugin plugin;
    private final KeyOpeningConfig config;
    private final KeyOpeningThreadPool threadPool;

    // Track active key opening operations
    private final ConcurrentHashMap<UUID, KeyOpeningTask> activeTasks = new ConcurrentHashMap<>();

    public AsyncKeyOpeningManager(Plugin plugin) {
        this.plugin = plugin;
        this.config = new KeyOpeningConfig(plugin);
        this.threadPool = new KeyOpeningThreadPool(plugin, config);

        plugin.getLogger().info("AsyncKeyOpeningManager initialized with async processing " +
                               (config.isAsyncEnabled() ? "enabled" : "disabled"));
    }

    /**
     * Opens keys asynchronously for a player.
     *
     * @param player The player opening keys
     * @param keyType The type of keys being opened
     * @param amount The number of keys to open
     * @param itemList The list of possible rewards
     * @param sendMessage Whether to send messages to the player
     * @return CompletableFuture that completes when all keys are processed
     */
    public CompletableFuture<KeyOpeningResult> openKeysAsync(Player player, String keyType, int amount,
                                                            List<ItemList> itemList, boolean sendMessage) {

        // Check if player already has an active key opening operation
        if (activeTasks.containsKey(player.getUniqueId())) {
            plugin.getLogger().warning("Player " + player.getName() + " already has an active key opening operation");
            return CompletableFuture.completedFuture(KeyOpeningResult.error("You already have keys being opened"));
        }

        // Determine if we should use async processing
        boolean useAsync = shouldUseAsync(amount);

        if (!useAsync) {
            // For small amounts, use synchronous processing
            return CompletableFuture.completedFuture(openKeysSynchronously(player, keyType, amount, itemList, sendMessage));
        }

        // Create and submit async task
        CompletableFuture<KeyOpeningResult> future = new CompletableFuture<>();

        KeyOpeningTask task = new KeyOpeningTask(
            plugin,
            player,
            keyType,
            amount,
            itemList,
            sendMessage,
            config,
            result -> {
                // Remove from active tasks when completed
                activeTasks.remove(player.getUniqueId());
                future.complete(result);
            }
        );

        // Track the active task
        activeTasks.put(player.getUniqueId(), task);

        // Submit to thread pool
        try {
            threadPool.submitTask(task).exceptionally(throwable -> {
                plugin.getLogger().log(Level.WARNING, "Error in async key opening for player " + player.getName(), throwable);
                activeTasks.remove(player.getUniqueId());
                future.complete(KeyOpeningResult.error("Processing failed: " + throwable.getMessage()));
                return null;
            });

            if (config.isDebugEnabled()) {
                plugin.getLogger().info("Submitted async key opening task for player " + player.getName() +
                                      " (" + amount + " " + keyType + " keys)");
            }

        } catch (Exception e) {
            plugin.getLogger().log(Level.WARNING, "Failed to submit async key opening task", e);
            activeTasks.remove(player.getUniqueId());
            future.complete(KeyOpeningResult.error("Failed to submit task: " + e.getMessage()));
        }

        return future;
    }

    /**
     * Determines whether to use async processing based on the number of keys.
     */
    private boolean shouldUseAsync(int keyCount) {
        if (!config.isAsyncEnabled()) {
            return false;
        }

        // Use async for large amounts or if specifically configured
        return keyCount >= config.getMinKeysForProgress() || keyCount >= 10000;
    }

    /**
     * Opens keys synchronously (fallback method).
     */
    private KeyOpeningResult openKeysSynchronously(Player player, String keyType, int amount,
                                                  List<ItemList> itemList, boolean sendMessage) {
        try {
            // Create a task but run it synchronously
            KeyOpeningTask task = new KeyOpeningTask(
                plugin,
                player,
                keyType,
                amount,
                itemList,
                sendMessage,
                config,
                null // No callback for sync execution
            );

            task.run();
            return KeyOpeningResult.success(amount);

        } catch (Exception e) {
            plugin.getLogger().log(Level.WARNING, "Error in synchronous key opening", e);
            return KeyOpeningResult.error("Processing failed: " + e.getMessage());
        }
    }

    /**
     * Cancels an active key opening operation for a player.
     */
    public boolean cancelKeyOpening(Player player) {
        KeyOpeningTask task = activeTasks.get(player.getUniqueId());
        if (task != null) {
            task.cancel();
            activeTasks.remove(player.getUniqueId());
            player.sendMessage("§e§lKEYS §8»§7 Key opening operation cancelled.");
            return true;
        }
        return false;
    }

    /**
     * Checks if a player has an active key opening operation.
     */
    public boolean hasActiveKeyOpening(Player player) {
        return activeTasks.containsKey(player.getUniqueId());
    }

    /**
     * Gets the active key opening task for a player.
     */
    public KeyOpeningTask getActiveTask(Player player) {
        return activeTasks.get(player.getUniqueId());
    }

    /**
     * Gets the number of active key opening operations.
     */
    public int getActiveTaskCount() {
        return activeTasks.size();
    }

    /**
     * Gets thread pool statistics.
     */
    public KeyOpeningPoolStats getPoolStats() {
        return threadPool.getStats();
    }

    /**
     * Gets the configuration.
     */
    public KeyOpeningConfig getConfig() {
        return config;
    }

    /**
     * Shuts down the async key opening manager.
     */
    public void shutdown() {
        plugin.getLogger().info("Shutting down AsyncKeyOpeningManager...");

        // Cancel all active tasks
        for (KeyOpeningTask task : activeTasks.values()) {
            task.cancel();
        }
        activeTasks.clear();

        // Shutdown thread pool
        threadPool.shutdown();

        plugin.getLogger().info("AsyncKeyOpeningManager shutdown complete");
    }
}