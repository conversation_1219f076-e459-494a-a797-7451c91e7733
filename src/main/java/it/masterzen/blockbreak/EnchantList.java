package it.masterzen.blockbreak;

public class EnchantList {

    private String enchantName;
    private int maxLevel;
    private double price;
    private String valuta;
    private boolean isInfinite;
    private int color;
    private boolean isNew;
    private boolean isUnique;
    private int tier = 0;
    private int blocksNeeded = 0;
    private boolean enchanter = false;
    private int blocksNeededToUnlockNewLevels = 0;
    private int levelsToUnlock;
    private int levelsToUnlockPerReset = -1;
    private int pickResetRequired;
    private double growthRate = 0.0; // Default 0.0 for linear behavior (backward compatibility)

    public EnchantList(String name, int maxLevel, double pricePerLevel, String valuta, boolean isInfinite, int color, boolean isNew, boolean isUnique, boolean enchanter, int pickResetRequired) {
        this.enchantName = name;
        this.maxLevel = maxLevel;
        this.price = pricePerLevel;
        this.valuta = valuta;
        this.isInfinite = isInfinite;
        this.color = color;
        this.isNew = isNew;
        this.isUnique = isUnique;
        this.enchanter = enchanter;
        this.pickResetRequired = pickResetRequired;
        this.growthRate = 0.0; // Default linear behavior
    }

    public EnchantList(String name, int maxLevel, double pricePerLevel, String valuta, boolean isInfinite, int color, boolean isNew, boolean isUnique, boolean enchanter, int levelsToUnlockPerReset, int pickResetRequired) {
        this.enchantName = name;
        this.maxLevel = maxLevel;
        this.price = pricePerLevel;
        this.valuta = valuta;
        this.isInfinite = isInfinite;
        this.color = color;
        this.isNew = isNew;
        this.isUnique = isUnique;
        this.enchanter = enchanter;
        this.levelsToUnlockPerReset = levelsToUnlockPerReset;
        this.pickResetRequired = pickResetRequired;
        this.growthRate = 0.0; // Default linear behavior
    }

    public EnchantList(String name, int maxLevel, double pricePerLevel, String valuta, boolean isInfinite, int color, boolean isNew, boolean isUnique, int tier, boolean enchanter) {
        this.enchantName = name;
        this.maxLevel = maxLevel;
        this.price = pricePerLevel;
        this.valuta = valuta;
        this.isInfinite = isInfinite;
        this.color = color;
        this.isNew = isNew;
        this.isUnique = isUnique;
        this.tier = tier;
        this.enchanter = enchanter;
        this.growthRate = 0.0; // Default linear behavior
    }

    public EnchantList(String name, int maxLevel, double pricePerLevel, String valuta, boolean isInfinite, int color, boolean isNew, boolean isUnique, int tier, int blocksNeeded, boolean enchanter, int levelsToUnlockPerReset) {
        this.enchantName = name;
        this.maxLevel = maxLevel;
        this.price = pricePerLevel;
        this.valuta = valuta;
        this.isInfinite = isInfinite;
        this.color = color;
        this.isNew = isNew;
        this.isUnique = isUnique;
        this.blocksNeeded = blocksNeeded;
        this.enchanter = enchanter;
        this.levelsToUnlockPerReset = levelsToUnlockPerReset;
        this.growthRate = 0.0; // Default linear behavior
    }

    public EnchantList(String name, int maxLevel, double pricePerLevel, String valuta, boolean isInfinite, int color, boolean isNew, boolean isUnique, boolean enchanter, int blocksNeededToUnlockNewLevels, int levelsToUnlock, int pickResetRequired) {
        this.enchantName = name;
        this.maxLevel = maxLevel;
        this.price = pricePerLevel;
        this.valuta = valuta;
        this.isInfinite = isInfinite;
        this.color = color;
        this.isNew = isNew;
        this.isUnique = isUnique;
        this.enchanter = enchanter;
        this.blocksNeededToUnlockNewLevels = blocksNeededToUnlockNewLevels;
        this.levelsToUnlock = levelsToUnlock;
        this.pickResetRequired = pickResetRequired;
        this.growthRate = 0.0; // Default linear behavior
    }

    // New constructor with growthRate parameter for infinite enchants
    public EnchantList(String name, int maxLevel, double pricePerLevel, String valuta, boolean isInfinite, int color, boolean isNew, boolean isUnique, boolean enchanter, int pickResetRequired, double growthRate) {
        this.enchantName = name;
        this.maxLevel = maxLevel;
        this.price = pricePerLevel;
        this.valuta = valuta;
        this.isInfinite = isInfinite;
        this.color = color;
        this.isNew = isNew;
        this.isUnique = isUnique;
        this.enchanter = enchanter;
        this.pickResetRequired = pickResetRequired;
        this.growthRate = growthRate;
    }

    public void setEnchantName(String name) {
        this.enchantName = name;
    }

    public void setMaxLevel(int level) {
        this.maxLevel = level;
    }

    public void setPrice(double pricePerLevel) {
        this.price = pricePerLevel;
    }

    public void setInfinite(boolean isInfinite) {
        this.isInfinite = isInfinite;
    }

    public void setColor(int color) {
        this.color = color;
    }

    public void setTier(int tier) {
        this.tier = tier;
    }

    public String getEnchantName() {
        return this.enchantName;
    }

    public int getMaxLevel() {
        return this.maxLevel;
    }

    public double getPrice() {
        return this.price;
    }

    public boolean isInfinite() {
        return this.isInfinite;
    }

    public int getColor() {
        return this.color;
    }

    public boolean isNew() {
        return this.isNew;
    }

    public boolean isUnique() {
        return this.isUnique;
    }

    public int getTier() {
        return this.tier;
    }

    public String getValuta() {
        return this.valuta;
    }

    public int getBlocksNeeded() {
        return blocksNeeded;
    }

    public void setBlocksNeeded(int blocksNeeded) {
        this.blocksNeeded = blocksNeeded;
    }

    public void setValuta(String valuta) {
        this.valuta = valuta;
    }

    public void setNew(boolean aNew) {
        isNew = aNew;
    }

    public void setUnique(boolean unique) {
        isUnique = unique;
    }

    public boolean isEnchanter() {
        return enchanter;
    }

    public void setEnchanter(boolean enchanter) {
        this.enchanter = enchanter;
    }

    public int getBlocksNeededToUnlockNewLevels() {
        return blocksNeededToUnlockNewLevels;
    }

    public void setBlocksNeededToUnlockNewLevels(int blocksNeededToUnlockNewLevels) {
        this.blocksNeededToUnlockNewLevels = blocksNeededToUnlockNewLevels;
    }

    public int getLevelsToUnlock() {
        return levelsToUnlock;
    }

    public void setLevelsToUnlock(int levelsToUnlock) {
        this.levelsToUnlock = levelsToUnlock;
    }

    public int getLevelsToUnlockPerReset() {
        return levelsToUnlockPerReset;
    }

    public void setLevelsToUnlockPerReset(int levelsToUnlockPerReset) {
        this.levelsToUnlockPerReset = levelsToUnlockPerReset;
    }

    public int getPickResetRequired() {
        return pickResetRequired;
    }

    public void setPickResetRequired(int pickResetRequired) {
        this.pickResetRequired = pickResetRequired;
    }

    public double getGrowthRate() {
        return growthRate;
    }

    public void setGrowthRate(double growthRate) {
        this.growthRate = growthRate;
    }
}
