package it.masterzen.blockbreak;

import com.songoda.skyblock.api.SkyBlockAPI;
import com.songoda.skyblock.api.island.Island;
import com.songoda.skyblock.api.island.IslandRole;
import it.masterzen.MongoDB.DataTypes.CropGeneratorPojo;
import it.masterzen.commands.Main;
import org.apache.commons.lang.StringUtils;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

public class CropsGenerator implements Listener {

    private final String prefix = "§e§lFARMER §8»§7 ";
    private AlphaBlockBreak mainClass;
    //private UUID owner;

    public CropsGenerator(AlphaBlockBreak plugin) {
        mainClass = plugin;
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (event.getClickedInventory() != null && event.getClickedInventory().equals(event.getView().getTopInventory())) {
            if (event.getView().getTitle().startsWith("§e§lCROPS §f| §7Generator (")) {
                Player player = (Player) event.getWhoClicked();
                ItemStack clickedItem = event.getCurrentItem();
                if (clickedItem != null && !clickedItem.getType().equals(Material.AIR)) {
                    event.setCancelled(true);
                    int currentPage = Integer.parseInt(ChatColor.stripColor(event.getView().getTitle()).replace("CROPS | Generator (", "").replace(")", ""));
                    if (clickedItem.getType().equals(Material.ARROW)) {
                        if (clickedItem.getItemMeta().getDisplayName().contains("Next")) {
                            openMenu(player, currentPage + 1);
                        } else {
                            openMenu(player, currentPage - 1);
                        }
                    } else {
                        if (event.isLeftClick()) {
                            // claim
                            List<String> lore = clickedItem.getItemMeta().getLore();
                            int points = Integer.parseInt(StringUtils.substringAfterLast(ChatColor.stripColor(lore.get(3)), ":").trim());
                            if (points > 0) {
                                mainClass.addCropsPoints(player, points, true);

                                Island island = SkyBlockAPI.getIslandManager().getIsland(player);
                                List<CropGeneratorPojo> generatorList = mainClass.getMongoReader().getIslandCropGenerators(island.getIslandUUID());
                                int generatorIndex = Integer.parseInt(ChatColor.stripColor(clickedItem.getItemMeta().getDisplayName()).replace("GENERATOR #", "")) - 1;
                                mainClass.getMongoReader().updateCropGenerator(generatorList.get(generatorIndex).getId());

                                for (UUID islandMemberUUID : mainClass.getIslandMember(island)) {
                                    Player tmpPlayer = Bukkit.getPlayer(islandMemberUUID);
                                    if (tmpPlayer != null && tmpPlayer.isOnline()) {
                                        tmpPlayer.sendMessage(prefix + player.getName() + " claimed §a§l" + points + " §7points from a generator");
                                    }
                                }

                                openMenu(player, currentPage);
                            } else {
                                player.sendMessage(prefix + "§cThere's no point to claim");
                            }
                        } else if (event.isRightClick()) {
                            // remove
                            Island island = SkyBlockAPI.getIslandManager().getIsland(player);
                            List<CropGeneratorPojo> generatorList = mainClass.getMongoReader().getIslandCropGenerators(island.getIslandUUID());
                            mainClass.getMongoReader().removeCropGenerator(generatorList.get(event.getSlot()).getId());
                            giveGenerator(player, true);

                            for (UUID islandMemberUUID : mainClass.getIslandMember(island)) {
                                Player tmpPlayer = Bukkit.getPlayer(islandMemberUUID);
                                if (tmpPlayer != null && tmpPlayer.isOnline()) {
                                    tmpPlayer.sendMessage(prefix + player.getName() + " removed a §e§lCROPS §7- Generator. §f§o(/island generators)");
                                }
                            }

                            openMenu(player, currentPage);
                        }
                    }
                }
            }
        }
    }

    public void removeGenerator(Player player, Location location) {
        Island island = SkyBlockAPI.getIslandManager().getIslandAtLocation(location);
        if ((island != null && (island.hasRole(player.getUniqueId(), IslandRole.OWNER) || island.hasRole(player.getUniqueId(), IslandRole.OPERATOR))) || player.isOp()) {
            player.closeInventory();
            AlphaBlockBreak.setBlockInNativeChunk(player, location.getWorld(), location.getBlockX(), location.getBlockY(), location.getBlockZ(), 0, (byte) 0);
            AlphaBlockBreak.GetInstance().getCropsGeneratorMap().remove(location);
            player.sendMessage(prefix + "Generator removed !");
            AlphaBlockBreak.GetInstance().getCropsGenerator().giveGenerator(player, false);
        } else {
            player.sendMessage(prefix + "§cYou can't remove this generator. You need to be Operator");
        }
    }

    public void placeGenerator(Player player, UUID islandUUID) {
        Island island = SkyBlockAPI.getIslandManager().getIslandByUUID(islandUUID);
        mainClass.getMongoReader().addCropGenerator(player, islandUUID);

        for (UUID islandMemberUUID : mainClass.getIslandMember(island)) {
            Player tmpPlayer = Bukkit.getPlayer(islandMemberUUID);
            if (tmpPlayer != null && tmpPlayer.isOnline()) {
                tmpPlayer.sendMessage(prefix + player.getName() + " deposited a §e§lCROPS §7- Generator. §f§o(/island generators)");
            }
        }
    }

    public void giveGenerator(Player player, boolean sendToConsole) {
        ItemStack head = SkullCreator.itemFromBase64("eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvN2FhNTk2NmExNDcyNDQ1MDRjYzU2ZWY2ZWZkMmQyZjQ0NzM4YjhmMDNkOTNhNjE3NjZhZjNmYzQ0ODdmOTgwYiJ9fX0=");
        SkullMeta meta = null;
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§7Place me to start Generate resources");
        lore.add("");
        meta = (SkullMeta) head.getItemMeta();
        meta.setLore(lore);
        meta.setDisplayName("§e§lCROPS §7- Generator");
        head.setItemMeta(meta);
        player.getInventory().addItem(head);
        player.sendMessage(prefix + "Generator added to your inventory");
        if (sendToConsole) {
            AlphaBlockBreak.GetInstance().getLogger().info("Crops generator added to " + player.getName() + " inventory");
        }
    }

    public void collectAllPoints(Player player) {
        Island island = SkyBlockAPI.getIslandManager().getIsland(player);
        List<CropGeneratorPojo> generatorList = mainClass.getMongoReader().getIslandCropGenerators(island.getIslandUUID());

        long pointsToAdd = 0;
        for (CropGeneratorPojo generator : generatorList) {
            long tmpPoints = calcGeneratedPoints(generator.getLastClaimDate());
            if (tmpPoints > 0) {
                pointsToAdd = pointsToAdd + tmpPoints;
                mainClass.getMongoReader().updateCropGenerator(generator.getId());
            }
        }

        if (pointsToAdd > 0) {
            mainClass.addCropsPoints(player, pointsToAdd, true);
        } else {
            player.sendMessage(prefix + "§cThere's no points to collect");
        }
    }

    public void openMenu(Player player, int page) {
        Inventory gui = Bukkit.createInventory(null, 54, "§e§lCROPS §f| §7Generator (" + page + ")");

        Island island = SkyBlockAPI.getIslandManager().getIsland(player);
        List<CropGeneratorPojo> generatorList = mainClass.getMongoReader().getIslandCropGenerators(island.getIslandUUID());

        int counter = 1;
        int initialLoopValue = (page == 1 ? 0 : (53 * (page - 1) - (page == 2 ? 0 : (page - 2))));
        boolean multiplePage = generatorList.size() > initialLoopValue + 52;
        int endLoopValue = (multiplePage ? initialLoopValue + 53 : initialLoopValue + 54);

        if (multiplePage) {
            ItemStack next = new ItemStack(Material.ARROW);
            ItemMeta meta = next.getItemMeta();
            meta.setDisplayName("§7Next Page");
            next.setItemMeta(meta);
            gui.setItem(53, next);
        }
        if (page > 1) {
            ItemStack back = new ItemStack(Material.ARROW);
            ItemMeta meta = back.getItemMeta();
            meta.setDisplayName("§7Previous Page");
            back.setItemMeta(meta);
            gui.setItem(45, back);
        }

        for (int i = initialLoopValue; i < endLoopValue; i++) {
            if (generatorList.size() > i) {
                int pointsGenerated = calcGeneratedPoints(generatorList.get(i).getLastClaimDate());

                Duration tmpDuration = Duration.between(LocalDateTime.ofInstant(generatorList.get(i).getLastClaimDate().toInstant(), ZoneId.systemDefault()), LocalDateTime.now());
                long nextGenerationInSeconds = 0;
                if (tmpDuration.getSeconds() > 60) {
                    int tmp = Math.round(tmpDuration.getSeconds() / 60);
                    nextGenerationInSeconds = (60 - (tmpDuration.getSeconds() - (tmp * 60L)));
                } else {
                    nextGenerationInSeconds = (60 - tmpDuration.getSeconds());
                }

                ItemStack head = SkullCreator.itemFromBase64("eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvN2FhNTk2NmExNDcyNDQ1MDRjYzU2ZWY2ZWZkMmQyZjQ0NzM4YjhmMDNkOTNhNjE3NjZhZjNmYzQ0ODdmOTgwYiJ9fX0=");
                SkullMeta meta = null;
                meta = (SkullMeta) head.getItemMeta();
                meta.setDisplayName("§6§lGENERATOR §f#" + (initialLoopValue + counter));
                List<String> lore = new ArrayList<>();
                lore.add("");
                lore.add("§7§lDESCRIPTION");
                lore.add("§7| §fPlaced by: §7" + Bukkit.getOfflinePlayer(UUID.fromString(generatorList.get(i).getPlayerUUID())).getName());
                lore.add("§7| §fPoints generated: §7" + pointsGenerated);
                lore.add("§7| §fNext generation in §7" + nextGenerationInSeconds + " §fsecond/s");
                lore.add("");
                lore.add("§6§lUSAGE");
                lore.add("§6| §fLeft click to claim");
                lore.add("§6| §fRight click to remove");
                lore.add("");
                meta.setLore(lore);
                head.setItemMeta(meta);

                gui.addItem(head);
                counter++;
            }
        }

        //ItemStack pickup = new ItemStack(Material.HOPPER);
        //meta = pickup.getItemMeta();
        //meta.setDisplayName("§c§lREMOVE");
        //Lore = new ArrayList<>();
        //Lore.add("§7Click me to pickup this generator");
        //meta.setLore(Lore);
        //pickup.setItemMeta(meta);

        //gui.setItem(13, pointsValue);
        //gui.setItem(26, pickup);

        player.openInventory(gui);
    }

    public int calcGeneratedPoints(Date lastUseDate) {
        LocalDateTime now = LocalDateTime.now();
        Duration time = Duration.between(LocalDateTime.ofInstant(lastUseDate.toInstant(), ZoneId.systemDefault()), now);
        /*long tmpValue = time.getSeconds();
        if (tmpValue >= 30) {
            tmpValue = Math.round(tmpValue / 30);
        } else {
            tmpValue = 0;
        }*/
        //AlphaBlockBreak.GetInstance().getLogger().info(time.toMinutes() + "");

        return (int) (time.toMinutes() * 200);
        //this.points = this.loadedPoints + (time.toMinutes() * 2);
        //this.lastUse = LocalDateTime.now();
    }
}
