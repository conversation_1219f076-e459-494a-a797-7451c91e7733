package it.masterzen.blockbreak;

import com.sk89q.worldedit.MaxChangedBlocksException;
import com.songoda.skyblock.api.SkyBlockAPI;
import com.songoda.skyblock.api.island.Island;
import it.masterzen.MongoDB.DataTypes.IslandValueTracker;
import it.masterzen.MongoDB.IslandData;
import it.masterzen.MongoDB.PlayerData;
import it.masterzen.commands.Main;
import org.apache.commons.lang.StringUtils;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class IslandManager implements Listener {

    private final String prefix = "§e§lISLAND §8»§7 ";
    public final AlphaBlockBreak mainClass;

    public IslandManager(AlphaBlockBreak plugin) {
        mainClass = plugin;
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (event.getClickedInventory() != null && event.getClickedInventory().equals(event.getView().getTopInventory())) {
            if (event.getView().getTitle().equalsIgnoreCase("§e§lISLAND §f| §7Statistics")) {
                event.setCancelled(true);
            }
        }
    }

    public void addValue(Player player, long amount) {
        Island island = SkyBlockAPI.getIslandManager().getIsland(player);
        if (island != null) {
            IslandData data = mainClass.getMongoReader().getIslandData(island.getIslandUUID());
            if (data.getMemberValueDepositedList() == null) {
                data.setMemberValueDepositedList(new ArrayList<>());
            }

            IslandValueTracker playerTracker = null;
            for (IslandValueTracker tracker : data.getMemberValueDepositedList()) {
                if (StringUtils.equalsIgnoreCase(tracker.getPlayerUUID(), player.getUniqueId().toString())) {
                    playerTracker = tracker;
                    break;
                }
            }
            if (playerTracker == null) {
                playerTracker = new IslandValueTracker();
                playerTracker.setPlayerUUID(player.getUniqueId().toString());
                playerTracker.setValueDeposited(0L);
                data.getMemberValueDepositedList().add(playerTracker);
            }

            playerTracker.setValueDeposited(playerTracker.getValueDeposited() + amount);
        }
    }

    public void openGUI(Player player) {
        Island island = SkyBlockAPI.getIslandManager().getIsland(player);
        if (island != null) {
            Inventory gui = Bukkit.createInventory(null, 54, "§e§lISLAND §f| §7Statistics");
            Main.FillBorder(gui);

            IslandData data = mainClass.getMongoReader().getIslandData(island.getIslandUUID());
            //int initialSlot = 19;

            if (data.getMemberValueDepositedList() != null) {
                for (IslandValueTracker tracker : data.getMemberValueDepositedList()) {
                    ItemStack trackerItem = new ItemStack(Material.BOOK);
                    ItemMeta meta = trackerItem.getItemMeta();
                    meta.setDisplayName("§f" + Bukkit.getOfflinePlayer(UUID.fromString(tracker.getPlayerUUID())).getName() + "§7's Information");
                    List<String> lore = new ArrayList<>();
                    lore.add("");
                    lore.add("§7Value deposited: §b" + tracker.getValueDeposited());
                    meta.setLore(lore);
                    trackerItem.setItemMeta(meta);

                    gui.addItem(trackerItem);
                    /*if (gui.getItem(initialSlot) == null) {
                        gui.setItem(initialSlot, trackerItem);
                        initialSlot++;
                    } else {
                        int maxIterations = 10;
                        ItemStack itemOnGui = gui.getItem(initialSlot);
                        while (itemOnGui != null && maxIterations > 0) { // prendo il prossimo slot libero
                            maxIterations--;
                            initialSlot++;
                            itemOnGui = gui.getItem(initialSlot);
                        }
                        gui.setItem(initialSlot, trackerItem);
                    }*/
                }
            }

            player.openInventory(gui);
        } else {
            player.sendMessage(prefix + "§cYou're not in any island. Create one first or join someone's island in order to view the statistics");
        }
    }
}
