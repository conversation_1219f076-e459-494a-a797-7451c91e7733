package it.masterzen.blockbreak;

/**
 * Statistics for the key opening thread pool.
 */
public class KeyOpeningPoolStats {

    private final int activeThreads;
    private final int maxThreads;
    private final int queueSize;
    private final int maxQueueSize;
    private final long tasksSubmitted;
    private final long tasksCompleted;
    private final long tasksRejected;
    private final long avgProcessingTimeMs;

    public KeyOpeningPoolStats(int activeThreads, int maxThreads, int queueSize, int maxQueueSize,
                              long tasksSubmitted, long tasksCompleted, long tasksRejected,
                              long avgProcessingTimeMs) {
        this.activeThreads = activeThreads;
        this.maxThreads = maxThreads;
        this.queueSize = queueSize;
        this.maxQueueSize = maxQueueSize;
        this.tasksSubmitted = tasksSubmitted;
        this.tasksCompleted = tasksCompleted;
        this.tasksRejected = tasksRejected;
        this.avgProcessingTimeMs = avgProcessingTimeMs;
    }

    public int getActiveThreads() { return activeThreads; }
    public int getMaxThreads() { return maxThreads; }
    public int getQueueSize() { return queueSize; }
    public int getMaxQueueSize() { return maxQueueSize; }
    public long getTasksSubmitted() { return tasksSubmitted; }
    public long getTasksCompleted() { return tasksCompleted; }
    public long getTasksRejected() { return tasksRejected; }
    public long getAvgProcessingTimeMs() { return avgProcessingTimeMs; }

    public double getQueueUtilization() {
        return maxQueueSize > 0 ? (double) queueSize / maxQueueSize * 100 : 0;
    }

    public double getThreadUtilization() {
        return maxThreads > 0 ? (double) activeThreads / maxThreads * 100 : 0;
    }

    public double getCompletionRate() {
        return tasksSubmitted > 0 ? (double) tasksCompleted / tasksSubmitted * 100 : 0;
    }

    public double getRejectionRate() {
        return tasksSubmitted > 0 ? (double) tasksRejected / tasksSubmitted * 100 : 0;
    }

    @Override
    public String toString() {
        return String.format("KeyOpeningPoolStats{active=%d/%d (%.1f%%), queue=%d/%d (%.1f%%), " +
                           "submitted=%d, completed=%d (%.1f%%), rejected=%d (%.1f%%), avgTime=%dms}",
                           activeThreads, maxThreads, getThreadUtilization(),
                           queueSize, maxQueueSize, getQueueUtilization(),
                           tasksSubmitted, tasksCompleted, getCompletionRate(),
                           tasksRejected, getRejectionRate(), avgProcessingTimeMs);
    }
}