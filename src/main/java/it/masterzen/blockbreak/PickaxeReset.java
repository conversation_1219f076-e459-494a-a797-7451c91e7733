package it.masterzen.blockbreak;

import it.masterzen.MongoDB.PlayerData;
import me.clip.ezblocks.EZBlocks;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;

public class PickaxeReset {

    private final String prefix = "§e§lPICK RESET §8»§7 ";
    public final AlphaBlockBreak mainClass;
    public static final int MAX_RESET = 99;

    public PickaxeReset(AlphaBlockBreak plugin) {
        mainClass = plugin;
    }

    public double missedLevelToResetPick(Player player, String enchantName) {
        return mainClass.getEnchantManager().getEnchantMaxLevel(player, enchantName) - mainClass.getEnchantLevel(player, enchantName);
    }

    public boolean canResetPick(Player player) {
        boolean canReset = false;

        for (EnchantList enchant : mainClass.getEnchantManager().getEnchantList().values()) {
            if (enchant.getLevelsToUnlockPerReset() != -1 &&
                    !enchant.getEnchantName().equals("ValueFinder")) {
                double missedLevels = missedLevelToResetPick(player, enchant.getEnchantName());
                if (missedLevels <= 0) {
                    canReset = true;
                } else {
                    canReset = false;
                    break;
                }
            }
        }

        // controllo 50k blocchi richiesti
        if (canReset) {
            PlayerData data = mainClass.getMongoReader().getPlayerData(player.getUniqueId());
            int currentReset = data.getPickaxeResetAmount() == null ? 0 : data.getPickaxeResetAmount();
            currentReset++; // controllo per il prossimo reset -> se a 0, saranno 50k, a 1 saranno 100k...
            int currentBlocks = EZBlocks.getEZBlocks().getBlocksBroken(player);

            if (currentBlocks <= currentReset * 50000) {
                canReset = false;
            }
        }

        return canReset;
    }

    public boolean resetPick(Player player) {
        boolean completed = false;
        PlayerData data = mainClass.getMongoReader().getPlayerData(player.getUniqueId());
        if (data.getPickaxeResetAmount() == null) {
            data.setPickaxeResetAmount(0);
        }

        if (data.getPickaxeResetAmount() < MAX_RESET) {
            ItemStack itemInHand = player.getInventory().getItemInMainHand();
            if (itemInHand.getType().equals(Material.DIAMOND_PICKAXE)) {
                data.setPickaxeResetAmount(data.getPickaxeResetAmount() + 1);

                ItemMeta meta = itemInHand.getItemMeta();
                List<String> lore = meta.getLore();
                List<String> newLore = new ArrayList<>();
                for (String line : lore) {
                    if (!StringUtils.contains(line, "▍ §7") || StringUtils.contains(line, "▍ §7ValueFinder") ||
                            StringUtils.contains(line, "▍ §7Nuke") || StringUtils.contains(line, "▍ §7JackHammer") ||
                            StringUtils.contains(line, "▍ §7Fortune") || StringUtils.contains(line, "▍ §7Token Greed")) {
                        newLore.add(line);
                    }
                }

                meta.setLore(newLore);
                itemInHand.setItemMeta(meta);
                String perkReceived = getPickResetPerk(player, data.getPickaxeResetAmount());
                Bukkit.broadcastMessage("§a" + player.getName() + " §7made his §f§l" + ordinal(data.getPickaxeResetAmount()) + " Pick Reset");
                if (data.getPickaxeResetAmount() < 10) {
                    Bukkit.broadcastMessage("§7and received §e" + perkReceived + " §7Perk");
                }

                if (data.getRebirthPointsSpended() != null) {
                    mainClass.addRebirthPoints(player, data.getRebirthPointsSpended());
                    player.sendMessage(prefix + "§f" + data.getRebirthPointsSpended() + " §7Rebirth Points has been added to your account");
                    data.setRebirthPointsSpended(0);
                }
                /*if (data.getFarmerPointsSpended() != null) {
                    mainClass.addCropsPoints(player, data.getFarmerPointsSpended(), false);
                    player.sendMessage(prefix + "§f" + data.getFarmerPointsSpended() + " §7Farmer Points has been added to your account");
                    data.setFarmerPointsSpended(0);
                }*/
                mainClass.getPlayerMomentum().remove(player.getUniqueId());
                completed = true;
            } else {
                player.sendMessage(prefix + "§cOnly diamond pickaxe allowed");
            }
        } else {
            player.sendMessage(prefix + "§cYou reached the max Pick Reset you can do.");
        }

        return completed;
    }

    public String getPickResetPerk(Player player, int resetNumber) {
        String perk = Strings.EMPTY;
        if (resetNumber == 1) {
            perk = "Money Pouch Finder";
            mainClass.addPex(player, "pickreset.moneypouch", 0, false);
        } else if (resetNumber == 2) {
            perk = "1.5x Money and Token Boosters from Keys";
            mainClass.addPex(player, "pickreset.keysboosters", 0, false);
        } else if (resetNumber == 3) {
            perk = "WonderLand CD reduction";
            mainClass.addPex(player, "pickreset.wonderland", 0, false);
        } else if (resetNumber == 4) {
            perk = "JackHammer Double Layer";
            mainClass.addPex(player, "pickreset.jackhammer", 0, false);
        } else if (resetNumber == 5) {
            perk = "Value Finder Booster";
            mainClass.addPex(player, "pickreset.valuefinder", 0, false);
        } else if (resetNumber == 6) {
            perk = "Token Pouch Finder";
            mainClass.addPex(player, "pickreset.tokenpouch", 0, false);
        } else if (resetNumber == 7) {
            perk = "1.2x Block Value";
            mainClass.addPex(player, "pickreset.blockvalue", 0, false);
        } else if (resetNumber == 8) {
            perk = "Half Requirements for Milestones";
            mainClass.addPex(player, "pickreset.milestones", 0, false);
        } else if (resetNumber == 9) {
            perk = "Half Requirements for Skills";
            mainClass.addPex(player, "pickreset.skills", 0, false);
        } else if (resetNumber == 10) {
            perk = "idk";
            mainClass.addPex(player, "pickreset.idk", 0, false);
        }

        return perk;
    }

    String[] suffixes = new String[] { "th", "st", "nd", "rd", "th", "th", "th", "th", "th", "th" };
    public String ordinal (int i) {
        switch (i % 100) {
            case 11:
            case 12:
            case 13:
                return i + "th";
            default:
                return i + suffixes[i % 10];

        }
    }
}
