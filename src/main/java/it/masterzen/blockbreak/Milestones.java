package it.masterzen.blockbreak;

import it.masterzen.MongoDB.DataTypes.MilestonePojo;
import org.apache.commons.lang.StringUtils;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;

import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

public class Milestones {

    private final String prefix = "§e§lMILESTONES §8»§7 ";
    public final AlphaBlockBreak mainClass;

    public enum Stones {
        MONEY,
        TOKENS,
        KEYS,
        XP,
        //ROBOTS,
        RANKS,
        MONTHLIES
    }

    public Milestones(AlphaBlockBreak plugin) {
        mainClass = plugin;
    }

    public void addRandomMilestone(Player player) {
        Stones randomMilestone = Stones.values()[ThreadLocalRandom.current().nextInt(Stones.values().length)];
        if (randomMilestone.equals(Stones.MONEY)) {
            mainClass.getMongoReader().getPlayerData(player.getUniqueId()).addMoneyBooster(1D);
            player.sendMessage(prefix + "You found a Money Booster ( +10% )");
        } else if (randomMilestone.equals(Stones.TOKENS)) {
            mainClass.getMongoReader().getPlayerData(player.getUniqueId()).addTokenBooster(1D);
            player.sendMessage(prefix + "You found a Token Booster ( +10% )");
        } else if (randomMilestone.equals(Stones.KEYS)) {
            mainClass.getMongoReader().getPlayerData(player.getUniqueId()).addKeysBooster(1D);
            player.sendMessage(prefix + "You found a Key Booster ( +1 Key )");
        } else if (randomMilestone.equals(Stones.XP)) {
            mainClass.getMongoReader().getPlayerData(player.getUniqueId()).addXpBooster(1D);
            player.sendMessage(prefix + "You found a XP Booster ( +10% )");
        } /*else if (randomMilestone.equals(Stones.ROBOTS)) {
            int tier = ThreadLocalRandom.current().nextInt(2) + 8;
            int amount = ThreadLocalRandom.current().nextInt(10) + 1;
            mainClass.getRobotSystem().addPoints(player, tier, amount);
            player.sendMessage(prefix + "You found " + amount + "x Tier " + tier + " Robots");
        } */else if (randomMilestone.equals(Stones.RANKS)) {
            String playerVip = mainClass.getPlayerVip(player);
            if (StringUtils.equalsIgnoreCase(playerVip, "Alpha") || StringUtils.equalsIgnoreCase(playerVip, "Alpha+")) {
                addRandomMilestone(player);
                return;
            }
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), Enums.Ranks.values()[ThreadLocalRandom.current().nextInt(Enums.Ranks.values().length)].getCommand().replace("%player%", player.getName()));
        } else if (randomMilestone.equals(Stones.MONTHLIES)) {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "amc give " + player.getName() + " AlphaMonthlyCrate 1");
            player.sendMessage(prefix + "You found a Monthly Crate");
        }

        mainClass.getMongoReader().saveMilestoneRecord(player, randomMilestone.toString());
    }

    public void sendMilestones(Player player) {
        List<MilestonePojo> milestoneList = mainClass.getMongoReader().getPlayerMilestone(player);
        if (milestoneList != null && !milestoneList.isEmpty()) {
            player.sendMessage("§e§lMILESTONES §f| History");
            for (MilestonePojo milestone : milestoneList) {
                if (milestone.getMilestone().equals("MONEY")) {
                    player.sendMessage("    " + milestone.getCreationDate() + " - Money Boost");
                } else if (milestone.getMilestone().equals("TOKENS")) {
                    player.sendMessage("    " + milestone.getCreationDate() + " - Token Boost");
                } else if (milestone.getMilestone().equals("KEYS")) {
                    player.sendMessage("    " + milestone.getCreationDate() + " - Key Boost");
                } else if (milestone.getMilestone().equals("XP")) {
                    player.sendMessage("    " + milestone.getCreationDate() + " - XP Boost");
                } else if (milestone.getMilestone().equals("ROBOTS")) {
                    player.sendMessage("    " + milestone.getCreationDate() + " - Robots");
                } else if (milestone.getMilestone().equals("RANKS")) {
                    player.sendMessage("    " + milestone.getCreationDate() + " - Rank Voucher");
                } else if (milestone.getMilestone().equals("MONTHLIES")) {
                    player.sendMessage("    " + milestone.getCreationDate() + " - Monthly Crate");
                }
            }
        } else {
            player.sendMessage(prefix + "You have not received any milestone yet");
        }
    }

    public void sendMilestonesList(Player player) {
        player.sendMessage("§e§lMILESTONES §f| List");
        player.sendMessage("§7- §eMoney Booster");
        player.sendMessage("§7- §aToken Booster");
        player.sendMessage("§7- §fKey Boost");
        player.sendMessage("§7- §fXP Boost");
        player.sendMessage("§7- §fSeasonal Rank Voucher");
        player.sendMessage("§7- §fMonthly Crate");
    }
}
