package it.masterzen.blockbreak;


import it.masterzen.MongoDB.PlayerData;
import it.masterzen.commands.Main;
import org.bukkit.Bukkit;
import org.bukkit.CropState;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.block.BlockState;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.material.Crops;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class CropsManager implements Listener {

    private final String prefix = "§e§lFARMER §8»§7 ";

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (event.getClickedInventory() != null && event.getClickedInventory().equals(event.getView().getTopInventory())) {
            if (event.getView().getTitle().equals("§e§lFARMER §f| §7Shop")) {
                Player player = (Player) event.getWhoClicked();
                event.setCancelled(true);

                if (event.getSlot() == 12) {
                    if (event.isLeftClick()) {
                        giveNukeLevels(player, false);
                    } else if (event.isRightClick()) {
                        giveNukeLevels(player, true);
                    }
                } else if (event.getSlot() == 14) {
                    if (event.isLeftClick()) {
                        giveJackHammerLevels(player, false);
                    } else if (event.isRightClick()) {
                        giveJackHammerLevels(player, true);
                    }
                }
            }
        }
    }

    public void giveNukeLevels(Player player, int levels) {
        ItemMeta meta;
        List<String> Lore = new ArrayList<>();
        ItemStack nukeBook = XMaterial.BOOK.parseItem();
        assert nukeBook != null;
        meta = nukeBook.getItemMeta();
        meta.setDisplayName("§6§lNUKE §7| Level");
        Lore.add("");
        Lore.add("§7Drag and drop in a pickaxe");
        Lore.add("§7to add §a§l" + levels + " §7level of Nuke");
        Lore.add("");
        meta.setLore(Lore);
        nukeBook.setItemMeta(meta);
        player.getInventory().addItem(nukeBook);
        player.sendMessage(prefix + "You received §a§l" + levels + " Level of Nuke");
    }

    public void giveJackHammerLevels(Player player, int levels) {
        ItemMeta meta;
        List<String> Lore = new ArrayList<>();
        ItemStack jackHammerBook = XMaterial.BOOK.parseItem();
        assert jackHammerBook != null;
        meta = jackHammerBook.getItemMeta();
        meta.setDisplayName("§6§lJACKHAMMER §7| Level");
        Lore.add("");
        Lore.add("§7Drag and drop in a pickaxe");
        Lore.add("§7to add §a§l" + levels + " §7level of JackHammer");
        Lore.add("");
        meta.setLore(Lore);
        jackHammerBook.setItemMeta(meta);
        player.getInventory().addItem(jackHammerBook);
        player.sendMessage(prefix + "You received §a§l" + levels + " Level of JackHammer");
    }

    public void giveNukeLevels(Player player, boolean max) {
        AlphaBlockBreak main = AlphaBlockBreak.GetInstance();
        long points = main.getPlayerCropsPoints(player);
        int totalLevels = 0;

        PlayerData data = main.getMongoReader().getPlayerData(player.getUniqueId());
        long price = 1000;
        if (data.getFarmerDiscount() != null) {
            price = 1000 - data.getFarmerDiscount();
        }

        if (points >= price) {
            if (max) {
                int maxIterations = 500;
                while (maxIterations > 0 && points >= price) {
                    maxIterations--;
                    points = points - price;
                }
                totalLevels = 500 - maxIterations;
            } else {
                totalLevels = 1;
            }

            if (totalLevels > 0) {
                if (data.getFarmerPointsSpended() == null) {
                    data.setFarmerPointsSpended(0);
                }
                data.setFarmerCropsFarmed((int) (data.getFarmerCropsFarmed() + (price * totalLevels)));
                main.removeCropsPoints(player, (price * totalLevels));

                ItemMeta meta;
                List<String> Lore = new ArrayList<>();
                ItemStack nukeBook = XMaterial.BOOK.parseItem();
                assert nukeBook != null;
                meta = nukeBook.getItemMeta();
                meta.setDisplayName("§6§lNUKE §7| Level");
                Lore.add("");
                Lore.add("§7Drag and drop in a pickaxe");
                Lore.add("§7to add §a§l" + totalLevels + " §7level of Nuke");
                Lore.add("");
                meta.setLore(Lore);
                nukeBook.setItemMeta(meta);
                player.getInventory().addItem(nukeBook);
                player.sendMessage(prefix + "You succesfully bought §a§l" + totalLevels + " Level of Nuke");
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough points ! Go farm at /warp Farmer");
        }
    }

    public void giveJackHammerLevels(Player player, boolean max) {
        AlphaBlockBreak main = AlphaBlockBreak.GetInstance();
        long points = main.getPlayerCropsPoints(player);
        int totalLevels = 0;

        PlayerData data = main.getMongoReader().getPlayerData(player.getUniqueId());
        long price = 1000;
        if (data.getFarmerDiscount() != null) {
            price = 1000 - data.getFarmerDiscount();
        }

        if (points >= price) {
            if (max) {
                int maxIterations = 500;
                while (maxIterations > 0 && points >= price) {
                    maxIterations--;
                    points = points - price;
                }
                totalLevels = 500 - maxIterations;
            } else {
                totalLevels = 1;
            }

            if (totalLevels > 0) {
                if (data.getFarmerPointsSpended() == null) {
                    data.setFarmerPointsSpended(0);
                }
                data.setFarmerCropsFarmed((int) (data.getFarmerCropsFarmed() + (price * totalLevels)));
                main.removeCropsPoints(player, (price * totalLevels));

                ItemMeta meta;
                List<String> Lore = new ArrayList<>();
                ItemStack jackHammerBook = XMaterial.BOOK.parseItem();
                assert jackHammerBook != null;
                meta = jackHammerBook.getItemMeta();
                meta.setDisplayName("§6§lJACKHAMMER §7| Level");
                Lore.add("");
                Lore.add("§7Drag and drop in a pickaxe");
                Lore.add("§7to add §a§l" + totalLevels + " §7level of JackHammer");
                Lore.add("");
                meta.setLore(Lore);
                jackHammerBook.setItemMeta(meta);
                player.getInventory().addItem(jackHammerBook);
                player.sendMessage(prefix + "You succesfully bought §a§l" + totalLevels + " Level of JackHammer");
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough points ! Go farm at /warp Farmer");
        }
    }

    public boolean isFullyGrown(Block block) {
        BlockState state = block.getState();
        Crops cropData = (Crops) state.getData();
        CropState cropState = cropData.getState();

        // CropState.SEEDED -> New crop
        // CropState.RIPE   -> Fully grown

        if (cropState == CropState.RIPE) {
            return true;
        } else {
            return false;
        }
    }

    public void setNewCrop(Block block) {
        block.getState().getData().setData(CropState.SEEDED.getData());
    }

    public void openShop(Player player) {
        Inventory gui = Bukkit.createInventory(null, 27, "§e§lFARMER §f| §7Shop");
        Main.FillBorder(gui);

        PlayerData data = AlphaBlockBreak.GetInstance().getMongoReader().getPlayerData(player.getUniqueId());
        int price = 1000;
        if (data.getFarmerDiscount() != null) {
            price = 1000 - data.getFarmerDiscount();
        }
        ItemMeta meta;
        List<String> Lore = new ArrayList<>();
        ItemStack nukeBook = XMaterial.BOOK.parseItem();
        assert nukeBook != null;
        meta = nukeBook.getItemMeta();
        meta.setDisplayName("§6§lNUKE §7| Level");
        Lore.add("");
        Lore.add("§e§lPRICE");
        Lore.add("§e| §e" + price + " §fPoints");
        Lore.add("");
        Lore.add("§7§lDESCRIPTION");
        Lore.add("§7| §fDrag and drop in a pickaxe");
        Lore.add("§7| §fto add §71 §flevel of Nuke");
        Lore.add("");
        Lore.add("§6§lUSAGE");
        Lore.add("§6| §fLeft Click: §61x");
        Lore.add("§6| §fRight click: §6Max Use");
        Lore.add("");
        meta.setLore(Lore);
        nukeBook.setItemMeta(meta);

        ItemStack jackHammerBook = XMaterial.BOOK.parseItem();
        assert jackHammerBook != null;
        meta = jackHammerBook.getItemMeta();
        meta.setDisplayName("§6§lJACKHAMMER §7| Level");
        Lore.clear();
        Lore.add("");
        Lore.add("§e§lPRICE");
        Lore.add("§e| §e" + price + " §fPoints");
        Lore.add("");
        Lore.add("§7§lDESCRIPTION");
        Lore.add("§7| §fDrag and drop in a pickaxe");
        Lore.add("§7| §fto add §71 §flevel of JackHammer");
        Lore.add("");
        Lore.add("§6§lUSAGE");
        Lore.add("§6| §fLeft Click: §61x");
        Lore.add("§6| §fRight click: §6Max Use");
        Lore.add("");
        meta.setLore(Lore);
        jackHammerBook.setItemMeta(meta);

        long points = AlphaBlockBreak.GetInstance().getPlayerCropsPoints(player);
        ItemStack information = new ItemStack(Material.BOOK);
        meta = information.getItemMeta();
        meta.setDisplayName("§6§lINFORMATION");
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lBALANCE");
        lore.add("§e| §f" + points);
        lore.add("");
        meta.setLore(lore);
        information.setItemMeta(meta);

        gui.setItem(4, information);
        gui.setItem(12, nukeBook);
        gui.setItem(14, jackHammerBook);

        player.openInventory(gui);
    }

}
