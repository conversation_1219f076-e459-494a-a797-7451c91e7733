package it.masterzen.blockbreak;

import java.time.LocalDateTime;

public class Momentum {

    private LocalDateTime lastBlockMinedTime;
    private int boost;
    private boolean is3SecondTimerGoing;
    private boolean is10SecondTimerGoing;

    public Momentum(LocalDateTime lastBlockMinedTime, int boost, boolean is3SecondTimerGoing) {
        this.lastBlockMinedTime = lastBlockMinedTime;
        this.boost = boost;
        this.is3SecondTimerGoing = is3SecondTimerGoing;
    }

    public LocalDateTime getLastBlockMinedTime() {
        return lastBlockMinedTime;
    }

    public void setLastBlockMinedTime(LocalDateTime lastBlockMinedTime) {
        this.lastBlockMinedTime = lastBlockMinedTime;
    }

    public int getBoost() {
        return boost;
    }

    public void setBoost(int boost) {
        this.boost = boost;
    }

    public boolean isIs3SecondTimerGoing() {
        return is3SecondTimerGoing;
    }

    public void setIs3SecondTimerGoing(boolean is3SecondTimerGoing) {
        this.is3SecondTimerGoing = is3SecondTimerGoing;
    }

    public boolean isIs10SecondTimerGoing() {
        return is10SecondTimerGoing;
    }

    public void setIs10SecondTimerGoing(boolean is10SecondTimerGoing) {
        this.is10SecondTimerGoing = is10SecondTimerGoing;
    }
}
