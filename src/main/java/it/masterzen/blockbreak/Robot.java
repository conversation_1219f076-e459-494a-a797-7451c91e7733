package it.masterzen.blockbreak;

import com.google.gson.Gson;
import org.bukkit.Location;
import org.bukkit.configuration.serialization.ConfigurationSerializable;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class Robot implements ConfigurationSerializable {
    private final Location loc;
    private final int level;
    private final UUID owner;

    // add more
    public Robot(Location loc, int level, UUID owner) {
        this.loc = loc;
        this.level = level;
        this.owner = owner;
    }

    public Location getLocation() {
        return this.loc;
    }

    @Override
    public Map<String, Object> serialize() {

        Map<String, Object> robotMap = new HashMap<>();
        robotMap.put("level", this.level);
        robotMap.put("owner", this.owner);

        return robotMap;
    }

    public static Robot deserialize(String args) {
        Gson gson = new Gson();
        Map<String, Object> map = gson.fromJson(args.replace("\"", ""), Map.class);

        Robot robot = new Robot(null, (Integer) map.get("level"), UUID.fromString((String) map.get("owner")));

        return robot;
    }
}
