package it.masterzen.blockbreak;

/**
 * Represents the result of a key opening operation.
 */
public class KeyOpeningResult {

    private final boolean success;
    private final String errorMessage;
    private final int processedKeys;

    private KeyOpeningResult(boolean success, String errorMessage, int processedKeys) {
        this.success = success;
        this.errorMessage = errorMessage;
        this.processedKeys = processedKeys;
    }

    /**
     * Creates a successful result.
     */
    public static KeyOpeningResult success(int processedKeys) {
        return new KeyOpeningResult(true, null, processedKeys);
    }

    /**
     * Creates an error result.
     */
    public static KeyOpeningResult error(String errorMessage) {
        return new KeyOpeningResult(false, errorMessage, 0);
    }

    /**
     * Checks if the operation was successful.
     */
    public boolean isSuccess() {
        return success;
    }

    /**
     * Gets the error message if the operation failed.
     */
    public String getErrorMessage() {
        return errorMessage;
    }

    /**
     * Gets the number of processed keys.
     */
    public int getProcessedKeys() {
        return processedKeys;
    }
}