package it.masterzen.blockbreak;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.UUID;
import java.util.function.Consumer;

/**
 * Represents a block break processing task for delayed execution.
 * This class is kept for future async implementation but currently
 * uses a simpler delayed execution approach.
 */
public class BlockBreakTask implements Runnable {
    
    // Event data (immutable snapshots)
    private final UUID playerId;
    private final String playerName;
    private final Material blockType;
    private final byte blockData;
    private final String worldName;
    private final int blockX, blockY, blockZ;
    private final long eventTime;
    
    // Tool information
    private final ItemStack toolSnapshot;
    private final boolean hasPickaxe;
    
    // Processing context
    private final AlphaBlockBreak plugin;
    private final Consumer<BlockBreakResult> resultCallback;
    
    // Processing flags
    private volatile boolean cancelled = false;
    
    /**
     * Creates a new BlockBreakTask from a BlockBreakEvent.
     * 
     * @param event The original block break event
     * @param plugin The plugin instance
     * @param resultCallback Callback to handle the processing result
     */
    public BlockBreakTask(BlockBreakEvent event, AlphaBlockBreak plugin, Consumer<BlockBreakResult> resultCallback) {
        this.plugin = plugin;
        this.resultCallback = resultCallback;
        this.eventTime = System.currentTimeMillis();
        
        // Create immutable snapshots of event data
        Player player = event.getPlayer();
        Block block = event.getBlock();

        this.playerId = player.getUniqueId();
        this.playerName = player.getName();
        this.blockType = block.getType();
        this.blockData = block.getData();
        this.worldName = block.getWorld().getName();
        this.blockX = block.getX();
        this.blockY = block.getY();
        this.blockZ = block.getZ();
        
        // Create tool snapshot
        ItemStack mainHand = player.getInventory().getItemInMainHand();
        this.toolSnapshot = mainHand != null ? mainHand.clone() : null;
        this.hasPickaxe = mainHand != null && mainHand.getType() == Material.DIAMOND_PICKAXE;
    }
    
    @Override
    public void run() {
        if (cancelled) {
            return;
        }
        
        try {
            // Process the block break asynchronously
            BlockBreakResult result = processBlockBreak();
            
            // Execute callback on main thread if needed
            if (resultCallback != null) {
                // Execute callback immediately
                resultCallback.accept(result);
            }
            
        } catch (Exception e) {
            plugin.getLogger().warning("Error processing block break task for player " + playerName + ": " + e.getMessage());
            
            // Send error result
            if (resultCallback != null) {
                BlockBreakResult errorResult = BlockBreakResult.error("Processing failed: " + e.getMessage());
                new BukkitRunnable() {
                    @Override
                    public void run() {
                        resultCallback.accept(errorResult);
                    }
                }.runTask(plugin);
            }
        }
    }
    
    /**
     * Processes the block break event asynchronously.
     * This method does lightweight processing and schedules the main work on the main thread.
     */
    private BlockBreakResult processBlockBreak() {
        // Get player (might be null if offline)
        Player player = Bukkit.getPlayer(playerId);
        if (player == null || !player.isOnline()) {
            return BlockBreakResult.cancelled("Player is offline");
        }

        // Validate world and block still exist
        if (!isValidBlock()) {
            return BlockBreakResult.cancelled("Block no longer valid");
        }

        try {
            // Do any async-safe preprocessing here
            // For example: database lookups, calculations that don't involve blocks

            // Schedule the main block break logic to run on the main thread
            executeOriginalBlockBreakLogic(player);

            return BlockBreakResult.success();

        } catch (Exception e) {
            plugin.getLogger().warning("Error in async block break processing: " + e.getMessage());
            return BlockBreakResult.error("Processing failed: " + e.getMessage());
        }
    }

    /**
     * Executes the block break logic on the main thread.
     * This schedules the original block break processing to run on the main thread
     * to avoid "Asynchronous block remove!" errors while still doing the heavy
     * computational work in the background thread.
     */
    private void executeOriginalBlockBreakLogic(Player player) throws Exception {
        // Schedule the block break processing to run on the main thread
        // This avoids the "Asynchronous block remove!" error

        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    // Get block from snapshot data
                    Block block = Bukkit.getWorld(worldName).getBlockAt(blockX, blockY, blockZ);

                    // Verify the block still exists and is the same type
//                    if (block.getType() != blockType) {
//                        plugin.getLogger().fine("Block changed during async processing for " + playerName +
//                                              " at " + worldName + ":" + blockX + "," + blockY + "," + blockZ);
//                        return;
//                    }

                    // Verify player is still online and in the same world
                    if (!player.isOnline() || !player.getWorld().getName().equals(worldName)) {
                        plugin.getLogger().fine("Player offline or changed worlds during async processing: " + playerName);
                        return;
                    }

                    // Create a new BlockBreakEvent for processing
                    BlockBreakEvent syntheticEvent = new BlockBreakEvent(block, player);
                    syntheticEvent.setDropItems(false);

                    // Prepare block data
                    BlockBreakData blockBreakData = new BlockBreakData(blockType, blockData, worldName, blockX, blockY, blockZ, block);

                    // Call the original method on the main thread
                    // This will handle all the complex logic (rewards, pets, enchantments, etc.)
                    ((AlphaBlockBreak) plugin).blockbreakOriginal(syntheticEvent, blockBreakData);

                    plugin.getLogger().fine("Successfully processed block break for " + playerName +
                                          " at " + worldName + ":" + blockX + "," + blockY + "," + blockZ);

                } catch (Exception e) {
                    plugin.getLogger().warning("Error in block break processing for " + playerName + ": " + e.getMessage());
                    e.printStackTrace();
                    // Don't print full stack trace for async block errors as they're expected
//                    if (!e.getMessage().contains("Asynchronous")) {
//                        e.printStackTrace();
//                    }
                }
            }
        }.runTask(plugin); // Run on main thread to avoid async block modification errors

        // Log that we've scheduled the processing
        plugin.getLogger().fine("Scheduled main thread block break processing for " + playerName +
                              " at " + worldName + ":" + blockX + "," + blockY + "," + blockZ);
    }
    
    private boolean isValidBlock() {
        // Validate that the block still exists and is in the expected state
        // This is a safety check for async processing
        return Bukkit.getWorld(worldName) != null;
    }
    
    /**
     * Cancels this task if it hasn't started processing yet.
     */
    public void cancel() {
        this.cancelled = true;
    }
    
    /**
     * Gets the player ID for this task.
     */
    public UUID getPlayerId() {
        return playerId;
    }
    
    /**
     * Gets the event timestamp.
     */
    public long getEventTime() {
        return eventTime;
    }
    
    /**
     * Gets the block type being processed.
     */
    public Material getBlockType() {
        return blockType;
    }

}
