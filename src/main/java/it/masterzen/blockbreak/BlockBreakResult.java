package it.masterzen.blockbreak;

import java.util.HashMap;
import java.util.Map;

/**
 * Represents the result of processing a block break event.
 * Contains all the calculated rewards, effects, and actions that need to be applied.
 */
public class BlockBreakResult {
    
    private final boolean success;
    private final boolean cancelled;
    private final String errorMessage;
    
    private BlockBreakResult(boolean success, boolean cancelled, String errorMessage) {
        this.success = success;
        this.cancelled = cancelled;
        this.errorMessage = errorMessage;
    }
    
    // Getters
    public boolean isSuccess() { return success; }
    public boolean isCancelled() { return cancelled; }
    public String getErrorMessage() { return errorMessage; }
    
    // Static factory methods
    public static BlockBreakResult success() {
        return new BlockBreakResult(true, false, null);
    }

    public static BlockBreakResult cancelled(String reason) {
        return new BlockBreakResult(false, true, reason);
    }

    public static BlockBreakResult error(String message) {
        return new BlockBreakResult(false, false, message);
    }


    @Override
    public String toString() {
        return String.format(
            "BlockBreakResult{success=%s, cancelled=%s, errorMessage='%s'}",
            success, cancelled, errorMessage
        );
    }
}
