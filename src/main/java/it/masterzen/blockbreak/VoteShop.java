package it.masterzen.blockbreak;

import com.sk89q.worldedit.MaxChangedBlocksException;
import it.masterzen.MongoDB.PlayerData;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import java.io.IOException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

public class VoteShop implements Listener {

    private final String prefix = "§e§lVOTE SHOP §8»§7 ";
    public final AlphaBlockBreak mainClass;

    public VoteShop(AlphaBlockBreak plugin) {
        mainClass = plugin;
    }

    public void addVotePoints(Player player, int amount) {
        PlayerData data = mainClass.getMongoReader().getPlayerData(player.getUniqueId());
        data.addVotePoints(amount);
        player.sendMessage(prefix + "You received " + amount + " Vote Point/s");
    }

    public void removeVotePoints(Player player, int amount) {
        PlayerData data = mainClass.getMongoReader().getPlayerData(player.getUniqueId());
        data.removeVotePoints(amount);
        player.sendMessage(prefix + amount + " Vote Point/s has been removed from your account");
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) throws IOException, MaxChangedBlocksException {
        if (event.getClickedInventory() != null && event.getClickedInventory().equals(event.getView().getTopInventory())) {
            Player player = (Player) event.getWhoClicked();

            if (event.getView().getTitle().equalsIgnoreCase("§e§lVOTE §f| §7Shop")) {
                event.setCancelled(true);

                if (event.getSlot() == 11) {
                    if (event.isLeftClick()) {
                        giveTokenBooster(player);
                    }
                } else if (event.getSlot() == 13) {
                    if (event.isLeftClick()) {
                        giveMoneyBooster(player);
                    }
                } else if (event.getSlot() == 15) {
                    if (event.isLeftClick()) {
                        giveClassicLootbox(player);
                    }
                }
            }
        }
    }

    public void giveMoneyBooster(Player player) {
        Integer votePoints = mainClass.getMongoReader().getPlayerData(player.getUniqueId()).getVotePoints();
        if (votePoints != null && votePoints >= 10) {
            removeVotePoints(player, 10);
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "lp user " + player.getName() + " permission settemp voteshop.moneybooster true 30m accumulate"); // CAMBIA ANCHE SOTTO
            player.sendMessage(prefix + "Your money booster is now active !");
            Duration totalDuration = mainClass.getPexDuration(player, "voteshop.moneybooster");
            if (totalDuration != null) {
                player.sendMessage("§7Total duration: §f" + (totalDuration.toMinutes() + 30) + " minutes");
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough points");
        }

        openGUI(player);
    }

    public void giveTokenBooster(Player player) {
        Integer votePoints = mainClass.getMongoReader().getPlayerData(player.getUniqueId()).getVotePoints();
        if (votePoints != null && votePoints >= 10) {
            removeVotePoints(player, 10);
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "lp user " + player.getName() + " permission settemp voteshop.tokenbooster true 30m accumulate"); // CAMBIA ANCHE SOTTO
            player.sendMessage(prefix + "Your token booster is now active !");
            Duration totalDuration = mainClass.getPexDuration(player, "voteshop.tokenbooster");
            if (totalDuration != null) {
                player.sendMessage("§7Total duration: §f" + (totalDuration.toMinutes() + 30) + " minutes");
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough points");
        }

        openGUI(player);
    }

    public void giveClassicLootbox(Player player) {
        Integer votePoints = mainClass.getMongoReader().getPlayerData(player.getUniqueId()).getVotePoints();
        if (votePoints != null && votePoints >= 70) {
            removeVotePoints(player, 70);
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "amc give " + player.getName() + " LootBoxC 1");
            player.sendMessage(prefix + "You received a LootBox Classic");
        } else {
            player.sendMessage(prefix + "§cYou don't have enough points");
        }

        openGUI(player);
    }

    public void openGUI(Player player) {
        Inventory gui = Bukkit.createInventory(null, 27, "§e§lVOTE §f| §7Shop");
        it.masterzen.commands.Main.FillBorder(gui);

        Integer votePoints = mainClass.getMongoReader().getPlayerData(player.getUniqueId()).getVotePoints();
        if (votePoints == null) {
            votePoints = 0;
        }
        ItemStack information = new ItemStack(Material.BOOK);
        ItemMeta meta = information.getItemMeta();
        meta.setDisplayName("§6§lINFORMATION");
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lBALANCE");
        lore.add("§e| §f" + votePoints);
        lore.add("");
        meta.setLore(lore);
        information.setItemMeta(meta);

        ItemStack money = new ItemStack(Material.PAPER);
        meta = money.getItemMeta();
        meta.setDisplayName("§e§l2x Money Booster §f| §730 minutes");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e10 §fPoints");
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fYou shall receive an §72x AUTOSELL booster §ffor 30 minutes on purchase!");
        lore.add("§7| §fBuying multiple at once will only result in more time.");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fClick me to buy");
        lore.add("");
        meta.setLore(lore);
        money.setItemMeta(meta);

        ItemStack tokens = new ItemStack(Material.MAGMA_CREAM);
        meta = tokens.getItemMeta();
        meta.setDisplayName("§a§l2x Token Booster §f| §730 minutes");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e10 §fPoints");
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fYou shall receive an §72x TOKENGREED booster §ffor 30 minutes on purchase!");
        lore.add("§7| §fBuying multiple at once will only result in more time.");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fClick me to buy");
        lore.add("");
        meta.setLore(lore);
        tokens.setItemMeta(meta);

        ItemStack lootboxClassic = new ItemStack(Material.DROPPER);
        meta = lootboxClassic.getItemMeta();
        meta.setDisplayName("§a§l1x LootBox §f| §7Classic");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e70 §fPoints");
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fYou will receive §71x Lootbox Classic");
        lore.add("§7| §fYou can see the content in the /buy");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fClick me to buy");
        lore.add("");
        meta.setLore(lore);
        lootboxClassic.setItemMeta(meta);

        gui.setItem(4, information);
        gui.setItem(11, tokens);
        gui.setItem(13, money);
        gui.setItem(15, lootboxClassic);

        player.openInventory(gui);
    }
}
