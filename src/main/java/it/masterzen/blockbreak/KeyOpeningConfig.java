package it.masterzen.blockbreak;

import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.plugin.Plugin;

import java.io.File;
import java.io.IOException;
import java.util.logging.Level;

/**
 * Configuration class for async key opening system.
 * Manages thread pool settings, batch processing, rate limiting, and monitoring.
 */
public class KeyOpeningConfig {

    private final Plugin plugin;
    private final File configFile;
    private FileConfiguration config;

    // Cached configuration values for performance
    private boolean asyncEnabled;
    private int corePoolSize;
    private int maximumPoolSize;
    private long keepAliveTime;
    private int queueCapacity;
    private int threadPriority;

    // Batch processing settings
    private int batchSize;
    private long batchDelay;
    private int maxConcurrentBatches;

    // Rate limiting settings
    private boolean rateLimitingEnabled;
    private int maxKeysPerSecond;
    private int maxKeysPerPlayer;
    private long rateLimitWindow;

    // Monitoring settings
    private boolean monitoringEnabled;
    private int logInterval;
    private boolean detailedLogging;

    // Performance settings
    private int maxTaskTime;
    private boolean timeoutProtection;
    private int taskTimeout;
    private boolean syncFallback;

    // Progress feedback settings
    private boolean progressFeedbackEnabled;
    private int progressUpdateInterval;
    private int minKeysForProgress;

    // Debug settings
    private boolean debugEnabled;
    private boolean logTaskSubmissions;
    private boolean logCompletionTimes;
    private boolean logPoolState;
    private boolean logRejectedTasks;

    // Thresholds
    private double minTpsThreshold;
    private double tpsRecoveryThreshold;

    public KeyOpeningConfig(Plugin plugin) {
        this.plugin = plugin;
        this.configFile = new File(plugin.getDataFolder(), "key-opening-config.yml");
        loadConfiguration();
    }

    /**
     * Loads configuration from file or creates default if not exists.
     */
    public void loadConfiguration() {
        try {
            if (!configFile.exists()) {
                plugin.getDataFolder().mkdirs();
                configFile.createNewFile();
                config = YamlConfiguration.loadConfiguration(configFile);
                createDefaultConfig();
            } else {
                config = YamlConfiguration.loadConfiguration(configFile);
                loadValues();
            }

            plugin.getLogger().info("KeyOpening configuration loaded successfully");

        } catch (IOException e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to load KeyOpening configuration", e);
            setDefaultValues();
        }
    }

    /**
     * Creates default configuration file.
     */
    private void createDefaultConfig() {
        setDefaultValues();
        saveConfiguration();
    }

    /**
     * Loads values from configuration file.
     */
    private void loadValues() {
        // Threading settings
        asyncEnabled = config.getBoolean("threading.enabled", true);
        corePoolSize = config.getInt("threading.core-pool-size", 2);
        maximumPoolSize = config.getInt("threading.maximum-pool-size", 4);
        keepAliveTime = config.getLong("threading.keep-alive-time", 60);
        queueCapacity = config.getInt("threading.queue-capacity", 500);
        threadPriority = config.getInt("threading.thread-priority", 5);

        // Batch processing
        batchSize = config.getInt("batch-processing.batch-size", 10000);
        batchDelay = config.getLong("batch-processing.batch-delay", 50);
        maxConcurrentBatches = config.getInt("batch-processing.max-concurrent-batches", 3);

        // Rate limiting
        rateLimitingEnabled = config.getBoolean("rate-limiting.enabled", false);
        maxKeysPerSecond = config.getInt("rate-limiting.max-keys-per-second", 1000);
        maxKeysPerPlayer = config.getInt("rate-limiting.max-keys-per-player", 10000000);
        rateLimitWindow = config.getLong("rate-limiting.window-seconds", 60);

        // Monitoring
        monitoringEnabled = config.getBoolean("monitoring.enabled", true);
        logInterval = config.getInt("monitoring.log-interval", 300);
        detailedLogging = config.getBoolean("monitoring.detailed-logging", false);

        // Performance
        maxTaskTime = config.getInt("performance.max-task-time", 200);
        timeoutProtection = config.getBoolean("performance.timeout-protection", true);
        taskTimeout = config.getInt("performance.task-timeout", 10000);
        syncFallback = config.getBoolean("performance.sync-fallback", true);

        // Progress feedback
        progressFeedbackEnabled = config.getBoolean("progress.enabled", true);
        progressUpdateInterval = config.getInt("progress.update-interval", 1000);
        minKeysForProgress = config.getInt("progress.min-keys-for-progress", 100);

        // Debug
        debugEnabled = config.getBoolean("debug.enabled", false);
        logTaskSubmissions = config.getBoolean("debug.log-task-submissions", false);
        logCompletionTimes = config.getBoolean("debug.log-completion-times", false);
        logPoolState = config.getBoolean("debug.log-pool-state", false);
        logRejectedTasks = config.getBoolean("debug.log-rejected-tasks", true);

        // Thresholds
        minTpsThreshold = config.getDouble("thresholds.min-tps", 15.0);
        tpsRecoveryThreshold = config.getDouble("thresholds.tps-recovery", 18.0);
    }

    /**
     * Sets default configuration values.
     */
    private void setDefaultValues() {
        // Threading settings
        asyncEnabled = true;
        corePoolSize = 2;
        maximumPoolSize = 4;
        keepAliveTime = 60;
        queueCapacity = 500;
        threadPriority = 5;

        // Batch processing
        batchSize = 10000;
        batchDelay = 50;
        maxConcurrentBatches = 3;

        // Rate limiting
        rateLimitingEnabled = false;
        maxKeysPerSecond = 1000;
        maxKeysPerPlayer = 10000000;
        rateLimitWindow = 60;

        // Monitoring
        monitoringEnabled = true;
        logInterval = 300;
        detailedLogging = false;

        // Performance
        maxTaskTime = 200;
        timeoutProtection = true;
        taskTimeout = 10000;
        syncFallback = true;

        // Progress feedback
        progressFeedbackEnabled = true;
        progressUpdateInterval = 1000;
        minKeysForProgress = 100;

        // Debug
        debugEnabled = false;
        logTaskSubmissions = false;
        logCompletionTimes = false;
        logPoolState = false;
        logRejectedTasks = true;

        // Thresholds
        minTpsThreshold = 15.0;
        tpsRecoveryThreshold = 18.0;
    }

    /**
     * Saves current configuration to file.
     */
    public void saveConfiguration() {
        try {
            // Threading settings
            config.set("threading.enabled", asyncEnabled);
            config.set("threading.core-pool-size", corePoolSize);
            config.set("threading.maximum-pool-size", maximumPoolSize);
            config.set("threading.keep-alive-time", keepAliveTime);
            config.set("threading.queue-capacity", queueCapacity);
            config.set("threading.thread-priority", threadPriority);

            // Batch processing
            config.set("batch-processing.batch-size", batchSize);
            config.set("batch-processing.batch-delay", batchDelay);
            config.set("batch-processing.max-concurrent-batches", maxConcurrentBatches);

            // Rate limiting
            config.set("rate-limiting.enabled", rateLimitingEnabled);
            config.set("rate-limiting.max-keys-per-second", maxKeysPerSecond);
            config.set("rate-limiting.max-keys-per-player", maxKeysPerPlayer);
            config.set("rate-limiting.window-seconds", rateLimitWindow);

            // Monitoring
            config.set("monitoring.enabled", monitoringEnabled);
            config.set("monitoring.log-interval", logInterval);
            config.set("monitoring.detailed-logging", detailedLogging);

            // Performance
            config.set("performance.max-task-time", maxTaskTime);
            config.set("performance.timeout-protection", timeoutProtection);
            config.set("performance.task-timeout", taskTimeout);
            config.set("performance.sync-fallback", syncFallback);

            // Progress feedback
            config.set("progress.enabled", progressFeedbackEnabled);
            config.set("progress.update-interval", progressUpdateInterval);
            config.set("progress.min-keys-for-progress", minKeysForProgress);

            // Debug
            config.set("debug.enabled", debugEnabled);
            config.set("debug.log-task-submissions", logTaskSubmissions);
            config.set("debug.log-completion-times", logCompletionTimes);
            config.set("debug.log-pool-state", logPoolState);
            config.set("debug.log-rejected-tasks", logRejectedTasks);

            // Thresholds
            config.set("thresholds.min-tps", minTpsThreshold);
            config.set("thresholds.tps-recovery", tpsRecoveryThreshold);

            config.save(configFile);

        } catch (IOException e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to save KeyOpening configuration", e);
        }
    }

    // Getters for all configuration values
    public boolean isAsyncEnabled() { return asyncEnabled; }
    public int getCorePoolSize() { return corePoolSize; }
    public int getMaximumPoolSize() { return maximumPoolSize; }
    public long getKeepAliveTime() { return keepAliveTime; }
    public int getQueueCapacity() { return queueCapacity; }
    public int getThreadPriority() { return threadPriority; }

    public int getBatchSize() { return batchSize; }
    public long getBatchDelay() { return batchDelay; }
    public int getMaxConcurrentBatches() { return maxConcurrentBatches; }

    public boolean isRateLimitingEnabled() { return rateLimitingEnabled; }
    public int getMaxKeysPerSecond() { return maxKeysPerSecond; }
    public int getMaxKeysPerPlayer() { return maxKeysPerPlayer; }
    public long getRateLimitWindow() { return rateLimitWindow; }

    public boolean isMonitoringEnabled() { return monitoringEnabled; }
    public int getLogInterval() { return logInterval; }
    public boolean isDetailedLogging() { return detailedLogging; }

    public int getMaxTaskTime() { return maxTaskTime; }
    public boolean isTimeoutProtection() { return timeoutProtection; }
    public int getTaskTimeout() { return taskTimeout; }
    public boolean isSyncFallback() { return syncFallback; }

    public boolean isProgressFeedbackEnabled() { return progressFeedbackEnabled; }
    public int getProgressUpdateInterval() { return progressUpdateInterval; }
    public int getMinKeysForProgress() { return minKeysForProgress; }

    public boolean isDebugEnabled() { return debugEnabled; }
    public boolean isLogTaskSubmissions() { return logTaskSubmissions; }
    public boolean isLogCompletionTimes() { return logCompletionTimes; }
    public boolean isLogPoolState() { return logPoolState; }
    public boolean isLogRejectedTasks() { return logRejectedTasks; }

    public double getMinTpsThreshold() { return minTpsThreshold; }
    public double getTpsRecoveryThreshold() { return tpsRecoveryThreshold; }
}