package it.masterzen.blockbreak;

import java.util.ArrayList;
import java.util.List;

public class Cooldown {

    private List<String> featuresOnCooldown = new ArrayList<>();

    public Cooldown() {
        featuresOnCooldown = new ArrayList<>();
    }

    public void addFeature(String name) {
        this.featuresOnCooldown.add(name);
    }

    public void removeFeature(String name) {
        this.featuresOnCooldown.remove(name);
    }

    public boolean containsFeature(String name) {
        return this.featuresOnCooldown.contains(name);
    }

    public List<String> getFeaturesOnCooldown() {
        return this.featuresOnCooldown;
    }
}
