package it.masterzen.blockbreak;

import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.UUID;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Thread pool manager for async key opening operations.
 * Provides queue management, monitoring, and fallback mechanisms.
 */
public class KeyOpeningThreadPool {

    private final Plugin plugin;
    private final Logger logger;
    private final KeyOpeningConfig config;

    private ThreadPoolExecutor executor;
    private final ThreadFactory threadFactory;
    private boolean enabled;
    private boolean shutdown = false;

    // Configuration values
    private final int corePoolSize;
    private final int maximumPoolSize;
    private final long keepAliveTime;
    private final int queueCapacity;

    // Monitoring
    private final AtomicLong tasksSubmitted = new AtomicLong(0);
    private final AtomicLong tasksCompleted = new AtomicLong(0);
    private final AtomicLong tasksRejected = new AtomicLong(0);
    private final AtomicLong totalProcessingTime = new AtomicLong(0);

    // Rate limiting
    private final ConcurrentHashMap<UUID, AtomicInteger> playerKeyCount = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<UUID, Long> playerLastReset = new ConcurrentHashMap<>();

    // Monitoring task
    private BukkitRunnable monitoringTask;

    /**
     * Creates a new KeyOpeningThreadPool with configuration.
     */
    public KeyOpeningThreadPool(Plugin plugin, KeyOpeningConfig config) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.config = config;
        this.corePoolSize = config.getCorePoolSize();
        this.maximumPoolSize = config.getMaximumPoolSize();
        this.keepAliveTime = config.getKeepAliveTime();
        this.queueCapacity = config.getQueueCapacity();

        // Create custom thread factory for better thread naming and monitoring
        this.threadFactory = new KeyOpeningThreadFactory(config.getThreadPriority());

        initialize();
        if (config.isMonitoringEnabled() && config.getLogInterval() > 0) {
            startMonitoring();
        }
    }

    /**
     * Initializes the thread pool.
     */
    private void initialize() {
        try {
            enabled = config.isAsyncEnabled();

            if (!enabled) {
                logger.info("KeyOpening async processing is disabled in configuration");
                return;
            }

            // Create thread pool with custom rejection handler
            BlockingQueue<Runnable> workQueue = new LinkedBlockingQueue<>(queueCapacity);
            executor = new ThreadPoolExecutor(
                corePoolSize,
                maximumPoolSize,
                keepAliveTime,
                TimeUnit.SECONDS,
                workQueue,
                threadFactory,
                new KeyOpeningRejectionHandler()
            );

            // Allow core threads to timeout
            executor.allowCoreThreadTimeOut(true);

            logger.info("KeyOpening thread pool initialized: " +
                       "core=" + corePoolSize +
                       ", max=" + maximumPoolSize +
                       ", queue=" + queueCapacity);

        } catch (Exception e) {
            logger.log(Level.SEVERE, "Failed to initialize KeyOpening thread pool", e);
            enabled = false;
        }
    }

    /**
     * Submits a key opening task for asynchronous processing.
     */
    public CompletableFuture<Void> submitTask(KeyOpeningTask task) {
        if (!enabled || shutdown) {
            // If threading is disabled or shutdown, execute synchronously
            if (config.isDebugEnabled() && config.isLogTaskSubmissions()) {
                logger.info("Executing key opening task synchronously for player " + task.getPlayerId() +
                           " (threading disabled or shutdown)");
            }
            return CompletableFuture.runAsync(() -> {
                try {
                    task.run();
                } catch (Exception e) {
                    logger.log(Level.WARNING, "Error executing key opening task synchronously", e);
                }
            });
        }

        // Check rate limiting
        if (!checkRateLimit(task.getPlayerId(), task.getTotalKeys())) {
            logger.warning("Rate limit exceeded for player " + task.getPlayerName() +
                          " - rejecting key opening request");
            return CompletableFuture.completedFuture(null);
        }

        long startTime = System.nanoTime();
        tasksSubmitted.incrementAndGet();

        if (config.isDebugEnabled() && config.isLogTaskSubmissions()) {
            logger.info("Submitting key opening task for player " + task.getPlayerName() +
                       " (" + task.getTotalKeys() + " " + task.getKeyType() + " keys)");
        }

        return CompletableFuture.runAsync(() -> {
            long taskStartTime = System.nanoTime();
            try {
                task.run();
                tasksCompleted.incrementAndGet();
                long processingTime = System.nanoTime() - startTime;
                long taskTime = System.nanoTime() - taskStartTime;
                totalProcessingTime.addAndGet(processingTime);

                // Log slow tasks
                long taskTimeMs = taskTime / 1_000_000;
                if (taskTimeMs > config.getMaxTaskTime()) {
                    logger.warning("Slow key opening task detected: " + taskTimeMs + "ms for player " +
                                 task.getPlayerName() + ", " + task.getTotalKeys() + " " + task.getKeyType() + " keys");
                }

                if (config.isDebugEnabled() && config.isLogCompletionTimes()) {
                    logger.info("Key opening task completed for player " + task.getPlayerName() +
                               " in " + taskTimeMs + "ms");
                }

            } catch (Exception e) {
                logger.log(Level.WARNING, "Error executing key opening task for player " +
                          task.getPlayerName(), e);
            }
        }, executor);
    }

    /**
     * Checks rate limiting for a player.
     */
    private boolean checkRateLimit(UUID playerId, int keyCount) {
        if (!config.isRateLimitingEnabled()) {
            return true;
        }

        long currentTime = System.currentTimeMillis();
        long windowStart = currentTime - (config.getRateLimitWindow() * 1000);

        // Reset counter if window has passed
        Long lastReset = playerLastReset.get(playerId);
        if (lastReset == null || lastReset < windowStart) {
            playerKeyCount.put(playerId, new AtomicInteger(0));
            playerLastReset.put(playerId, currentTime);
        }

        AtomicInteger currentCount = playerKeyCount.computeIfAbsent(playerId, k -> new AtomicInteger(0));

        // Check if adding these keys would exceed the limit
        if (currentCount.get() + keyCount > config.getMaxKeysPerPlayer()) {
            return false;
        }

        // Add to counter
        currentCount.addAndGet(keyCount);
        return true;
    }

    /**
     * Starts monitoring task.
     */
    private void startMonitoring() {
        monitoringTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (enabled && !shutdown) {
                    logPoolStatistics();
                }
            }
        };

        monitoringTask.runTaskTimerAsynchronously(plugin,
            config.getLogInterval() * 20L,
            config.getLogInterval() * 20L);
    }

    /**
     * Logs pool statistics.
     */
    private void logPoolStatistics() {
        if (executor == null) return;

        long submitted = tasksSubmitted.get();
        long completed = tasksCompleted.get();
        long rejected = tasksRejected.get();
        long avgProcessingTime = submitted > 0 ? totalProcessingTime.get() / submitted / 1_000_000 : 0;

        logger.info("KeyOpening Pool Stats - " +
                   "Active: " + executor.getActiveCount() + "/" + executor.getMaximumPoolSize() +
                   ", Queue: " + executor.getQueue().size() + "/" + queueCapacity +
                   ", Submitted: " + submitted +
                   ", Completed: " + completed +
                   ", Rejected: " + rejected +
                   ", Avg Time: " + avgProcessingTime + "ms");

        if (config.isDetailedLogging()) {
            logger.info("KeyOpening Pool Details - " +
                       "Core: " + executor.getCorePoolSize() +
                       ", Pool: " + executor.getPoolSize() +
                       ", Largest: " + executor.getLargestPoolSize() +
                       ", Task Count: " + executor.getTaskCount() +
                       ", Completed Tasks: " + executor.getCompletedTaskCount());
        }
    }

    /**
     * Shuts down the thread pool.
     */
    public void shutdown() {
        shutdown = true;

        if (monitoringTask != null) {
            monitoringTask.cancel();
        }

        if (executor != null) {
            logger.info("Shutting down KeyOpening thread pool...");

            executor.shutdown();
            try {
                if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
                    logger.warning("KeyOpening thread pool did not terminate gracefully, forcing shutdown");
                    executor.shutdownNow();

                    if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                        logger.severe("KeyOpening thread pool did not terminate after forced shutdown");
                    }
                }
            } catch (InterruptedException e) {
                logger.warning("Interrupted while waiting for KeyOpening thread pool shutdown");
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }

            logger.info("KeyOpening thread pool shutdown complete");
        }
    }

    /**
     * Gets pool statistics.
     */
    public KeyOpeningPoolStats getStats() {
        if (executor == null) {
            return new KeyOpeningPoolStats(0, 0, 0, 0, 0, 0, 0, 0);
        }

        return new KeyOpeningPoolStats(
            executor.getActiveCount(),
            executor.getMaximumPoolSize(),
            executor.getQueue().size(),
            queueCapacity,
            tasksSubmitted.get(),
            tasksCompleted.get(),
            tasksRejected.get(),
            tasksSubmitted.get() > 0 ? totalProcessingTime.get() / tasksSubmitted.get() / 1_000_000 : 0
        );
    }

    /**
     * Checks if the thread pool is enabled.
     */
    public boolean isEnabled() {
        return enabled;
    }

    /**
     * Checks if the thread pool is shutdown.
     */
    public boolean isShutdown() {
        return shutdown;
    }

    /**
     * Custom thread factory for key opening threads.
     */
    private static class KeyOpeningThreadFactory implements ThreadFactory {
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final int priority;

        public KeyOpeningThreadFactory(int priority) {
            this.priority = Math.max(Thread.MIN_PRIORITY, Math.min(Thread.MAX_PRIORITY, priority));
        }

        @Override
        public Thread newThread(Runnable r) {
            Thread thread = new Thread(r, "KeyOpening-Worker-" + threadNumber.getAndIncrement());
            thread.setDaemon(true);
            thread.setPriority(priority);
            return thread;
        }
    }

    /**
     * Custom rejection handler for key opening tasks.
     */
    private class KeyOpeningRejectionHandler implements RejectedExecutionHandler {
        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
            tasksRejected.incrementAndGet();

            if (!executor.isShutdown()) {
                if (config.isLogRejectedTasks()) {
                    logger.warning("KeyOpening thread pool queue is full, executing task synchronously " +
                                  "(Queue: " + executor.getQueue().size() + "/" + queueCapacity +
                                  ", Active: " + executor.getActiveCount() + "/" + executor.getMaximumPoolSize() + ")");
                }

                // Execute synchronously as fallback if enabled
                if (config.isSyncFallback()) {
                    try {
                        r.run();
                    } catch (Exception e) {
                        logger.log(Level.WARNING, "Error executing rejected key opening task synchronously", e);
                    }
                } else {
                    logger.severe("KeyOpening thread pool rejected task and sync fallback is disabled - task dropped!");
                }
            }
        }
    }
}