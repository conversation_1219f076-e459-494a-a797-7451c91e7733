package it.masterzen.blockbreak;

import com.sk89q.worldguard.bukkit.WorldGuardPlugin;
import it.masterzen.commands.Main;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;

import java.util.ArrayList;
import java.util.List;

public class BeaconBackpack implements Listener {

    private AlphaBlockBreak mainClass;

    private final String prefix = "§e§lBEACONS §8»§7 ";

    public BeaconBackpack(AlphaBlockBreak plugin) {
        this.mainClass = plugin;
    }

    public String getPrefix() {
        return prefix;
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (event.getClickedInventory() != null && event.getClickedInventory().equals(event.getView().getTopInventory())) {
            if (event.getView().getTitle().equals("§e§lBEACON §f| §7Backpack")) {
                Player player = (Player) event.getWhoClicked();
                //ItemStack clickedItem = event.getCurrentItem();
                event.setCancelled(true);

                if (event.getSlot() == 13) {
                    int freeslot = /*36 - */mainClass.getEmptySlots(player.getInventory());
                    if (freeslot > 0) {
                        if (event.getClick().isLeftClick()) {
                            removeBeacons(player, 64);
                        } else if (event.getClick().isRightClick()) {
                            removeBeacons(player, getActualPoints(player.getInventory().getItemInMainHand()));
                            //removeBeacons(player, freeslot * 64L);
                        }
                        openGUI(player);
                    } else {
                        player.sendMessage(prefix + "§cYour inventory is full");
                    }
                } else if (event.getSlot() == 10) {
                    giveBeaconCollector(player);
                }
            }
        }
    }

    public long getActualPoints(ItemStack item) {
        String tmp;
        long currentAmount = 0;

        for (String line : item.getItemMeta().getLore()) {
            if (line.contains("§b§lBEACONS§7: ")) {
                tmp = line.replace("§b§lBEACONS§7: ", "");
                tmp = ChatColor.stripColor(tmp);
                currentAmount = Long.parseLong(tmp);
            }
        }

        return currentAmount;
    }

    public void giveBackpack(Player player) {
        ItemStack head = SkullCreator.itemFromBase64("eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvM2U1YWJkYjczNzQ1NTNkMDU2NWNiY2IzMjk1YWVkNTE1YTg5N2ViY2U5ZTBiYzYwZjFjMWY4YWU1NGM3NDlkZiJ9fX0=");
        SkullMeta meta;
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§b§lBEACONS§7: 0");
        lore.add("");
        meta = (SkullMeta) head.getItemMeta();
        meta.setLore(lore);
        meta.setDisplayName("§e§lBEACON §7- Backpack");
        head.setItemMeta(meta);
        player.getInventory().addItem(head);
        player.sendMessage(prefix + "You received 1x §e§lBEACON §7- Backpack");
    }

    public void giveBeaconCollector(Player player) {
        ItemStack collector = new ItemStack(Material.STICK);
        ItemMeta meta = getBeaconCollectorMeta();
        collector.setItemMeta(meta);
        player.getInventory().addItem(collector);
        player.sendMessage(prefix + "You received 1x §b§lBEACON §7Collector");
    }

    public ItemMeta getBeaconCollectorMeta() {
        ItemStack collector = new ItemStack(Material.STICK);
        ItemMeta meta = collector.getItemMeta();
        meta.setDisplayName("§b§lBEACON §7Collector");
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§7Right click a check to claim all the beacons");
        lore.add("§7and add it to your beacon backpack");
        lore.add("");
        lore.add("§7§o(( This collector will claim Beacons and Beacon Note ))");
        meta.setLore(lore);

        return meta;
    }

    public void addBeacons(Player player, long numbers) {
        boolean addedToBackpack = false;
        long currentAmount = 0;
        String tmp;

        for (ItemStack item : player.getInventory().getContents()) {
            if (item != null && item.hasItemMeta() && item.getType().equals(XMaterial.PLAYER_HEAD.parseMaterial()) && item.getItemMeta().hasDisplayName() && item.getItemMeta().getDisplayName().equals("§e§lBEACON §7- Backpack") && item.getItemMeta().hasLore() && !addedToBackpack) {
                List<String> newLore = new ArrayList<>();
                ItemMeta meta = item.getItemMeta();

                for (String line : item.getItemMeta().getLore()) {
                    if (line.contains("§b§lBEACONS§7: ")) {
                        tmp = line.replace("§b§lBEACONS§7: ", "");
                        tmp = ChatColor.stripColor(tmp);
                        currentAmount = Long.parseLong(tmp);
                        line = "§b§lBEACONS§7: " + (currentAmount + numbers);
                        //line = line.replace(currentAmount + "", (currentAmount + numbers) + "");
                        //line = line.replace("§8", "§7");
                        addedToBackpack = true;
                    }
                    newLore.add(line);
                }
                meta.setLore(newLore);
                item.setItemMeta(meta);
                if (!player.hasPermission("beacongreed.remove")) {
                    player.sendMessage(prefix + "§a§l" + numbers + " §7Beacons added to your Backpack");
                }
                player.updateInventory();
            }
        }

        if (!addedToBackpack) {
            ItemStack beacon = new ItemStack(Material.BEACON, (int) numbers);
            player.getInventory().addItem(beacon);
            player.updateInventory();
            //player.sendMessage(prefix + "You received §a§l" + numbers + " §7Beacons");
        }
    }

    public void removeBeacons(Player player, long numbers) {
        boolean hasEnoughBeacons = false;
        long currentAmount = 0;
        String tmp;

        //for (ItemStack item : player.getInventory().getContents()) {
        ItemStack itemInHand = player.getInventory().getItemInMainHand();
        if (itemInHand != null && itemInHand.hasItemMeta() && itemInHand.getType().equals(XMaterial.PLAYER_HEAD.parseMaterial()) && itemInHand.getItemMeta().hasDisplayName() && itemInHand.getItemMeta().getDisplayName().equals("§e§lBEACON §7- Backpack") && itemInHand.getItemMeta().hasLore()) {
            List<String> newLore = new ArrayList<>();
            ItemMeta meta = itemInHand.getItemMeta();

            for (String line : itemInHand.getItemMeta().getLore()) {
                if (line.contains("§b§lBEACONS§7: ")) {
                    tmp = line.replace("§b§lBEACONS§7: ", "");
                    tmp = ChatColor.stripColor(tmp);
                    currentAmount = Long.parseLong(tmp);
                    if (currentAmount >= numbers) {
                        line = line.replace(currentAmount + "", (currentAmount - numbers) + "");
                        hasEnoughBeacons = true;
                    } else if (currentAmount > 0) {
                        line = line.replace(currentAmount + "", 0 + "");
                        numbers = currentAmount;
                        hasEnoughBeacons = true;
                    }
                }
                newLore.add(line);
            }
            if (hasEnoughBeacons && numbers > 0) {
                meta.setLore(newLore);
                itemInHand.setItemMeta(meta);
                player.sendMessage(prefix + "§a§l" + numbers + " §7Beacons collected");

                ItemStack item = new ItemStack(Material.PAPER);
                meta = item.getItemMeta();
                meta.setDisplayName("§b§lBeacon Note §7(Right Click)");
                List<String> Lore = new ArrayList<>();
                Lore.add("§dType: §fBeacon");
                Lore.add("§dValue: §f" + numbers);
                meta.setLore(Lore);
                item.setItemMeta(meta);

                //player.getInventory().addItem(new ItemStack(Material.BEACON, (int) numbers));
                player.getInventory().addItem(item);
                player.updateInventory();
            } else {
                player.sendMessage(prefix + "§cYou don't have enough Beacon in the backpack");
            }
        }
        //}
    }

    public boolean hasBackpack(Player player) {
        boolean hasBackpack = false;

        for (ItemStack item : player.getInventory().getContents()) {
            if (item != null && item.getType().equals(XMaterial.PLAYER_HEAD.parseMaterial()) && item.hasItemMeta() && item.getItemMeta().getDisplayName().equals("§e§lBEACON §7- Backpack") && item.getItemMeta().hasLore()) {
                hasBackpack = true;
            }
        }

        return hasBackpack;
    }

    public boolean isBackpack(ItemStack item) {
        boolean isBackpack = false;

        if (item != null && item.getType().equals(XMaterial.PLAYER_HEAD.parseMaterial()) && item.hasItemMeta() && item.getItemMeta().getDisplayName().equals("§e§lBEACON §7- Backpack") && item.getItemMeta().hasLore()) {
            isBackpack = true;
        }

        return isBackpack;
    }

    public void stackBeacons(Player player) {
        if (hasBackpack(player)) {
            long totalBeacon = 0;
            for (ItemStack item : player.getInventory().getContents()) {
                if (item != null && item.getType() == Material.BEACON) {
                    totalBeacon = totalBeacon + item.getAmount();
                    mainClass.removeItemFromPlayer(player, null, Material.BEACON, true, true, 0);
                    //player.getInventory().remove(item);
                }
            }
            if (totalBeacon > 0) {
                addBeacons(player, totalBeacon);
            }
        } else {
            player.sendMessage(prefix + "§cYou need to have a Beacon Backpack in your inventory to use this command");
        }
    }

    public void openGUI(Player player) {
        Inventory gui = Bukkit.createInventory(null, 27, "§e§lBEACON §f| §7Backpack");
        Main.FillBorder(gui);

        ItemStack itemInHand = player.getInventory().getItemInMainHand();
        long currentAmount = 0;
        String tmp;
        for (String line : itemInHand.getItemMeta().getLore()) {
            if (line.contains("§b§lBEACONS§7: ")) {
                tmp = line.replace("§b§lBEACONS§7: ", "");
                tmp = ChatColor.stripColor(tmp);
                currentAmount = Long.parseLong(tmp);
            }
        }

        ItemMeta meta;
        List<String> Lore = new ArrayList<>();
        ItemStack beacon = XMaterial.BEACON.parseItem();
        assert beacon != null;
        meta = beacon.getItemMeta();
        meta.setDisplayName("§6§lBEACON §7| Amount");
        Lore.add("");
        Lore.add("§e§lTOTAL BEACON§7: " + currentAmount);
        Lore.add("");
        Lore.add("§7Left click to withdraw x64");
        Lore.add("§7Right click to withdraw max amount");
        Lore.add("");
        meta.setLore(Lore);
        beacon.setItemMeta(meta);

        ItemStack collector = new ItemStack(Material.STICK);
        meta = getBeaconCollectorMeta();
        Lore = meta.getLore();
        Lore.add("");
        Lore.add("§7Left click to obtain");
        meta.setLore(Lore);
        collector.setItemMeta(meta);

        gui.setItem(10, collector);
        gui.setItem(13, beacon);

        player.openInventory(gui);
    }

}
