package it.masterzen.blockbreak;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;

public class BlockBreakData {

    private final Material blockType;
    private final byte blockData;
    private final String worldName;
    private final int blockX, blockY, blockZ;
    private final Location location;

    private final Block block;

    public BlockBreakData(Material blockType, byte blockData, String worldName, int blockX, int blockY, int blockZ, Block block) {
        this.blockType = blockType;
        this.blockData = blockData;
        this.worldName = worldName;
        this.blockX = blockX;
        this.blockY = blockY;
        this.blockZ = blockZ;
        this.block = block;

        this.location = new Location(Bukkit.getWorld(worldName), blockX, blockY, blockZ);
    }

    public Material getBlockType() { return blockType; }
    public byte getBlockData() { return blockData; }
    public String getWorldName() { return worldName; }
    public int getBlockX() { return blockX; }
    public int getBlockY() { return blockY; }
    public int getBlockZ() { return blockZ; }
    public Location getLocation() { return location; }
    public Block getBlock() { return block; }
}
