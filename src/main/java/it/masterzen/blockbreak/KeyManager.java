package it.masterzen.blockbreak;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;

import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;

public class KeyManager {

    private final String prefix = "§e§lKEYS §8»§7 ";

    public void openMineKey(Player player, int number) {

        long legendaryKeys = 0;
        long rareKeys = 0;
        long mineKeys = 0;
        long tier1Crystals = 0;
        double tokens = 0;
        double money = 0;

        int chance;

        if (number > 10000000) {
            player.sendMessage(prefix + "Please wait...");
        }

        while (number > 0) {
            chance = ThreadLocalRandom.current().nextInt(100);
            if (chance >= 60) {
                money = money + 5000000000D;
            } else if (chance >= 40) {
                money = money + 25000000000D;
            } else if (chance >= 20) {
                tokens = tokens + 50000;
            } else if (chance >= 10) {
                tokens = tokens + 100000;
            } else if (chance >= 7) {
                mineKeys++;
            } else if (chance >= 5) {
                rareKeys++;
            } else if (chance >= 2) {
                tier1Crystals++;
            } else if (chance >= 1) {
                legendaryKeys++;
            }
            number--;
        }

        AlphaBlockBreak main = AlphaBlockBreak.GetInstance();
        if (money > 0) {
            main.getEconomy().depositPlayer(player, money);
            if (!player.hasPermission("blockbroken.remove")) {
                player.sendMessage(prefix + "You received a total of §a§l" + main.newFormatNumber(money, player) + "§7 Money");
            }
        }
        if (tokens > 0) {
            main.getTeAPI().addTokens(player, tokens);
            if (!player.hasPermission("blockbroken.remove")) {
                player.sendMessage(prefix + "You received a total of §a§l" + main.newFormatNumber(tokens, player) + "§7 Tokens");
            }
        }
        if (mineKeys > 0) {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "adminkey give " + player.getName() + " Mine " + mineKeys);
        }
        if (rareKeys > 0) {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "adminkey give " + player.getName() + " Rare " + rareKeys);
        }
        if (legendaryKeys > 0) {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "adminkey give " + player.getName() + " Legendary " + legendaryKeys);
        }
        if (tier1Crystals > 0) {
            long totalCrystals = 0;
            for (int i = 0; i < tier1Crystals; i++) {
                chance = ThreadLocalRandom.current().nextInt(10) + 1;
                totalCrystals = totalCrystals + chance;
            }
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "randomt1 Mine " + player.getName() + " " + tier1Crystals);
        }
    }

    public void openRareKey(Player player, int number) {

        long legendaryKeys = 0;
        long rareKeys = 0;
        long tagsKeys = 0;
        long tier2Crystals = 0;
        double tokens = 0;
        double money = 0;

        int chance;

        if (number > 10000000) {
            player.sendMessage(prefix + "Please wait...");
        }

        while (number > 0) {
            chance = ThreadLocalRandom.current().nextInt(100);
            if (chance >= 60) {
                money = money + 50000000000D;
            } else if (chance >= 40) {
                money = money + 250000000000D;
            } else if (chance >= 20) {
                tokens = tokens + 500000;
            } else if (chance >= 10) {
                chance = ThreadLocalRandom.current().nextInt(3);
                if (chance == 0) {
                    tokens = tokens + 1000000;
                } else if (chance == 1) {
                    tagsKeys++;
                } else {
                    rareKeys++;
                }
            } else if (chance >= 3) {
                legendaryKeys++;
            } else if (chance >= 2) {
                tier2Crystals++;
            }
            number--;
        }

        AlphaBlockBreak main = AlphaBlockBreak.GetInstance();
        if (money > 0) {
            main.getEconomy().depositPlayer(player, money);
            if (!player.hasPermission("blockbroken.remove")) {
                player.sendMessage(prefix + "You received a total of §a§l" + main.newFormatNumber(money, player) + "§7 Money");
            }
        }
        if (tokens > 0) {
            main.getTeAPI().addTokens(player, tokens);
            if (!player.hasPermission("blockbroken.remove")) {
                player.sendMessage(prefix + "You received a total of §a§l" + main.newFormatNumber(tokens, player) + "§7 Tokens");
            }
        }
        if (tagsKeys > 0) {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "adminkey give " + player.getName() + " Tags " + tagsKeys);
        }
        if (rareKeys > 0) {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "adminkey give " + player.getName() + " Rare " + rareKeys);
        }
        if (legendaryKeys > 0) {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "adminkey give " + player.getName() + " Legendary " + legendaryKeys);
        }
        if (tier2Crystals > 0) {
            long totalCrystals = 0;
            for (int i = 0; i < tier2Crystals; i++) {
                chance = ThreadLocalRandom.current().nextInt(10) + 1;
                totalCrystals = totalCrystals + chance;
            }
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "randomt2 Rare " + player.getName() + " " + tier2Crystals);
        }
    }

    public void openLegendaryKey(Player player, int number) {

        long tagsKeys = 0;
        long tier1Crystals = 0;
        long tier2Crystals = 0;
        double tokens = 0;
        double money = 0;
        long transmog = 0;
        long whitescroll = 0;
        long blackscroll = 0;
        long robot = 0;
        boolean greenChat = false;
        boolean yellowChat = false;
        boolean goldChat = false;
        boolean redChat = false;

        int chance;

        if (number > 10000000) {
            player.sendMessage(prefix + "Please wait...");
        }

        while (number > 0) {
            chance = ThreadLocalRandom.current().nextInt(100);
            if (chance >= 50) {
                money = money + 1000000000000D;
            } else if (chance >= 30) {
                money = money + 5000000000000D;
            } else if (chance >= 20) {
                tokens = tokens + 1000000;
            } else if (chance >= 15) {
                tagsKeys++;
            } else if (chance >= 10) {
                tokens = tokens + 5000000;
            } else if (chance >= 5) {
                chance = ThreadLocalRandom.current().nextInt(3);
                if (chance == 0) {
                    transmog++;
                } else if (chance == 1) {
                    whitescroll++;
                } else {
                    blackscroll++;
                }
            } else if (chance >= 2) {
                chance = ThreadLocalRandom.current().nextInt(2);
                if (chance == 1) {
                    robot++;
                } else {
                    tier1Crystals++;
                }
            } else if (chance >= 1) {
                chance = ThreadLocalRandom.current().nextInt(6);
                if (chance == 1) {
                    tier2Crystals++;
                } else if (chance == 2) {
                    greenChat = true;
                } else if (chance == 3) {
                    yellowChat = true;
                } else if (chance == 4) {
                    goldChat = true;
                } else if (chance == 5) {
                    redChat = true;
                }
            }
            number--;
        }

        AlphaBlockBreak main = AlphaBlockBreak.GetInstance();
        if (money > 0) {
            main.getEconomy().depositPlayer(player, money);
            if (!player.hasPermission("blockbroken.remove")) {
                player.sendMessage(prefix + "You received a total of §a§l" + main.newFormatNumber(money, player) + "§7 Money");
            }
        }
        if (tokens > 0) {
            main.getTeAPI().addTokens(player, tokens);
            if (!player.hasPermission("blockbroken.remove")) {
                player.sendMessage(prefix + "You received a total of §a§l" + main.newFormatNumber(tokens, player) + "§7 Tokens");
            }
        }
        if (tagsKeys > 0) {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "adminkey give " + player.getName() + " Tags " + tagsKeys);
        }
        if (blackscroll > 0) {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "adminkey give " + player.getName() + " BlackScroll " + blackscroll);
        }
        if (whitescroll > 0) {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "adminkey give " + player.getName() + " WhiteScroll " + whitescroll);
        }
        if (transmog > 0) {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "adminkey give " + player.getName() + " Transmog " + transmog);
        }
        if (tier1Crystals > 0) {
            long totalCrystals = 0;
            for (int i = 0; i < tier1Crystals; i++) {
                chance = ThreadLocalRandom.current().nextInt(15) + 10;
                totalCrystals = totalCrystals + chance;
            }
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "randomt1 Legendary " + player.getName() + " " + tier1Crystals);
        }
        if (tier2Crystals > 0) {
            long totalCrystals = 0;
            for (int i = 0; i < tier2Crystals; i++) {
                chance = ThreadLocalRandom.current().nextInt(5) + 5;
                totalCrystals = totalCrystals + chance;
            }
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "randomt2 Legendary " + player.getName() + " " + tier2Crystals);
        }
        if (robot > 0) {
            //main.getRobotSystem().addPoints(player, robot); DA SISTEMARE
            //Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "robotadmin give " + player.getName() + " " + robot);
        }
        if (greenChat) {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "lp user " + player.getName() + " permission set ChatColor.green true");
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "lp user " + player.getName() + " permission set ChatColor.use true");
        }
        if (yellowChat) {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "lp user " + player.getName() + " permission set ChatColor.yellow true");
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "lp user " + player.getName() + " permission set ChatColor.use true");
        }
        if (goldChat) {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "lp user " + player.getName() + " permission set ChatColor.gold true");
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "lp user " + player.getName() + " permission set ChatColor.use true");
        }
        if (redChat) {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "lp user " + player.getName() + " permission set ChatColor.red true");
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "lp user " + player.getName() + " permission set ChatColor.use true");
        }
    }

    public void openAlphaKey(Player player, int number) {

        double tokens = 0;
        double money = 0;
        long vipVoucher = 0;
        long multiplier = 0;
        long robot = 0;
        long silverfish = 0;
        long supremeTime = 0;
        long tier1Crystals = 0;
        long tier2Crystals = 0;
        long tier3Crystals = 0;
        long prestigePoints = 0;

        int chance;

        if (number > 10000000) {
            player.sendMessage(prefix + "Please wait...");
        }

        while (number > 0) {
            chance = ThreadLocalRandom.current().nextInt(100);
            if (chance >= 50) {
                chance = ThreadLocalRandom.current().nextInt(2);
                if (chance == 0) {
                    money = money + 2500000000000000D;
                } else {
                    tokens = tokens + 1000000000D;
                }
            } else if (chance >= 25) {
                chance = ThreadLocalRandom.current().nextInt(5);
                if (chance == 1) {
                    multiplier = multiplier + 100;
                } else if (chance == 2) {
                    supremeTime++;
                } else if (chance == 3) {
                    tier1Crystals++;
                } else if (chance == 4) {
                    prestigePoints++;
                }
            } else if (chance >= 15) {
                chance = ThreadLocalRandom.current().nextInt(2);
                if (chance == 0) {
                    robot = robot + 5;
                } else {
                    silverfish++;
                }
            } else if (chance >= 10) {
                tier2Crystals++;
            } else if (chance >= 5) {
                tier3Crystals++;
            } else if (chance >= 1) {
                vipVoucher++;
            }
            number--;
        }

        AlphaBlockBreak main = AlphaBlockBreak.GetInstance();
        if (money > 0) {
            main.getEconomy().depositPlayer(player, money);
            if (!player.hasPermission("blockbroken.remove")) {
                player.sendMessage(prefix + "You received a total of §a§l" + main.newFormatNumber(money, player) + "§7 Money");
            }
        }
        if (tokens > 0) {
            main.getTeAPI().addTokens(player, tokens);
            if (!player.hasPermission("blockbroken.remove")) {
                player.sendMessage(prefix + "You received a total of §a§l" + main.newFormatNumber(tokens, player) + "§7 Tokens");
            }
        }
        if (multiplier > 0) {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "sudo " + player.getName() + " addmultiplier " + multiplier);
        }
        if (supremeTime > 0) {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "voucher give " + player.getName() + " SupremeMine30 " + supremeTime);
        }
        if (tier1Crystals > 0) {
            long totalCrystals = 0;
            for (int i = 0; i < tier1Crystals; i++) {
                chance = ThreadLocalRandom.current().nextInt(150) + 100;
                totalCrystals = totalCrystals + chance;
            }
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "randomt1 Alpha " + player.getName() + " " + tier1Crystals);
        }
        if (tier2Crystals > 0) {
            long totalCrystals = 0;
            for (int i = 0; i < tier2Crystals; i++) {
                chance = ThreadLocalRandom.current().nextInt(25) + 25;
                totalCrystals = totalCrystals + chance;
            }
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "randomt2 Alpha " + player.getName() + " " + tier2Crystals);
        }
        if (tier3Crystals > 0) {
            long totalCrystals = 0;
            for (int i = 0; i < tier3Crystals; i++) {
                chance = ThreadLocalRandom.current().nextInt(15) + 10;
                totalCrystals = totalCrystals + chance;
            }
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "randomt3 Alpha " + player.getName() + " " + tier3Crystals);
        }
        if (robot > 0) {
            //main.getRobotSystem().addPoints(player, robot); DA SISTEMARE
            //Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "robotadmin give " + player.getName() + " " + robot);
        }
        if (prestigePoints > 0) {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "randompoints " + player.getName() + " " + prestigePoints);
        }
        if (silverfish > 0) {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "stacker give " + player.getName() + " spawner silverfish " + silverfish);
        }
        if (vipVoucher > 0) {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "voucher give " + player.getName() + " Vip " + vipVoucher);
        }
    }
}
