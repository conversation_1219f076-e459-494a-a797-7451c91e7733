package it.masterzen.blockbreak;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mongodb.MongoClient;
import com.mongodb.client.MongoDatabase;
import it.masterzen.MongoDB.PlayerData;
import org.apache.commons.lang3.time.DateUtils;
import org.bukkit.entity.Player;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class Classes {

    private final String prefix = "§e§lCLASSES §8»§7 ";
    public final AlphaBlockBreak mainClass;

    public Classes(AlphaBlockBreak plugin) {
        mainClass = plugin;
    }

    public void setClass(Player player, Enums.Classes classChoosed) {
        Date lastClaimDate = mainClass.getMongoReader().getPlayerData(player.getUniqueId()).getClassClaimDate();
        boolean allowed = false;
        if (lastClaimDate == null) {
            allowed = true;
        } else {
            if (new Date().after(DateUtils.addDays(lastClaimDate, 7))) {
                allowed = true;
            } else {
                player.sendMessage(prefix + "You can change your class every 7 days. Command in cooldown");
                SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
                player.sendMessage("§7Claim Date: " + dateFormat.format(lastClaimDate));
            }
        }

        if (allowed) {
            mainClass.getMongoReader().getPlayerData(player.getUniqueId()).setClassCode(classChoosed.toString());
            mainClass.getMongoReader().getPlayerData(player.getUniqueId()).setClassClaimDate(new Date());

            if (classChoosed == Enums.Classes.MERCHANT) {
                player.sendMessage(prefix + "Merchant class has been chosen. You received a 1.5x Money Multiplier");
            } else if (classChoosed == Enums.Classes.MINER) {
                player.sendMessage(prefix + "Miner class has been chosen. You received a 1.5x Token Multiplier");
            } else if (classChoosed == Enums.Classes.TREASURER) {
                player.sendMessage(prefix + "Treasurer class has been chosen. You will now receive Double Keys");
            } else if (classChoosed == Enums.Classes.EXPERIENCER) {
                player.sendMessage(prefix + "Experiencer class has been chosen. You will now receive Double Experience");
            }
        }
    }

    public void sendReminder(Player player) {
        player.sendMessage(prefix + "You still have not chose your class !");
        player.sendMessage("§7Available classes:");
        player.sendMessage("    §fMERCHANT: §7You will have a 1.5x Money Multiplier");
        player.sendMessage("    §fMINER: §7You will have a 1.5x Token Multiplier");
        player.sendMessage("    §fTREASURER: §7You will receive Double Keys");
        player.sendMessage("    §fEXPERIENCER: §7You will receive Double Experience");
        player.sendMessage("§7Claim now your class with §f§o/class [name]");
    }
}
