package it.masterzen.blockbreak;

import com.sk89q.worldedit.MaxChangedBlocksException;
import com.songoda.skyblock.api.SkyBlockAPI;
import com.songoda.skyblock.api.island.Island;
import com.songoda.skyblock.api.island.IslandManager;
import it.masterzen.MongoDB.DataTypes.QuestPojo;
import it.masterzen.MongoDB.IslandData;
import it.masterzen.MongoDB.PlayerData;
import it.masterzen.commands.Main;
import org.apache.commons.lang.StringUtils;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;

public class IslandSpawners implements Listener {

    private final String prefix = "§e§lSPAWNERS §8»§7 ";
    public final AlphaBlockBreak mainClass;

    public IslandSpawners(AlphaBlockBreak plugin) {
        mainClass = plugin;
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) throws IOException, MaxChangedBlocksException {
        if (event.getClickedInventory() != null && event.getClickedInventory().equals(event.getView().getTopInventory())) {
            Player player = (Player) event.getWhoClicked();

            if (event.getView().getTitle().equalsIgnoreCase("§e§lISLAND §f| §7Spawners")) {
                event.setCancelled(true);

                if (event.getSlot() == 29 || event.getSlot() == 30 || event.getSlot() == 31 || event.getSlot() == 32 || event.getSlot() == 33) {
                    Island island = SkyBlockAPI.getIslandManager().getIsland(player);
                    if (island != null) {
                        if (mainClass.getEmptySlots(player.getInventory()) >= 1) {
                            IslandData data = mainClass.getMongoReader().getIslandData(island.getIslandUUID());
                            if (event.getSlot() == 29) {
                                if ((data.getSpawnersMoney() == null ? 0 : data.getSpawnersMoney()) > 0) {
                                    giveMoneyRewards(player, data.getSpawnersMoney());
                                    data.setSpawnersMoney(0D);
                                } else {
                                    player.sendMessage(prefix + "§cThere's no money to withdraw");
                                }
                            } else if (event.getSlot() == 30) {
                                if ((data.getSpawnersTokens() == null ? 0 : data.getSpawnersTokens()) > 0) {
                                    giveTokenRewards(player, data.getSpawnersTokens());
                                    data.setSpawnersTokens(0D);
                                } else {
                                    player.sendMessage(prefix + "§cThere's no tokens to withdraw");
                                }
                            } else if (event.getSlot() == 31) {
                                if ((data.getSpawnersMobcoins() == null ? 0 : data.getSpawnersMobcoins()) > 0) {
                                    giveMobcoinsRewards(player, data.getSpawnersMobcoins());
                                    data.setSpawnersMobcoins(0);
                                } else {
                                    player.sendMessage(prefix + "§cThere's no tokens to withdraw");
                                }
                            } else if (event.getSlot() == 32) {
                                if ((data.getSpawnersRobot() == null ? 0 : data.getSpawnersRobot()) > 0) {
                                    giveRobotRewards(player, data.getSpawnersRobot());
                                    data.setSpawnersRobot(0);
                                } else {
                                    player.sendMessage(prefix + "§cThere's no tokens to withdraw");
                                }
                            } else if (event.getSlot() == 33) {
                                if ((data.getSpawnersPrestiges() == null ? 0 : data.getSpawnersPrestiges()) > 0) {
                                    givePrestigesRewards(player, data.getSpawnersPrestiges());
                                    data.setSpawnersPrestiges(0);
                                } else {
                                    player.sendMessage(prefix + "§cThere's no tokens to withdraw");
                                }
                            }

                            openMenu(player);
                        } else {
                            player.sendMessage(prefix + "§cInventory full");
                        }
                    }
                } else if (event.getSlot() == 40) {
                    // claim all
                    if (mainClass.getEmptySlots(player.getInventory()) >= 5) {
                        Island island = SkyBlockAPI.getIslandManager().getIsland(player);
                        if (island != null) {
                            IslandData data = mainClass.getMongoReader().getIslandData(island.getIslandUUID());
                            if ((data.getSpawnersMoney() == null ? 0 : data.getSpawnersMoney()) > 0) {
                                giveMoneyRewards(player, data.getSpawnersMoney());
                                data.setSpawnersMoney(0D);
                            }
                            if ((data.getSpawnersTokens() == null ? 0 : data.getSpawnersTokens()) > 0) {
                                giveTokenRewards(player, data.getSpawnersTokens());
                                data.setSpawnersTokens(0D);
                            }
                            if ((data.getSpawnersMobcoins() == null ? 0 : data.getSpawnersMobcoins()) > 0) {
                                giveMobcoinsRewards(player, data.getSpawnersMobcoins());
                                data.setSpawnersMobcoins(0);
                            }
                            if ((data.getSpawnersRobot() == null ? 0 : data.getSpawnersRobot()) > 0) {
                                giveRobotRewards(player, data.getSpawnersRobot());
                                data.setSpawnersRobot(0);
                            }
                            if ((data.getSpawnersPrestiges() == null ? 0 : data.getSpawnersPrestiges()) > 0) {
                                givePrestigesRewards(player, data.getSpawnersPrestiges());
                                data.setSpawnersPrestiges(0);
                            }

                            openMenu(player);
                        }
                    } else {
                        player.sendMessage(prefix + "§cThere's not enough space in your inventory to claim all the rewards");
                    }
                }

                openMenu(player);
            } else if (event.getView().getTitle().equalsIgnoreCase("§e§lQUESTS §f| §7Menu")) {
                event.setCancelled(true);
            }
        }
    }

    public void generateRewards() {
        List<UUID> islandChecked = new ArrayList<>();
        IslandManager islandManager = SkyBlockAPI.getIslandManager();
        for (Player player : Bukkit.getOnlinePlayers()) {
            if (player.getWorld().getName().equals("island_normal_world")) {
                Island island = islandManager.getIsland(player);
                if (island != null && island.isLoaded() && !islandChecked.contains(island.getIslandUUID())) {
                    islandChecked.add(island.getIslandUUID());

                    IslandData data = mainClass.getMongoReader().getIslandData(island.getIslandUUID());
                    int moneySpawners = (data.getMoneySpawners() == null ? 0 : data.getMoneySpawners());
                    int tokenSpawners = (data.getTokenSpawners() == null ? 0 : data.getTokenSpawners());
                    int silverfishSpawners = (data.getSilverfishSpawners() == null ? 0 : data.getSilverfishSpawners());
                    int mobcoinsSpawners = (data.getMobcoinsSpawners() == null ? 0 : data.getMobcoinsSpawners());
                    // NB: MINECRAFT SOLITAMENTE SPAWNA SEMPRE 4 MOBS, ECCO PERCHE' HO MESSO DA 1 A 8 E NON DA 1 A 4
                    if (moneySpawners > 0) {
                        int mobSpawned = ThreadLocalRandom.current().nextInt(8) + 1; // 1 to 8
                        mobSpawned = mobSpawned * moneySpawners;

                        double totalReward = 500000000000D * mobSpawned;
                        data.addSpawnersMoney(totalReward);
                    }
                    if (tokenSpawners > 0) {
                        int mobSpawned = ThreadLocalRandom.current().nextInt(8) + 1; // 1 to 8
                        mobSpawned = mobSpawned * tokenSpawners;

                        double totalReward = 250000D * mobSpawned;
                        data.addSpawnersTokens(totalReward);
                    }
                    if (silverfishSpawners > 0) {
                        int mobSpawned = ThreadLocalRandom.current().nextInt(8) + 1; // 1 to 8
                        mobSpawned = mobSpawned * silverfishSpawners;

                        int chance = ThreadLocalRandom.current().nextInt(2);
                        double totalReward = 0;
                        if (chance == 0) {
                            // TOKENS
                            totalReward = 750000D * mobSpawned;
                            data.addSpawnersTokens(totalReward);
                        } else {
                            // MONEY
                            totalReward = 100000000000D * mobSpawned;
                            data.addSpawnersMoney(totalReward);
                        }

                        // ROBOTS
                        chance = ThreadLocalRandom.current().nextInt(100);
                        if (chance < 1) {
                            int robotToAdd = 1;
                            if (mobSpawned > 100) {
                                robotToAdd = Math.round(mobSpawned / 100);
                            }

                            data.addSpawnersRobot(robotToAdd);
                        }

                        // PRESTIGE
                        chance = ThreadLocalRandom.current().nextInt(100);
                        if (chance < 3) {
                            data.addSpawnersPrestiges(mobSpawned);
                        }
                    }
                    if (mobcoinsSpawners > 0) {
                        int mobSpawned = ThreadLocalRandom.current().nextInt(8) + 1; // 1 to 8
                        mobSpawned = mobSpawned * mobcoinsSpawners;

                        data.addSpawnersMobcoins(mobSpawned);
                    }
                }
            }
        }
    }

    public void addSpawners(Player player) {
        Island island = SkyBlockAPI.getIslandManager().getIsland(player);
        if (island != null) {
            ItemStack itemInHand = player.getInventory().getItemInMainHand();
            if (itemInHand != null && itemInHand.getType().equals(Material.MOB_SPAWNER)) {
                if (itemInHand.hasItemMeta() && itemInHand.getItemMeta().hasDisplayName()) {
                    String itemName = itemInHand.getItemMeta().getDisplayName();

                    if (StringUtils.contains(itemName, "x")) {
                        String amountInString = StringUtils.substringBefore(itemName, " "); // ex.: x1 or x128
                        amountInString = ChatColor.stripColor(amountInString);
                        int amount = Integer.parseInt(StringUtils.replace(amountInString, "x", ""));
                        amount = amount * itemInHand.getAmount();
                        String spawnerType = StringUtils.substringAfterLast(itemName, " "); // ex.: Money or Silverfish or Mobcoin or Token

                        IslandData data = mainClass.getMongoReader().getIslandData(island.getIslandUUID());
                        if (StringUtils.equalsIgnoreCase(spawnerType, "Money") || StringUtils.equalsIgnoreCase(spawnerType, "Bat")) {
                            data.setMoneySpawners((data.getMoneySpawners() == null ? 0 : data.getMoneySpawners()) + amount);
                        } else if (StringUtils.equalsIgnoreCase(spawnerType, "Token") || StringUtils.equalsIgnoreCase(spawnerType, "Parrot")) {
                            data.setTokenSpawners((data.getTokenSpawners() == null ? 0 : data.getTokenSpawners()) + amount);
                        } else if (StringUtils.equalsIgnoreCase(spawnerType, "Silverfish")) {
                            data.setSilverfishSpawners((data.getSilverfishSpawners() == null ? 0 : data.getSilverfishSpawners()) + amount);
                        } else if (StringUtils.equalsIgnoreCase(spawnerType, "Mobcoin") || StringUtils.equalsIgnoreCase(spawnerType, "Villager")) {
                            data.setMobcoinsSpawners((data.getMobcoinsSpawners() == null ? 0 : data.getMobcoinsSpawners()) + amount);
                        }

                        player.getInventory().setItemInMainHand(null); // rimuovo spawner da inventario
                        player.sendMessage(prefix + "§f" + amount + "x " + spawnerType + "§7 Spawners has been added to your /island spawners");
                    } else {
                        player.sendMessage(prefix + "§cThis item seems to not be a Spawner, if you think that's an error please report on /discord (3)");
                    }
                } else {
                    player.sendMessage(prefix + "§cThis item seems to not be a Spawner, if you think that's an error please report on /discord (2)");
                }
            } else {
                player.sendMessage(prefix + "§cThis item seems to not be a Spawner, if you think that's an error please report on /discord (1)");
            }
        } else {
            player.sendMessage(prefix + "§cUnexpected error, please report this on /discord");
        }
    }

    public void withdrawSpawners(Player player, String spawnerType, Integer amount) {
        if (amount > 0) {
            Island island = SkyBlockAPI.getIslandManager().getIsland(player);
            if (island != null) {
                IslandData data = mainClass.getMongoReader().getIslandData(island.getIslandUUID());
                if (StringUtils.equalsIgnoreCase(spawnerType, "Money")) {
                    if ((data.getMoneySpawners() == null ? 0 : data.getMoneySpawners()) >= amount) {
                        data.setMoneySpawners((data.getMoneySpawners() == null ? 0 : data.getMoneySpawners()) - amount);
                        Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "stacker give " + player.getName() + " spawner bat " + amount);
                    }
                } else if (StringUtils.equalsIgnoreCase(spawnerType, "Token")) {
                    if ((data.getTokenSpawners() == null ? 0 : data.getTokenSpawners()) >= amount) {
                        data.setTokenSpawners((data.getTokenSpawners() == null ? 0 : data.getTokenSpawners()) - amount);
                        Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "stacker give " + player.getName() + " spawner Parrot " + amount);
                    }
                } else if (StringUtils.equalsIgnoreCase(spawnerType, "Silverfish")) {
                    if ((data.getSilverfishSpawners() == null ? 0 : data.getSilverfishSpawners()) >= amount) {
                        data.setSilverfishSpawners((data.getSilverfishSpawners() == null ? 0 : data.getSilverfishSpawners()) - amount);
                        Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "stacker give " + player.getName() + " spawner Silverfish " + amount);
                    }
                } else if (StringUtils.equalsIgnoreCase(spawnerType, "Mobcoin")) {
                    if ((data.getMobcoinsSpawners() == null ? 0 : data.getMobcoinsSpawners()) >= amount) {
                        data.setMobcoinsSpawners((data.getMobcoinsSpawners() == null ? 0 : data.getMobcoinsSpawners()) - amount);
                        Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "stacker give " + player.getName() + " spawner Villager " + amount);
                    }
                } else {
                    player.sendMessage(prefix + "§cThis is not a valid spawner type");
                    player.sendMessage("§7Spawner Types: §fMoney, Token, Silverfish, Mobcoin");
                }
            }
        } else {
            player.sendMessage(prefix + "§cPlease insert a valid amount");
        }
    }

    public void giveMoneyRewards(Player player, double amount) {
        ItemStack reward = new ItemStack(Material.PAPER);
        ItemMeta meta = reward.getItemMeta();
        meta.setDisplayName("§a§lMoney Note §7(Right Click)");
        List<String> lore = new ArrayList<>();
        lore.add("§dType: §fMoney");
        lore.add("§dValue: §f" + mainClass.newFormatNumber15Places(amount));
        meta.setLore(lore);
        reward.setItemMeta(meta);

        player.getInventory().addItem(reward);
    }

    public void giveTokenRewards(Player player, double amount) {
        ItemStack reward = new ItemStack(Material.PAPER);
        ItemMeta meta = reward.getItemMeta();
        meta.setDisplayName("§a§lTokens Note §7(Right Click)");
        List<String> lore = new ArrayList<>();
        lore.add("§dType: §fTokens");
        lore.add("§dValue: §f" + mainClass.newFormatNumber15Places(amount));
        meta.setLore(lore);
        reward.setItemMeta(meta);

        player.getInventory().addItem(reward);
    }

    public void giveMobcoinsRewards(Player player, int amount) {
        ItemStack reward = new ItemStack(Material.DOUBLE_PLANT);
        ItemMeta meta = reward.getItemMeta();
        meta.setDisplayName("§7" + amount + " §9§lMOB §5§lCOIN");
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§7Right click to redeem!");
        meta.setLore(lore);
        reward.setItemMeta(meta);

        player.getInventory().addItem(reward);
    }

    public void giveRobotRewards(Player player, int amount) {
        ItemStack reward = new ItemStack(Material.PAPER);
        ItemMeta meta = reward.getItemMeta();
        meta.setDisplayName("§a" + amount + " §7Tier 1 Robots");
        List<String> lore = new ArrayList<>();
        lore.add("§6§l* §eRight click to deposit");
        meta.setLore(lore);
        reward.setItemMeta(meta);

        player.getInventory().addItem(reward);
    }

    public void givePrestigesRewards(Player player, int amount) {
        ItemStack reward = new ItemStack(Material.PAPER);
        ItemMeta meta = reward.getItemMeta();
        meta.setDisplayName("§a" + amount + " §7Prestige");
        List<String> lore = new ArrayList<>();
        lore.add("§6§l* §eRight click to reclaim");
        meta.setLore(lore);
        reward.setItemMeta(meta);

        player.getInventory().addItem(reward);
    }

    public void openMenu(Player player) {
        Island island = SkyBlockAPI.getIslandManager().getIsland(player);
        if (island != null) {
            IslandData data = mainClass.getMongoReader().getIslandData(island.getIslandUUID());

            Inventory gui = Bukkit.createInventory(null, 45, "§e§lISLAND §f| §7Spawners");
            Main.FillBorder(gui);

            ItemStack moneySpawners = new ItemStack(Material.MOB_SPAWNER);
            ItemMeta meta = moneySpawners.getItemMeta();
            meta.setDisplayName("§e§lMONEY §fSpawners");
            List<String> lore = new ArrayList<>();
            lore.add("");
            lore.add("§e§lINFORMATIONS");
            lore.add("§e| §fCurrent amount: §e" + (data.getMoneySpawners() == null ? 0 : data.getMoneySpawners()));
            lore.add("");
            lore.add("§7§lDROPS");
            lore.add("§7| §fMoney");
            lore.add("");
            meta.setLore(lore);
            moneySpawners.setItemMeta(meta);

            ItemStack tokenSpawners = new ItemStack(Material.MOB_SPAWNER);
            meta = tokenSpawners.getItemMeta();
            meta.setDisplayName("§a§lTOKEN §fSpawners");
            lore = new ArrayList<>();
            lore.add("");
            lore.add("§e§lINFORMATIONS");
            lore.add("§e| §fCurrent amount: §e" + (data.getTokenSpawners() == null ? 0 : data.getTokenSpawners()));
            lore.add("");
            lore.add("§7§lDROPS");
            lore.add("§7| §fTokens");
            lore.add("");
            meta.setLore(lore);
            tokenSpawners.setItemMeta(meta);

            ItemStack silverfishSpawners = new ItemStack(Material.MOB_SPAWNER);
            meta = silverfishSpawners.getItemMeta();
            meta.setDisplayName("§7§lSILVERFISH §fSpawners");
            lore = new ArrayList<>();
            lore.add("");
            lore.add("§e§lINFORMATIONS");
            lore.add("§e| §fCurrent amount: §e" + (data.getSilverfishSpawners() == null ? 0 : data.getSilverfishSpawners()));
            lore.add("");
            lore.add("§7§lDROPS");
            lore.add("§7| §fMoney");
            lore.add("§7| §fTokens");
            lore.add("§7| §fTier 1 Robots");
            lore.add("§7| §fPrestiges");
            lore.add("");
            meta.setLore(lore);
            silverfishSpawners.setItemMeta(meta);

            ItemStack mobcoinSpawners = new ItemStack(Material.MOB_SPAWNER);
            meta = mobcoinSpawners.getItemMeta();
            meta.setDisplayName("§9§lMOB §5§lCOIN §fSpawners");
            lore = new ArrayList<>();
            lore.add("");
            lore.add("§e§lINFORMATIONS");
            lore.add("§e| §fCurrent amount: §e" + (data.getMobcoinsSpawners() == null ? 0 : data.getMobcoinsSpawners()));
            lore.add("");
            lore.add("§7§lDROPS");
            lore.add("§7| §fMobcoins");
            lore.add("");
            meta.setLore(lore);
            mobcoinSpawners.setItemMeta(meta);

            ItemStack moneyReward = new ItemStack(Material.GOLD_INGOT);
            meta = moneyReward.getItemMeta();
            meta.setDisplayName("§e§lMONEY");
            lore = new ArrayList<>();
            lore.add("");
            lore.add("§e§lSTORAGE");
            lore.add("§e| §fAmount: §e" + mainClass.newFormatNumber(data.getSpawnersMoney() == null ? 0 : data.getSpawnersMoney()));
            lore.add("");
            lore.add("§6§lUSAGE");
            lore.add("§6| §fClick to withdraw");
            lore.add("");
            meta.setLore(lore);
            moneyReward.setItemMeta(meta);

            ItemStack tokenReward = new ItemStack(Material.MAGMA_CREAM);
            meta = tokenReward.getItemMeta();
            meta.setDisplayName("§a§lTOKENS");
            lore = new ArrayList<>();
            lore.add("");
            lore.add("§e§lSTORAGE");
            lore.add("§e| §fAmount: §e" + mainClass.newFormatNumber(data.getSpawnersTokens() == null ? 0 : data.getSpawnersTokens()));
            lore.add("");
            lore.add("§6§lUSAGE");
            lore.add("§6| §fClick to withdraw");
            lore.add("");
            meta.setLore(lore);
            tokenReward.setItemMeta(meta);

            ItemStack mobcoinsReward = new ItemStack(Material.DOUBLE_PLANT);
            meta = mobcoinsReward.getItemMeta();
            meta.setDisplayName("§9§lMOB §5§lCOINS");
            lore = new ArrayList<>();
            lore.add("");
            lore.add("§e§lSTORAGE");
            lore.add("§e| §fAmount: §e" + mainClass.newFormatNumber(data.getSpawnersMobcoins() == null ? 0 : data.getSpawnersMobcoins()));
            lore.add("");
            lore.add("§6§lUSAGE");
            lore.add("§6| §fClick to withdraw");
            lore.add("");
            meta.setLore(lore);
            mobcoinsReward.setItemMeta(meta);

            ItemStack robotReward = new ItemStack(XMaterial.ZOMBIE_SPAWN_EGG.parseItem());
            meta = robotReward.getItemMeta();
            meta.setDisplayName("§e§lTier 1 §7Robots");
            lore = new ArrayList<>();
            lore.add("");
            lore.add("§e§lSTORAGE");
            lore.add("§e| §fAmount: §e" + mainClass.newFormatNumber(data.getSpawnersRobot() == null ? 0 : data.getSpawnersRobot()));
            lore.add("");
            lore.add("§6§lUSAGE");
            lore.add("§6| §fClick to withdraw");
            lore.add("");
            meta.setLore(lore);
            robotReward.setItemMeta(meta);

            ItemStack prestigeReward = new ItemStack(Material.PAPER);
            meta = prestigeReward.getItemMeta();
            meta.setDisplayName("§7§lPRESTIGES");
            lore = new ArrayList<>();
            lore.add("");
            lore.add("§e§lSTORAGE");
            lore.add("§e| §fAmount: §e" + mainClass.newFormatNumber(data.getSpawnersPrestiges() == null ? 0 : data.getSpawnersPrestiges()));
            lore.add("");
            lore.add("§6§lUSAGE");
            lore.add("§6| §fClick to withdraw");
            lore.add("");
            meta.setLore(lore);
            prestigeReward.setItemMeta(meta);

            ItemStack claimAllRewards = new ItemStack(Material.HOPPER);
            meta = claimAllRewards.getItemMeta();
            meta.setDisplayName("§7§lCLAIM ALL");
            lore = new ArrayList<>();
            lore.add("");
            lore.add("§6§lUSAGE");
            lore.add("§6| §fClick to withdraw");
            lore.add("§6| §fAll the rewards");
            lore.add("");
            meta.setLore(lore);
            claimAllRewards.setItemMeta(meta);

            gui.setItem(11, moneySpawners);
            gui.setItem(12, tokenSpawners);
            gui.setItem(14, silverfishSpawners);
            gui.setItem(15, mobcoinSpawners);

            gui.setItem(29, moneyReward);
            gui.setItem(30, tokenReward);
            gui.setItem(31, mobcoinsReward);
            gui.setItem(32, robotReward);
            gui.setItem(33, prestigeReward);
            gui.setItem(40, claimAllRewards);

            player.openInventory(gui);
        }
    }
}
