package it.masterzen.blockbreak;

import it.masterzen.MongoDB.PlayerData;
import org.bukkit.entity.Player;

import java.util.concurrent.ThreadLocalRandom;

public class Pouches {

    private final String prefix = "§e§lPOUCH §8»§7 ";
    public final AlphaBlockBreak mainClass;

    public Pouches(AlphaBlockBreak plugin) {
        mainClass = plugin;
    }

    public void giveMoneyPouch(Player player) {
        //double amount = ThreadLocalRandom.current().nextDouble(400000000000000000D) + 100000000000000000D;
        PlayerData data = mainClass.getMongoReader().getPlayerData(player.getUniqueId());
        double amount = data.getPickaxeResetAmount() * 750000000000000000D;

        mainClass.getEconomy().depositPlayer(player, amount);
        mainClass.getResume().addValue(player, "Money", amount);

        if (!player.hasPermission("puches.message.remove")) {
            player.sendMessage(prefix + "§7You received §e" + mainClass.newFormatNumber(amount) + " Money §7from a Pouch (based on fortune level)");
        }
    }

    public void giveTokenPouch(Player player) {
        double playerPickValue = mainClass.getPickValue(player);
        double finalValue = playerPickValue * 0.0015;

        mainClass.getTeAPI().addTokens(player, finalValue);
        mainClass.getResume().addValue(player, "Tokens", finalValue);

        if (!player.hasPermission("puches.message.remove")) {
            player.sendMessage(prefix + "§7You received §a" + mainClass.newFormatNumber(finalValue) + " Tokens §7from a Pouch (based on pick value)");
        }
    }
}
