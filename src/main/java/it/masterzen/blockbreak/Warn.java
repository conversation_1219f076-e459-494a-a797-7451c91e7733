package it.masterzen.blockbreak;

import com.earth2me.essentials.Essentials;
import me.clip.ezblocks.EZBlocks;
import org.bukkit.Bukkit;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;

public class Warn {
    public static HashMap<Player, Integer> playerWarnList = new HashMap<>();
    private static YamlConfiguration ymlFile;
    private final String prefix = "§e§lWARN §8»§7 ";
    File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/PlayerWarnList.yml");

    public void loadList() {
        ymlFile = YamlConfiguration.loadConfiguration(file);

        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (Exception ignored) {
            }
        }

        for (Player player : Bukkit.getOnlinePlayers()) {
            if (!playerWarnList.containsKey(player)) {
                playerWarnList.put(player, ymlFile.getInt(player.getName()));
            }
        }
    }

    public boolean isLoaded(Player player) {
        if (playerWarnList.containsKey(player)) {
            return true;
        } else {
            return false;
        }
    }

    public void loadPlayer(Player player, int value) {
        playerWarnList.put(player, value);
    }

    public void warn(Player player) throws IOException {
        if (!playerWarnList.containsKey(player) || playerWarnList.get(player) < 1) {
            playerWarnList.put(player, 1);
            ymlFile.set(player.getName(), playerWarnList.get(player));
            player.sendMessage(prefix + "You got warned, from next time you will get mute. §7§o(" + playerWarnList.get(player) + "° Warn)");
            //Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "mute " + player.getName() + " 15m");
        } else if (playerWarnList.get(player) == 1) {
            playerWarnList.replace(player, 2);
            ymlFile.set(player.getName(), playerWarnList.get(player));
            player.sendMessage(prefix + "You got muted for §a§l1 §7hour. §7§o(" + playerWarnList.get(player) + "° Warn)");
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "mute " + player.getName() + " 1h");
        } else if (playerWarnList.get(player) == 2) {
            playerWarnList.replace(player, 3);
            ymlFile.set(player.getName(), playerWarnList.get(player));
            player.sendMessage(prefix + "You got muted for §a§l4 §7hours. §7§o(" + playerWarnList.get(player) + "° Warn)");
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "mute " + player.getName() + " 4h");
        } else if (playerWarnList.get(player) == 3) {
            playerWarnList.replace(player, 4);
            ymlFile.set(player.getName(), playerWarnList.get(player));
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "tempban " + player.getName() + " 1h Warned " + playerWarnList.get(player) + " times");
        } else if (playerWarnList.get(player) > 4) {
            playerWarnList.replace(player, playerWarnList.get(player) + 1);
            ymlFile.set(player.getName(), playerWarnList.get(player));
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "tempban " + player.getName() + " 8h Warned " + playerWarnList.get(player) + " times");
        }
        ymlFile.save(file);
    }

    public int getWarn(Player player) {
        if (playerWarnList.containsKey(player)) {
            return playerWarnList.get(player);
        } else {
            return 0;
        }
    }
}
