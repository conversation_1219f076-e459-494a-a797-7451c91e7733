package it.masterzen.blockbreak;

import it.masterzen.Keys.TemporaryReward;

import java.util.HashMap;

/**
 * Holds the accumulated results from processing a batch of keys.
 */
public class KeyOpeningBatchResult {

    private double totalTokens = 0;
    private double totalMoney = 0;
    private int totalXP = 0;
    private int totalPrestigePoints = 0;
    private int totalRobots = 0;
    private HashMap<String, String> pexToAdd = new HashMap<>();
    private HashMap<String, TemporaryReward> finalRewards = new HashMap<>();

    /**
     * Adds tokens to the batch result.
     */
    public void addTokens(double tokens) {
        this.totalTokens += tokens;
    }

    /**
     * Adds money to the batch result.
     */
    public void addMoney(double money) {
        this.totalMoney += money;
    }

    /**
     * Adds XP to the batch result.
     */
    public void addXP(int xp) {
        this.totalXP += xp;
    }

    /**
     * Adds prestige points to the batch result.
     */
    public void addPrestigePoints(int points) {
        this.totalPrestigePoints += points;
    }

    /**
     * Adds robots to the batch result.
     */
    public void addRobots(int robots) {
        this.totalRobots += robots;
    }

    /**
     * Adds a permission to the batch result.
     */
    public void addPex(String pex, String message) {
        this.pexToAdd.putIfAbsent(pex, message);
    }

    /**
     * Adds a command reward to the batch result.
     */
    public void addCommand(String command, String message, int amount) {
        if (!finalRewards.containsKey(command)) {
            finalRewards.put(command, new TemporaryReward(command, message, amount));
        } else {
            finalRewards.get(command).addAmount(amount);
        }
    }

    // Getters
    public double getTotalTokens() { return totalTokens; }
    public double getTotalMoney() { return totalMoney; }
    public int getTotalXP() { return totalXP; }
    public int getTotalPrestigePoints() { return totalPrestigePoints; }
    public int getTotalRobots() { return totalRobots; }
    public HashMap<String, String> getPexToAdd() { return pexToAdd; }
    public HashMap<String, TemporaryReward> getFinalRewards() { return finalRewards; }
}