package it.masterzen.blockbreak;

import it.masterzen.Resume.Resume;
import it.masterzen.commands.PointShop;
import net.luckperms.api.LuckPerms;
import net.luckperms.api.cacheddata.CachedMetaData;
import net.luckperms.api.model.user.User;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

public class CustomItem implements Listener {

    private final String pouchPrefix = "§e§lPOUCH §8»§7 ";
    private Resume resume;

    public CustomItem(AlphaBlockBreak mainClass) {
        resume = mainClass.getResume();
    }

    @EventHandler
    public void onPlayerClicks(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        Action action = event.getAction();

        if (action.equals(Action.RIGHT_CLICK_AIR) || action.equals(Action.RIGHT_CLICK_BLOCK)) {
            ItemStack playerHand = player.getInventory().getItemInMainHand();
            if (playerHand.getType().equals(Material.MAGMA_CREAM) && playerHand.hasItemMeta()) {
                if (playerHand.getItemMeta().getDisplayName().contains("TOKEN POUCH")) {
                    String[] splitted = ChatColor.stripColor(playerHand.getItemMeta().getDisplayName()).split(" ");
                    int tier = Integer.parseInt(splitted[splitted.length - 1]);
                    giveDynamicTokenFromItem(player, tier);
                }
            }

            /*if (playerHand.hasItemMeta() && playerHand.getType().equals(Material.MAGMA_CREAM) && playerHand.getItemMeta().getDisplayName().equals("§6§lTOKEN §7| Small Pouch")) {
                giveDynamicTokenFromItem(player, 1);
            } else if (playerHand.hasItemMeta() && playerHand.getType().equals(Material.MAGMA_CREAM) && playerHand.getItemMeta().getDisplayName().equals("§6§lTOKEN §7| Medium Pouch")) {
                giveDynamicTokenFromItem(player, 2);
            } else if (playerHand.hasItemMeta() && playerHand.getType().equals(Material.MAGMA_CREAM) && playerHand.getItemMeta().getDisplayName().equals("§6§lTOKEN §7| Large Pouch")) {
                giveDynamicTokenFromItem(player, 3);
            }*/
        }
    }

    private int getPrestige(Player player) {
        AlphaBlockBreak main = AlphaBlockBreak.GetInstance();
        LuckPerms luckPerms = main.getLuckPerms();
        int prestigeLevel = 0;
        String tmp;

        CachedMetaData metaData = luckPerms.getPlayerAdapter(Player.class).getMetaData(player);
        String prefix = metaData.getPrefix();
        assert prefix != null;
        if (prefix.contains("Lv")) {
            tmp = prefix.replace("§bLv ", "");
            tmp = tmp.replace(" ", "");
            tmp = ChatColor.stripColor(tmp);
            prestigeLevel = Integer.parseInt(tmp);
        }

        return prestigeLevel;
    }

    private int getRebirth(Player player) {
        AlphaBlockBreak main = AlphaBlockBreak.GetInstance();
        LuckPerms luckPerms = main.getLuckPerms();
        int rebirthLevel = 0;
        String tmp;

        CachedMetaData metaData = luckPerms.getPlayerAdapter(Player.class).getMetaData(player);
        String prefix = metaData.getSuffix();
        if (prefix != null && prefix.contains("#")) {
            tmp = prefix.replace("§f#§l", "");
            tmp = tmp.replace(" ", "");
            tmp = ChatColor.stripColor(tmp);
            rebirthLevel = Integer.parseInt(tmp);
        }

        return rebirthLevel;
    }

    private void giveDynamicTokenFromItem(Player player, int tier) {
        AlphaBlockBreak main = AlphaBlockBreak.GetInstance();
        //LocalDate currentdate = LocalDate.now();
        //int prestigeLevel = getPrestige(player);
        //int rebirthLevel = getRebirth(player);
        //double baseValue = 0;
        double finalValue = 0;
        double playerPickValue = main.getPickValue(player);

        // double value = Math.pow(x, (1 + (0.005 * i)));

        /*if (tier == 1) {
            finalValue = 500000000000000D; // 100000000000D
        } else if (tier == 2) {
            finalValue = 1000000000000000D; // 750000000000D
        } else if (tier >= 3) {
            finalValue = 2500000000000000D; // 1500000000000D
        }*/

        /*if (tier == 1) {
            baseValue = 5000000000D;
        } else if (tier == 2) {
            baseValue = 10000000000D;
        } else if (tier == 3) {
            baseValue = 17500000000D;
        }*/

        // OLD MANAGEMENT
        /*int dayOfMonth = currentdate.getDayOfMonth();

        if (currentdate.getDayOfMonth() > 30) {
            dayOfMonth = 1;
        }
        if (prestigeLevel > 2500 || rebirthLevel > 0) {
            if (prestigeLevel > 2500) {
                if (rebirthLevel > 2) {
                    finalValue = baseValue * (dayOfMonth * 12) * (prestigeLevel / 2500) * ((rebirthLevel / 2) + 1);
                } else {
                    finalValue = baseValue * (dayOfMonth * 12) * (prestigeLevel / 2500);
                }
            } else if (rebirthLevel > 2) {
                finalValue = baseValue * (dayOfMonth * 12) * ((rebirthLevel / 2) + 1);
            } else {
                finalValue = baseValue * (dayOfMonth * 12);
            }
        } else {
            finalValue = baseValue * (dayOfMonth * 12);
        }*/

        if (tier == 1) {
            finalValue = playerPickValue * 0.01;
        } else if (tier == 2) {
            finalValue = playerPickValue * 0.025;
        } else if (tier == 3) {
            finalValue = playerPickValue * 0.035;
        } else if (tier == 4) {
            finalValue = playerPickValue * 0.045;
        } else {
            finalValue = playerPickValue * 0.07;
        }

        /*if (finalValue > 5000000000000000D) {
            finalValue = 5000000000000000D;
        }*/

        ItemStack playerHand = player.getInventory().getItemInMainHand();
        playerHand.setAmount(playerHand.getAmount() - 1);

        main.getTeAPI().addTokens(player, finalValue);
        resume.addValue(player, "Tokens", finalValue);
        player.sendMessage(pouchPrefix + "You received a total of §a§l" + main.newFormatNumber(finalValue, player) + " §7Tokens");
    }

    public void giveDynamicTokenItem(Player player, int tier) {
        ItemStack item = new ItemStack(Material.MAGMA_CREAM);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§6§lTOKEN POUCH §7| Tier " + tier);

        /*if (tier == 1) {
            meta.setDisplayName("§6§lTOKEN §7| Small Pouch");
        } else if (tier == 2) {
            meta.setDisplayName("§6§lTOKEN §7| Medium Pouch");
        } else if (tier == 3) {
            meta.setDisplayName("§6§lTOKEN §7| Large Pouch");
        }*/

        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§eThis item will give you");
        if (tier == 1) {
            lore.add("§e1% of your PickValue.");
        } else if (tier == 2) {
            lore.add("§e2.5% of your PickValue.");
        } else if (tier == 3) {
            lore.add("§e3.5% of your PickValue.");
        } else if (tier == 4) {
            lore.add("§e4.5% of your PickValue.");
        } else {
            lore.add("§e7% of your PickValue.");
        }
        lore.add("");
        lore.add("§4§l* §cRight Click to use");
        lore.add("");
        meta.setLore(lore);
        item.setItemMeta(meta);

        player.getInventory().addItem(item);
        player.sendMessage(pouchPrefix + "You recieved a §a§lTier " + tier + " §7Token Pouch");
    }

}
