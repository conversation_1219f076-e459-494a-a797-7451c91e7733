package it.masterzen.blockbreak;

import com.sk89q.worldedit.WorldEdit;
import com.sk89q.worldedit.bukkit.WorldEditPlugin;
import com.sk89q.worldguard.bukkit.WorldGuardPlugin;
import com.sk89q.worldguard.protection.ApplicableRegionSet;
import com.sk89q.worldguard.protection.regions.ProtectedRegion;
import javafx.scene.control.Alert;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.entity.*;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.util.Vector;

import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.ThreadLocalRandom;

public class Arena implements Listener {
    ArrayList<Player> arenaPlayerList = new ArrayList<>();
    ArrayList<Entity> waveMobList = new ArrayList<>();
    //List<EntityType> mobList = Arrays.asList(EntityType.CREEPER, EntityType.ZOMBIE, EntityType.CAVE_SPIDER, EntityType.SPIDER, EntityType.SKELETON, EntityType.WITCH);
    List<Class<? extends Monster>> monsterList = Arrays.asList(Zombie.class, Creeper.class, Spider.class, Skeleton.class, Witch.class);

    private String prefix = "§e§lARENA §8»§7 ";

    private int wave = 1;
    private double waveMoney = 0;
    private double waveTokens = 0;
    private double totalMoney = 0;
    private double totalTokens = 0;

    private WorldGuardPlugin WorldGuard;
    private WorldEdit worldEdit;

    public void addPlayer(Player player) {
        if (!this.arenaPlayerList.contains(player)) {
            this.arenaPlayerList.add(player);
        }
    }

    public ArrayList<Player> getArenaPlayerList() {
        return this.arenaPlayerList;
    }

    public ArrayList<Entity> getWaveMob() {
        return this.waveMobList;
    }

    public void addWave(int number) {
        this.wave = this.wave + number;
    }

    public void startWave() {
        List<Location> zombieLocation = new ArrayList <>();
        Random random = new Random();

        World arenaWorld = Bukkit.getServer().getWorld("Arena");
        Location firstAngle = new Location(arenaWorld, 167, 4, -921);
        Location secondAngle = new Location(arenaWorld, 177, 4, -911);
        zombieLocation.add(firstAngle);
        zombieLocation.add(secondAngle);

        int tmpWave = 10;

        if (wave / 5 > 0) {
            tmpWave = tmpWave + ((wave / 5) * 5);
        }

        for (int i = 0; i < tmpWave; i++) {
            for (Location tmpLocation : zombieLocation) {
                Class<? extends Monster> mob = monsterList.get(ThreadLocalRandom.current().nextInt(monsterList.size()));
                Entity tmp = arenaWorld.spawn(tmpLocation, mob);
                this.waveMobList.add(tmp);
            }
        }

        for (Player tmpPlayer : this.arenaPlayerList) {
            tmpPlayer.sendTitle("§e§lARENA", "§7§oWave §a§l" + this.wave + " §7§o(" + this.waveMobList.size() + ")", 3, 60, 3);
        }

    }

    public boolean isInCuboid(Location l, Location c1, Location c2){
        double maxx = Math.max(c1.getX(), c2.getX());
        double maxy = Math.max(c1.getY(), c2.getY());
        double maxz = Math.max(c1.getZ(), c2.getZ());
        Vector max = new Vector(maxx, maxy, maxz);

        double minx = Math.min(c1.getX(), c2.getX());
        double miny = Math.min(c1.getY(), c2.getY());
        double minz = Math.min(c1.getZ(), c2.getZ());
        Vector min = new Vector(minx, miny, minz);

        return l.toVector().isInAABB(min, max);
    }

    @EventHandler
    public void checkWhoDie(EntityDeathEvent event) {
        Entity dead = event.getEntity();
        EntityType deadType = event.getEntityType();
        Player killer = event.getEntity().getKiller();

        if (dead.getLocation().getWorld().getName().equals("Arena")) {
            event.getDrops().clear();

            if (WorldGuard == null) {
                WorldGuard = WorldGuardPlugin.inst();
            }
            ApplicableRegionSet set = WorldGuard.getRegionManager(dead.getLocation().getWorld()).getApplicableRegions(dead.getLocation());
            if (set.getRegions().size() > 0) {
                ProtectedRegion region = (ProtectedRegion) set.getRegions().toArray()[0];
                List<Entity> entityList = new ArrayList<>();
                entityList = event.getEntity().getNearbyEntities(100, 5, 100);

                int mobleft = 0;
                for (Entity tmpEntity : entityList) {
                    if (!tmpEntity.getType().equals(EntityType.PLAYER)) {
                        // Zombie.class, Creeper.class, Spider.class, Skeleton.class, Witch.class
                        if (tmpEntity.getType().equals(EntityType.ZOMBIE) || tmpEntity.getType().equals(EntityType.CREEPER) || tmpEntity.getType().equals(EntityType.SPIDER) || tmpEntity.getType().equals(EntityType.SKELETON) || tmpEntity.getType().equals(EntityType.WITCH)) {
                            if (!tmpEntity.isDead()) {
                                mobleft++;
                            }
                        }
                    }
                }

                killer.sendMessage(prefix + "Mob left: §a" + mobleft);
                if (mobleft == 0) {
                    this.wave++;
                    startWave();
                }
                //if (worldEdit == null) {
                //    worldEdit = com.sk89q.worldedit.WorldEdit.getInstance();
                //}
            }

            /*AlphaBlockBreak.GetInstance().getLogger().info(this.waveMobList.contains(dead) + "");
            AlphaBlockBreak.GetInstance().getLogger().info(this.waveMobList.contains(deadType) + "");
            AlphaBlockBreak.GetInstance().getLogger().info(this.waveMobList.contains(dead.getType()) + "");
            AlphaBlockBreak.GetInstance().getLogger().info(this.waveMobList.contains(dead instanceof Zombie) + "");
            AlphaBlockBreak.GetInstance().getLogger().info(this.waveMobList.contains(dead instanceof Creeper) + "");
            AlphaBlockBreak.GetInstance().getLogger().info(this.waveMobList.contains(dead instanceof Skeleton) + "");
            AlphaBlockBreak.GetInstance().getLogger().info(this.waveMobList.isEmpty() + "");

            killer.sendMessage(dead.getType() + " " + dead.getName());

            killer.sendMessage("WaveMobList: ");
            for (Entity tmpEntity : this.waveMobList) {
                killer.sendMessage(tmpEntity.getType() + "");
            }
            killer.sendMessage("WaveMobList finised.");

            if (this.waveMobList.contains(dead)) {

                AlphaBlockBreak.GetInstance().getLogger().info("OKE");

                killer.sendMessage("Mob wave killed");

                this.waveMobList.remove(dead);
                if (this.waveMobList.isEmpty()) {
                    this.wave++;
                    startWave();
                }
            }*/

            /*if (dead instanceof Zombie) {
                if (((Zombie) dead).getKiller() instanceof Player) {
                    Player player = ((Zombie) dead).getKiller();
                    player.sendMessage("Zombie Killed");

                    if (zombieWaves.contains(deadType)) {
                        player.sendMessage("Zombie Killed 2.0");
                        zombieWaves.remove(deadType);
                        if (zombieWaves.isEmpty()) {
                            wave++;
                            startWave();
                        }
                    }
                } else {
                    Player player = ((Zombie) dead).getKiller();
                    player.sendMessage("FF");
                }
            }*/
        }
    }

}
