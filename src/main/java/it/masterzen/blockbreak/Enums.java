package it.masterzen.blockbreak;

public class Enums {

    public enum Ranks {
        VIP("voucher give %player% Vip 1"),
        VIP_PL<PERSON>("voucher give %player% Vip+ 1"),
        <PERSON>("voucher give %player% Mvp 1"),
        MVP_PLUS("voucher give %player% Mvp+ 1"),
        ALPHA("voucher give %player% Alpha 1");

        private final String command;

        Ranks(String command) {
            this.command = command;
        }

        public String getCommand() {
            return this.command;
        }
    }

    // IL CODICE C'E' ANCHE SU JACKHAMMER
    public enum Classes {
        MERCHANT(1.5),
        MINER(1.5),
        TREASURER(2D),
        EXPERIENCER(2D);

        private final Double booster;

        Classes(Double booster) {
            this.booster = booster;
        }

        public Double getBooster() {
            return booster;
        }
    }

    public enum Quests {
        BLOCK_MINER,
        DUNGEON_MINER,
        PLAYTIME,
        FARMER_MINER,
        BEACON_MINER
    }
}
