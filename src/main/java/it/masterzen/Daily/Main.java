package it.masterzen.Daily;

import com.comphenix.net.sf.cglib.core.Local;
import it.masterzen.Robot.Robot;
import it.masterzen.blockbreak.AlphaBlockBreak;
import net.luckperms.api.model.user.User;
import net.luckperms.api.node.Node;
import org.apache.commons.lang.time.DurationFormatUtils;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;

import java.io.File;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class Main implements CommandExecutor, Listener {

    private final String prefix = "§e§lDAILY REWARDS §8»§7 ";
    public final AlphaBlockBreak mainClass;
    private static YamlConfiguration ymlFile;

    public Main(AlphaBlockBreak plugin) {
        mainClass = plugin;
    }

    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        if (canReclaim(player)) {
            player.sendMessage(prefix + "You can claim your §a/Daily §7rewards");
        }
    }

    public boolean canReclaim(Player player) {
        return !player.hasPermission("dailyrewards.cooldown");

        /*boolean canBeRewarded = false;

        File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/PlayerDailyRewards.yml");
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (Exception ex) {
            }
        }

        ymlFile = YamlConfiguration.loadConfiguration(file);
        if (ymlFile.contains(player.getUniqueId().toString())) {
            if (Duration.between(LocalDateTime.parse(ymlFile.getString(player.getUniqueId().toString())), LocalDateTime.now()).toDays() >= 1) {
                canBeRewarded = true;
            }
        }

        return canBeRewarded;*/
    }

    /*public LocalDateTime getLastUsage(Player player) {
        LocalDateTime lastUsage = LocalDateTime.now();

        File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/PlayerDailyRewards.yml");
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (Exception ex) {
            }
        }

        ymlFile = YamlConfiguration.loadConfiguration(file);
        if (ymlFile.contains(player.getUniqueId().toString())) {
            lastUsage = LocalDateTime.parse(ymlFile.getString(player.getUniqueId().toString()));
        }

        return lastUsage;
    }*/

    public long getCoolDown(Player player) {
        long milliseconds = 0;

        if (player.hasPermission("dailyrewards.cooldown")) {
            User user = mainClass.getLuckPerms().getPlayerAdapter(Player.class).getUser(player);

            for (Node perm : user.getDistinctNodes()) {
                if (perm.getKey().equalsIgnoreCase("dailyrewards.cooldown")) {
                    milliseconds = Objects.requireNonNull(perm.getExpiryDuration()).toMillis();
                }
            }
        }

        return milliseconds;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command cmd, String label, String[] args) {
        if (cmd.getName().equalsIgnoreCase("daily")) {
            if (sender instanceof Player) {
                Player player = (Player) sender;
                if (canReclaim(player) || player.isOp()) {
                    mainClass.giveDailyRewards(player);
                } else {
                    //player.sendMessage(prefix + "§cYou can't reclaim this now. You need to wait §l" + DurationFormatUtils.formatDurationHMS(Duration.between(getLastUsage(player).plusDays(1), getLastUsage(player)).toMillis()));
                    player.sendMessage(prefix + "§cYou can't reclaim this now");
                    player.sendMessage("§cYou need to wait §l" + DurationFormatUtils.formatDuration(getCoolDown(player), "HH'h' mm'm' ss's'"));
                }
            }
        }
        return false;
    }
}
