package it.masterzen.ServerGoal;

import com.sk89q.worldguard.bukkit.WorldGuardPlugin;
import com.sk89q.worldguard.protection.ApplicableRegionSet;
import com.sk89q.worldguard.protection.flags.DefaultFlag;
import com.sk89q.worldguard.protection.flags.StateFlag;
import com.vk2gpz.tokenenchant.api.TokenEnchantAPI;
import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.prestigemine.Main;
import it.masterzen.prestigemine.MineManager;
import javafx.scene.paint.Color;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.OfflinePlayer;
import org.bukkit.World;
import org.bukkit.boss.BarColor;
import org.bukkit.boss.BarStyle;
import org.bukkit.boss.BossBar;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.craftbukkit.v1_12_R1.boss.CraftBossBar;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.scheduler.BukkitRunnable;

import java.io.IOException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

public class ServerGoal implements CommandExecutor, Listener {

    private final AlphaBlockBreak mainClass;
    private final TokenEnchantAPI teAPI;
    private WorldGuardPlugin WorldGuard = null;

    public int infiniteLoopID = 0;
    private boolean isEventActive = false;
    private boolean isEventFailed = false;
    private LocalDateTime eventStartTime;
    private HashMap <UUID, Long> minedBlocks = new HashMap<>();
    private HashMap <UUID, Long> sortedStats = new HashMap<>();

    private final Main mineSystem = Main.getInstance();

    private final String prefix = "§e§lSERVER GOAL §8»§7 ";
    private int blocksNeeded;
    BossBar bar;

    public void cancelBossBar() {
        if (bar != null) {
            bar.setVisible(false);
            bar.removeAll();
        }
    }

    @EventHandler
    public void blockbreak(BlockBreakEvent event) {
        if (!event.getBlock().getType().equals(Material.AIR)) {
            if (isEventActive) {
                Player player = event.getPlayer();

                if (WorldGuard == null) {
                    WorldGuard = WorldGuardPlugin.inst();
                }
                if (player.getWorld().getName().equalsIgnoreCase("PrestigeMine")) {
                    ApplicableRegionSet set = WorldGuard.getRegionManager(player.getWorld()).getApplicableRegions(event.getBlock().getLocation());

                    if (set.queryState(null, DefaultFlag.BLOCK_BREAK) == StateFlag.State.ALLOW) {
                        if (minedBlocks.containsKey(player.getUniqueId())) {
                            minedBlocks.replace(player.getUniqueId(), minedBlocks.get(player.getUniqueId()), minedBlocks.get(player.getUniqueId()) + 1L);
                        } else {
                            minedBlocks.put(player.getUniqueId(), 1L);
                        }
                        if (getTotalBlocks() < blocksNeeded) {
                            bar.setTitle("§7ServerGoal: §a" + getTotalBlocks() + "§7/§a" + blocksNeeded /*+ "§7§o (" + getCurrentPosition(player) + ")"*/);
                            bar.setProgress((double) getTotalBlocks() / blocksNeeded);
                        }
                        if (getTotalBlocks() > blocksNeeded) {
                            for (Player tmpPlayer : Bukkit.getOnlinePlayers()) {
                                sendLeaderBoard(tmpPlayer, true);
                            }

                            giveRewards();
                            stopEvent();
                        }
                    }
                }
            }
        }
    }

    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        if (isEventActive) {
            player.sendTitle("§e§lSERVER GOAL", "§7§oEvent Active !", 5, 80, 5);
            sendIsEventEnabled(player);
            bar.addPlayer(player);
        }
    }

    public ServerGoal(AlphaBlockBreak plugin) {
        mainClass = plugin;
        teAPI = TokenEnchantAPI.getInstance();
        startScheduler();
    }

    public void checkChance() {
        int chance = ThreadLocalRandom.current().nextInt(100);

        if (chance <= 1 && !isEventActive) {
            isEventActive = true;
            startEvent();
        } else if (isEventActive) {
            if (getMinutesLeft() < 0) {
                stopEvent();
                for (Player player : Bukkit.getOnlinePlayers()) {
                    player.sendTitle("§e§lSERVER GOAL", "§c§oEvent Failed !", 5, 80, 5);
                }
            }
        }
    }

    public void sendGlobalMessage() {
        for (Player player : Bukkit.getOnlinePlayers()) {
            player.sendTitle("§e§lSERVER GOAL", "§7§oEvent Started !", 5, 80, 5);
            player.sendMessage("§e§lSERVER GOAL");
            player.sendMessage("§7Your achieve is to mine as much blocks as you can");
            player.sendMessage("§7You can check your current stats with §a/sg stats§7");
            player.sendMessage("");
            player.sendMessage("§7#1 will receive 5% of total Tokens of online players");
            player.sendMessage("§7#2 will receive 3% of total Tokens of online players");
            player.sendMessage("§7All the others, if they mine at least 7.5k blocks, they will receive 1%");
            player.sendMessage("§7§o(all the rewards are capped at 15% of your pick value)");
            player.sendMessage("");
            player.sendMessage("§7You all got §a§l1 Hour§7 to break §a§l" + mainClass.newFormatNumber(blocksNeeded) + "§7 blocks, Good Luck !");
        }
    }

    public void stopEvent() {
        isEventActive = false;
        minedBlocks.clear();
        bar.setVisible(false);
    }

    public void startEvent() {
        minedBlocks.clear();
        blocksNeeded = (Bukkit.getOnlinePlayers().toArray().length * 8500);
        sendGlobalMessage();
        isEventActive = true;
        eventStartTime = LocalDateTime.now();
        bar = new CraftBossBar("§7ServerGoal: §a0§7/§a" + blocksNeeded, BarColor.PURPLE, BarStyle.SOLID);
        bar.removeAll();
        bar.setVisible(true);
        bar.setProgress(0.0001);
        for (Player player : Bukkit.getOnlinePlayers()) {
            bar.addPlayer(player);
        }
    }

    // function to sort hashmap by values
    public static HashMap<UUID, Long> sortProgress(HashMap<UUID, Long> hm)
    {
        // Create a list from elements of HashMap
        List<Map.Entry<UUID, Long> > list = new LinkedList<Map.Entry<UUID, Long> >(hm.entrySet());

        // Sort the list
        list.sort(new Comparator<Map.Entry<UUID, Long>>() {
            public int compare(Map.Entry<UUID, Long> o1, Map.Entry<UUID, Long> o2) {
                return (o2.getValue()).compareTo(o1.getValue());
            }
        });

        // put data from sorted list to hashmap
        HashMap<UUID, Long> temp = new LinkedHashMap<UUID, Long>();
        for (Map.Entry<UUID, Long> aa : list) {
            temp.put(aa.getKey(), aa.getValue());
        }
        return temp;
    }

    public long getTotalBlocks() {
        long totalBlocks = 0;
        if (isEventActive && !minedBlocks.isEmpty()) {
            for (UUID player : minedBlocks.keySet()) {
                totalBlocks = totalBlocks + minedBlocks.get(player);
            }
        }

        return totalBlocks;
    }

    public double getTotalTokens() {
        double totalTokens = 0;
        for (Player player : Bukkit.getOnlinePlayers()) {
            if (!player.isOp()) {
                totalTokens = totalTokens + teAPI.getTokens(player);
            }
        }

        return totalTokens;
    }

    public void giveRewards() {
        if (!minedBlocks.isEmpty()) {
            double totalTokens = getTotalTokens();

            int tmpPlayerPosition = 0;
            double totalTokensToAdd = 0;
            for (UUID player : minedBlocks.keySet()) {
                if (minedBlocks.containsKey(player)) {
                    if (minedBlocks.get(player) >= 7500) {
                        if (Bukkit.getPlayer(player) != null) {
                            tmpPlayerPosition = getCurrentPosition(Bukkit.getPlayer(player));
                            if (tmpPlayerPosition == 1) {
                                totalTokensToAdd = ((totalTokens / 100) * 5);
                            } else if (tmpPlayerPosition == 2) {
                                totalTokensToAdd = ((totalTokens / 100) * 3);
                            } else {
                                totalTokensToAdd = ((totalTokens / 100) * 1);
                            }

                            boolean isCapped = false;
                            double playerPickValue = mainClass.getPickValue(Bukkit.getPlayer(player));
                            double maxPickValue = playerPickValue * 0.15; // 15%
                            if (totalTokensToAdd > maxPickValue) {
                                totalTokensToAdd = maxPickValue;
                                isCapped = true;
                            }

                            teAPI.addTokens(Bukkit.getPlayer(player), totalTokensToAdd);
                            Bukkit.getPlayer(player).sendMessage(prefix + "You mined a total of §a" + minedBlocks.get(player) + "§7 and you received §a§l" + mainClass.newFormatNumber(totalTokensToAdd) + "§7 Tokens" + (isCapped ? " §7§o(reward is capped at 15% of your pick value)" : ""));
                        }
                    } else {
                        if (Bukkit.getPlayer(player) != null) {
                            Bukkit.getPlayer(player).sendMessage(prefix + "§cYou need to mine at least 7.5k blocks to receive the Event Rewards");
                        }
                    }
                } else {
                    if (Bukkit.getPlayer(player) != null) {
                        Bukkit.getPlayer(player).sendMessage(prefix + "§cYou don't have mined a single block during the Server Goal Event");
                    }
                }
            }
        }
    }

    public void startScheduler() {
        if (infiniteLoopID == 0) {
            infiniteLoopID = Bukkit.getScheduler().scheduleSyncRepeatingTask(mainClass, new Runnable() {
                @Override
                public void run() {
                    checkChance();
                }
            }, 3600L, 3600L);
        }
    }

    public int getCurrentPosition(Player player) {
        if (minedBlocks.containsKey(player.getUniqueId())) {
            sortedStats = sortProgress(minedBlocks);
            int position = 1;

            int counter = 1;
            for (UUID tmpPlayer : sortedStats.keySet()) {
                if (Bukkit.getPlayer(tmpPlayer) != null) {
                    if (Bukkit.getPlayer(tmpPlayer) == player && position == 1) {
                        position = counter;
                    } else {
                        counter++;
                    }
                } else {
                    if (Bukkit.getOfflinePlayer(tmpPlayer) == player && position == 1) {
                        position = counter;
                    } else {
                        counter++;
                    }
                }
            }

            return position;
        } else {
            return 0;
        }
    }

    public void sendTimeLeft(Player player) {
        if (getMinutesLeft() > 0) {
            player.sendMessage(prefix + "Time left: §a§l" + getMinutesLeft() + "§7 minutes");
        } else {
            stopEvent();
            player.sendMessage(prefix + "§7Events Currently §c§lINACTIVE");
        }
    }

    public long getMinutesLeft() {
        if (isEventActive) {
            LocalDateTime now = LocalDateTime.now();
            Duration time = Duration.between(eventStartTime, now);
            return (60 - time.toMinutes());
        } else {
            return 0;
        }
    }

    public void sendCurrentStats(Player player) {
        int playerPosition = getCurrentPosition(player);
        if (isEventActive) {
            if (minedBlocks.containsKey(player.getUniqueId())) {
                player.sendMessage(prefix + "You have mined a total of §a" + minedBlocks.get(player.getUniqueId()) + "§7 raw blocks");
                player.sendMessage(prefix + "Your current position: §a#§l" + playerPosition + "/" + minedBlocks.size());
                player.sendMessage("");
                player.sendMessage(prefix + "Total amount of broken blocks: §a" + getTotalBlocks() + "/" + blocksNeeded);
            } else {
                player.sendMessage(prefix + "§cYou need to mine at least 1 block !");
                player.sendMessage("");
                player.sendMessage(prefix + "Total amount of broken blocks: §a" + getTotalBlocks() + "/" + blocksNeeded);
            }
            sendTimeLeft(player);
        } else {
            sendIsEventEnabled(player);
        }
    }

    public void sendIsEventEnabled(Player player) {
        if (!minedBlocks.isEmpty() || isEventActive) {
            player.sendMessage(prefix + "§7Events Currently §a§lACTIVE");
            player.sendMessage("§7Start mine to try to obtain some Tokens");
            sendTimeLeft(player);
        } else {
            player.sendMessage(prefix + "§7Events Currently §c§lINACTIVE");
        }
    }

    public void sendLeaderBoard(Player player, boolean endEvent) {
        if (isEventActive && !minedBlocks.isEmpty()) {
            sortedStats = sortProgress(minedBlocks);

            player.sendMessage("§e§lSERVER GOAL §f| §7LeaderBoard");
            player.sendMessage("");
            int times = Math.min(sortedStats.size(), 5);
            for (int i = 0; i < times; i++) {
                OfflinePlayer tmpPlayer = Bukkit.getOfflinePlayer((UUID) sortedStats.keySet().toArray()[i]);
                if (endEvent) {
                    if (minedBlocks.get(tmpPlayer.getUniqueId()) > 7500 && Bukkit.getPlayer((UUID) sortedStats.keySet().toArray()[i]) != null) {
                        Player onlinePlayer = Bukkit.getPlayer((UUID) sortedStats.keySet().toArray()[i]);
                        double totalTokens = getTotalTokens();
                        double tokensToAdd;
                        if (getCurrentPosition(onlinePlayer) == 1) {
                            tokensToAdd = ((totalTokens / 100) * 5);
                        } else if (getCurrentPosition(onlinePlayer) == 2) {
                            tokensToAdd = ((totalTokens / 100) * 3);
                        } else {
                            tokensToAdd = ((totalTokens / 100) * 1);
                        }

                        double playerPickValue = mainClass.getPickValue(onlinePlayer);
                        double maxPickValue = playerPickValue * 0.15; // 15%
                        if (tokensToAdd > maxPickValue) {
                            tokensToAdd = maxPickValue;
                        }

                        player.sendMessage("§7#" + (i + 1) + " §f" + tmpPlayer.getName() + " §8»§7 " + minedBlocks.get(tmpPlayer.getUniqueId()) + "§7 won a total of §a§l" + mainClass.newFormatNumber(tokensToAdd) + "§7 Tokens");
                    } else {
                        player.sendMessage("§7#" + (i + 1) + " §f" + tmpPlayer.getName() + " §8»§7 " + minedBlocks.get(tmpPlayer.getUniqueId()) + "§c did not won");
                    }
                } else {
                    player.sendMessage("§7#" + (i + 1) + " §f" + tmpPlayer.getName() + " §8»§7 " + minedBlocks.get(tmpPlayer.getUniqueId()));
                }
            }
        } else {
            sendIsEventEnabled(player);
        }
    }

    @Override
    public boolean onCommand(CommandSender sender, Command cmd, String label, String[] args) {
        if (cmd.getName().equalsIgnoreCase("sg") || cmd.getName().equalsIgnoreCase("ServerGoal")) {
            if (sender instanceof Player) {
                Player player = (Player) sender;
                if (args.length == 1 && args[0].equalsIgnoreCase("stats")) {
                    sendCurrentStats(player);
                } else if (args.length == 1 && (args[0].equalsIgnoreCase("leaderboard") || args[0].equalsIgnoreCase("lb"))) {
                    sendLeaderBoard(player, false);
                } else {
                    sendIsEventEnabled(player);
                }
                if (player.isOp()) {
                    if (args.length == 1 && args[0].equalsIgnoreCase("start")) {
                        if (!isEventActive) {
                            startEvent();
                        } else {
                            player.sendMessage(prefix + "§cEvent is already started");
                        }
                    } else if (args.length == 1 && args[0].equalsIgnoreCase("giveRewards")) {
                        giveRewards();
                    }
                }
            }
        }
        return false;
    }
}
