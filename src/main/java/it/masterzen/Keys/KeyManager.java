package it.masterzen.Keys;

import org.bukkit.entity.Player;

import javax.swing.*;

public class KeyManager {

    private int deltaKeys;
    private int betaKeys;
    private int gammaKeys;
    private int omegaKeys;
    private int alphaKeys;
    private int tokenKeys;
    private int randomKeys;
    private int voteKeys;
    private int armorKeys;

    public KeyManager(int deltaKeys, int betaKeys, int gammaKeys, int omegaKeys, int alphaKeys, int tokenKeys, int randomKeys, int voteKeys, int armorKeys) {
        this.deltaKeys = deltaKeys;
        this.betaKeys = betaKeys;
        this.gammaKeys = gammaKeys;
        this.omegaKeys = omegaKeys;
        this.alphaKeys = alphaKeys;
        this.tokenKeys = tokenKeys;
        this.randomKeys = randomKeys;
        this.voteKeys = voteKeys;
        this.armorKeys = armorKeys;
    }

    public void giveKeys(String type, int amount) {
        if (type.equalsIgnoreCase("Delta")) {
            this.deltaKeys = this.deltaKeys + amount;
        } else if (type.equalsIgnoreCase("Beta")) {
            this.betaKeys = this.betaKeys + amount;
        } else if (type.equalsIgnoreCase("Gamma")) {
            this.gammaKeys = this.gammaKeys + amount;
        } else if (type.equalsIgnoreCase("Omega")) {
            this.omegaKeys = this.omegaKeys + amount;
        } else if (type.equalsIgnoreCase("Alpha")) {
            this.alphaKeys = this.alphaKeys + amount;
        } else if (type.equalsIgnoreCase("Token")) {
            this.tokenKeys = this.tokenKeys + amount;
        } else if (type.equalsIgnoreCase("Random")) {
            this.randomKeys = this.randomKeys + amount;
        } else if (type.equalsIgnoreCase("Vote")) {
            this.voteKeys = this.voteKeys + amount;
        } else if (type.equalsIgnoreCase("Armor")) {
            this.armorKeys = this.armorKeys + amount;
        }
    }

    public void removeKeys(String type, int amount) {
        if (type.equalsIgnoreCase("Delta")) {
            this.deltaKeys = this.deltaKeys - amount;
        } else if (type.equalsIgnoreCase("Beta")) {
            this.betaKeys = this.betaKeys - amount;
        } else if (type.equalsIgnoreCase("Gamma")) {
            this.gammaKeys = this.gammaKeys - amount;
        } else if (type.equalsIgnoreCase("Omega")) {
            this.omegaKeys = this.omegaKeys - amount;
        } else if (type.equalsIgnoreCase("Alpha")) {
            this.alphaKeys = this.alphaKeys - amount;
        } else if (type.equalsIgnoreCase("Token")) {
            this.tokenKeys = this.tokenKeys - amount;
        } else if (type.equalsIgnoreCase("Random")) {
            this.randomKeys = this.randomKeys - amount;
        } else if (type.equalsIgnoreCase("Vote")) {
            this.voteKeys = this.voteKeys - amount;
        } else if (type.equalsIgnoreCase("Armor")) {
            this.armorKeys = this.armorKeys - amount;
        }
    }

    public int getKeys(String type) {
        if (type.equalsIgnoreCase("Delta")) {
            return this.deltaKeys;
        } else if (type.equalsIgnoreCase("Beta")) {
            return this.betaKeys;
        } else if (type.equalsIgnoreCase("Gamma")) {
            return this.gammaKeys;
        } else if (type.equalsIgnoreCase("Omega")) {
            return this.omegaKeys;
        } else if (type.equalsIgnoreCase("Alpha")) {
            return this.alphaKeys;
        } else if (type.equalsIgnoreCase("Token")) {
            return this.tokenKeys;
        } else if (type.equalsIgnoreCase("Random")) {
            return this.randomKeys;
        } else if (type.equalsIgnoreCase("Vote")) {
            return this.voteKeys;
        } else if (type.equalsIgnoreCase("Armor")) {
            return this.armorKeys;
        }

        return 0;
    }

    public int getDeltaKeys() {
        return this.deltaKeys;
    }

    public int getBetaKeys() {
        return this.betaKeys;
    }

    public int getGammaKeys() {
        return this.gammaKeys;
    }

    public int getOmegaKeys() {
        return this.omegaKeys;
    }

    public int getAlphaKeys() {
        return this.alphaKeys;
    }

    public int getTokenKeys() {
        return this.tokenKeys;
    }

    public int getRandomKeys() {
        return this.randomKeys;
    }

    public int getVoteKeys() {
        return this.voteKeys;
    }

    public int getArmorKeys() {
        return this.armorKeys;
    }
}
