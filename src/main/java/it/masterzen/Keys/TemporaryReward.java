package it.masterzen.Keys;

public class TemporaryReward {

    private String commandToExecute;
    private String messageToSend;
    private int amount;

    public TemporaryReward(String commandToExecute, String messageToSend, int amount) {
        this.commandToExecute = commandToExecute;
        this.messageToSend = messageToSend;
        this.amount = amount;
    }

    public String getCommandToExecute() {
        return this.commandToExecute;
    }

    public String getMessageToSend() {
        return this.messageToSend;
    }

    public int getAmount() {
        return this.amount;
    }

    public void addAmount(int amount) {
        this.amount = this.amount + amount;
    }

    public void removeAmount(int amount) {
        this.amount = this.amount - amount;
    }

    public void setAmount(int amount) {
        this.amount = amount;
    }

}
