package it.masterzen.Keys;

import com.sk89q.worldedit.Vector;
import com.sk89q.worldguard.bukkit.RegionContainer;
import com.sk89q.worldguard.protection.ApplicableRegionSet;
import com.sk89q.worldguard.protection.flags.DefaultFlag;
import com.sk89q.worldguard.protection.flags.StateFlag;
import com.vk2gpz.tokenenchant.api.TokenEnchantAPI;
import it.masterzen.MongoDB.PlayerData;
import it.masterzen.Resume.Resume;
import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.blockbreak.Enums;
import it.masterzen.blockbreak.XMaterial;
import it.masterzen.commands.PointShop;
import net.luckperms.api.model.user.User;
import net.luckperms.api.node.Node;
import net.md_5.bungee.api.ChatMessageType;
import net.md_5.bungee.api.chat.TextComponent;
import net.milkbowl.vault.economy.Economy;
import org.apache.commons.lang3.EnumUtils;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.RegisteredServiceProvider;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

public class Main implements CommandExecutor, Listener {

    private final AlphaBlockBreak mainClass;
    private final TokenEnchantAPI teAPI;
    private Economy economy;
    private Resume resume;

    private KeyList keyList;
    private GUI gui;

    public final String prefix = "§e§lKEYS §8»§7 ";

    private static YamlConfiguration ymlFile;
    private HashMap<UUID, KeyManager> playerKeys = new HashMap<>();
    private HashMap<String, String> keyNames = new HashMap<>();

    public Main(AlphaBlockBreak plugin) {
        mainClass = plugin;
        keyList = new KeyList();
        gui = new GUI(this);

        teAPI = TokenEnchantAPI.getInstance();

        RegisteredServiceProvider<Economy> rsp = mainClass.getServer().getServicesManager().getRegistration(Economy.class);
        economy = rsp.getProvider();

        resume = mainClass.getResume();
        setupKeyNames();
    }

    public void setupKeyNames() {
        keyNames.put("Delta", "§b§lDELTA");
        keyNames.put("Beta", "§c§lBETA");
        keyNames.put("Gamma", "§d§lGAMMA");
        keyNames.put("Omega", "§e§lOMEGA");
        keyNames.put("Alpha", "§c§lA§6§lL§e§lP§a§lH§b§lA");
        keyNames.put("Token", "§a§lTOKEN");
        keyNames.put("Random", "§2§lRAN§a§lDOM");
        keyNames.put("Vote", "§f§lVOTE");
        keyNames.put("Armor", "§6§lARMOR");
    }

    public String getKeyName(String type) {
        return this.keyNames.get(type);
    }

    public AlphaBlockBreak getMainClass() {
        return this.mainClass;
    }

    public KeyList getKeyList() {
        return this.keyList;
    }

    public HashMap<UUID, KeyManager> getPlayerKeys() {
        return this.playerKeys;
    }

    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) throws IOException {
        Player player = event.getPlayer();
        loadKeys(player);
    }

    public void saveKeys() throws IOException {
        File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/PlayerKeys.yml");
        ymlFile = YamlConfiguration.loadConfiguration(file);

        if (!file.exists()) {
            file.createNewFile();
        }

        if (playerKeys != null && !playerKeys.isEmpty()) {
            for (UUID player : playerKeys.keySet()) {
                saveKeys(player, false);
            }
        }
    }

    public void saveKeys(UUID player, boolean remove) throws IOException {
        File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/PlayerKeys.yml");
        ymlFile = YamlConfiguration.loadConfiguration(file);

        if (!file.exists()) {
            file.createNewFile();
        }

        if (playerKeys != null && !playerKeys.isEmpty() && playerKeys.containsKey(player)) {
            ymlFile.set(player.toString() + ".Delta", playerKeys.get(player).getDeltaKeys());
            ymlFile.set(player.toString() + ".Beta", playerKeys.get(player).getBetaKeys());
            ymlFile.set(player.toString() + ".Gamma", playerKeys.get(player).getGammaKeys());
            ymlFile.set(player.toString() + ".Omega", playerKeys.get(player).getOmegaKeys());
            ymlFile.set(player.toString() + ".Alpha", playerKeys.get(player).getAlphaKeys());
            ymlFile.set(player.toString() + ".Token", playerKeys.get(player).getTokenKeys());
            ymlFile.set(player.toString() + ".Random", playerKeys.get(player).getRandomKeys());
            ymlFile.set(player.toString() + ".Vote", playerKeys.get(player).getVoteKeys());
            ymlFile.set(player.toString() + ".Armor", playerKeys.get(player).getArmorKeys());

            ymlFile.save(file);

            if (remove) {
                playerKeys.remove(player);
            }
        }
    }

    public void loadKeys() throws IOException {
        File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/PlayerKeys.yml");
        ymlFile = YamlConfiguration.loadConfiguration(file);

        if (!file.exists()) {
            file.createNewFile();
        }

        for (Player player : Bukkit.getOnlinePlayers()) {
            loadKeys(player);
        }
    }

    public void loadKeys(Player player) throws IOException {
        File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/PlayerKeys.yml");
        ymlFile = YamlConfiguration.loadConfiguration(file);

        if (!file.exists()) {
            file.createNewFile();
        }

        if (!playerKeys.containsKey(player.getUniqueId())) {
            int deltaKeys = ymlFile.getInt(player.getUniqueId() + ".Delta");
            int betaKeys = ymlFile.getInt(player.getUniqueId() + ".Beta");
            int gammaKeys = ymlFile.getInt(player.getUniqueId() + ".Gamma");
            int omegaKeys = ymlFile.getInt(player.getUniqueId() + ".Omega");
            int alphaKeys = ymlFile.getInt(player.getUniqueId() + ".Alpha");
            int tokenKeys = ymlFile.getInt(player.getUniqueId() + ".Token");
            int randomKeys = ymlFile.getInt(player.getUniqueId() + ".Random");
            int voteKeys = ymlFile.getInt(player.getUniqueId() + ".Vote");
            int armorKeys = ymlFile.getInt(player.getUniqueId() + ".Armor");

            playerKeys.put(player.getUniqueId(), new KeyManager(deltaKeys, betaKeys, gammaKeys, omegaKeys, alphaKeys, tokenKeys, randomKeys, voteKeys, armorKeys));
        }
    }

    public void giveKeys(Player player, String type, int amount, boolean sendMessage) {
        type = gui.getTypeName(type);
        if (!playerKeys.containsKey(player.getUniqueId())) {
            playerKeys.put(player.getUniqueId(), new KeyManager(0, 0, 0, 0, 0, 0, 0, 0, 0));
        }
        if (!player.hasPermission("keys.autoopen")) {
            playerKeys.get(player.getUniqueId()).giveKeys(type, amount);
        } else {
            openKeys(player, type, amount, (type.equals("Armor")));
        }

        if (sendMessage && !player.hasPermission("keyfinder.remove")) {
            player.sendMessage(prefix + "§a§l+ §7" + amount + " " + keyNames.get(type));
        }

        resume.addValue(player, type, amount);
    }

    public void removeKeys(Player player, String type, int amount) {
        if (playerKeys.containsKey(player.getUniqueId())) {
            playerKeys.get(player.getUniqueId()).removeKeys(type, amount);
            player.sendMessage(prefix + "§c§l- §7" + amount + " " + type);
        }
    }

    public void withdrawKeys(Player player, String type, int amount, int times) {
        int playerKeysAmount = playerKeys.get(player.getUniqueId()).getKeys(type);

        amount = amount * times;
        if (playerKeysAmount >= amount) {
            int freeSlots = mainClass.getEmptySlots(player.getInventory());
            if (freeSlots > 0) {
                removeKeys(player, type, amount);

                ItemStack key = new ItemStack(Material.TRIPWIRE_HOOK);
                ItemMeta meta = key.getItemMeta();
                meta.setDisplayName(keyNames.get(type) + "§7 Key §7§o(§f§ox" + (amount / times) + "§7§o)");
                if (type.equalsIgnoreCase("Alpha")) {
                    meta = mainClass.addGlowing(meta);
                }
                List<String> lore = new ArrayList<>();
                lore.add("");
                lore.add("§7Right click to claim");
                meta.setLore(lore);
                key.setItemMeta(meta);
                key.setAmount(times);
                player.getInventory().addItem(key);
            } else {
                player.sendMessage(prefix + "§cYou don't have enough space in your inventory");
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough Keys");
        }
    }

    public double getExtraValue(double startingAmount) {
        double extra = 0;

        extra = (mainClass.getSeasonDay() * 0.75) * startingAmount;

        return extra;
    }

    public void openKeys(Player player, String type, int amount, boolean sendMessage) {
        // Use async key opening for large amounts
        if (amount >= 10000 && mainClass.getAsyncKeyOpeningManager() != null) {
            openKeysAsync(player, type, amount, sendMessage);
            return;
        }

        // Use original synchronous method for small amounts
        openKeysSync(player, type, amount, sendMessage);
    }

    /**
     * Opens keys asynchronously using the new async system.
     */
    public void openKeysAsync(Player player, String type, int amount, boolean sendMessage) {
        List<ItemList> itemList = keyList.getKeyList().get(type);
        if (itemList == null || itemList.isEmpty()) {
            player.sendMessage(prefix + "§cNo rewards configured for " + type + " keys");
            return;
        }

        // Check if player already has an active operation
        if (mainClass.getAsyncKeyOpeningManager().hasActiveKeyOpening(player)) {
            player.sendMessage(prefix + "§cYou already have keys being opened! Please wait...");
            return;
        }

        if (sendMessage) {
            player.sendMessage("");
            player.sendMessage("§e§lKEYS §7| Starting processing...");
            player.sendMessage("§7Keys Type: §f" + type);
            player.sendMessage("§7Amount: §f" + amount);
            player.sendMessage("");
        }

        // Submit to async manager
        mainClass.getAsyncKeyOpeningManager().openKeysAsync(player, type, amount, itemList, sendMessage)
            .thenAccept(result -> {
                if (result.isSuccess()) {
                    if (sendMessage) {
                        player.sendMessage("§e§lKEYS §8»§7 Successfully opened §f" + result.getProcessedKeys() + " §7keys!");
                    }
                } else {
                    player.sendMessage("§e§lKEYS §8»§c " + result.getErrorMessage());
                }
            })
            .exceptionally(throwable -> {
                player.sendMessage("§e§lKEYS §8»§c An error occurred while opening keys: " + throwable.getMessage());
                mainClass.getLogger().warning("Error in async key opening for " + player.getName() + ": " + throwable.getMessage());
                return null;
            });
    }

    /**
     * Opens keys synchronously (original method).
     */
    public void openKeysSync(Player player, String type, int amount, boolean sendMessage) {
        new BukkitRunnable() {
            @Override
            public void run() {
                List<ItemList> itemList = keyList.getKeyList().get(type);
                double totalTokens = 0;
                double totalMoney = 0;
                int totalXP = 0;
                int totalPrestigePoints = 0;
                int totalRobots = 0;
                HashMap<String, String> pexToAdd = new HashMap<>();
                HashMap<String, TemporaryReward> finalRewards = new HashMap<>();

                for (int i = 0; i < amount; i++) {
                    boolean rewardAdded = false;
                    int maxIteration = itemList.size() * 1000;

                    while (!rewardAdded && maxIteration > 0) {
                        double chance = ThreadLocalRandom.current().nextDouble(100);

                        //for (ItemList tmpItem : itemList) {
                        ItemList tmpItem = itemList.get(ThreadLocalRandom.current().nextInt(itemList.size()));
                        if (tmpItem.getChance() > chance /*&& !rewardAdded*/) {
                            /*if (tmpItem.getCommandToExecute().contains("giveTokens")) {
                                String tmp = tmpItem.getCommandToExecute();
                                tmp = ChatColor.stripColor(tmp);
                                tmp = tmp.replace("giveTokens", "");
                                tmp = tmp.trim();
                                double tokensToAdd = Double.parseDouble(tmp);
                                //if (type.equalsIgnoreCase("Token")) {
                                tokensToAdd = tokensToAdd + getExtraValue(tokensToAdd);
                                //}
                                totalTokens = totalTokens + tokensToAdd;
                            } else if (tmpItem.getCommandToExecute().contains("giveMoney")) {
                                String tmp = tmpItem.getCommandToExecute();
                                tmp = ChatColor.stripColor(tmp);
                                tmp = tmp.replace("giveMoney", "");
                                tmp = tmp.trim();
                                double moneyToAdd = Double.parseDouble(tmp);
                                totalMoney = totalMoney + moneyToAdd;
                            }*/
                            if (tmpItem.getCommandToExecute().contains("giveTokenBooster")) {
                                String tmp = tmpItem.getCommandToExecute();
                                tmp = ChatColor.stripColor(tmp);
                                tmp = tmp.replace("giveTokenBooster", "");
                                tmp = tmp.trim();
                                double tokensToAdd = Double.parseDouble(tmp);
                                totalTokens = totalTokens + tokensToAdd;
                            } else if (tmpItem.getCommandToExecute().contains("giveMoneyBooster")) {
                                String tmp = tmpItem.getCommandToExecute();
                                tmp = ChatColor.stripColor(tmp);
                                tmp = tmp.replace("giveMoneyBooster", "");
                                tmp = tmp.trim();
                                double moneyToAdd = Double.parseDouble(tmp);
                                totalMoney = totalMoney + moneyToAdd;
                            } else if (tmpItem.getCommandToExecute().contains("giveXP")) {
                                String tmp = tmpItem.getCommandToExecute();
                                tmp = ChatColor.stripColor(tmp);
                                tmp = tmp.replace("giveXP", "");
                                tmp = tmp.trim();
                                int xpToAdd = Integer.parseInt(tmp);
                                totalXP = totalXP + xpToAdd;
                            } else if (tmpItem.getCommandToExecute().contains("givePrestigePoints")) {
                                String tmp = tmpItem.getCommandToExecute();
                                tmp = ChatColor.stripColor(tmp);
                                tmp = tmp.replace("givePrestigePoints", "");
                                tmp = tmp.trim();
                                int pointsToAdd = Integer.parseInt(tmp);
                                totalPrestigePoints = totalPrestigePoints + pointsToAdd;
                            } else if (tmpItem.getCommandToExecute().contains("giveRobots")) {
                                String tmp = tmpItem.getCommandToExecute();
                                tmp = ChatColor.stripColor(tmp);
                                tmp = tmp.replace("giveRobots", "");
                                tmp = tmp.trim();
                                int pointsToAdd = Integer.parseInt(tmp);
                                totalRobots = totalRobots + pointsToAdd;
                            } else if (tmpItem.getCommandToExecute().contains("givePex")) {
                                String tmp = tmpItem.getCommandToExecute();
                                tmp = ChatColor.stripColor(tmp);
                                tmp = tmp.replace("givePex ", "");
                                pexToAdd.putIfAbsent(tmp, tmpItem.getMessageToSend());
                            } else {
                                String command = tmpItem.getCommandToExecute().replace("player", player.getName());
                                String[] tmp = command.split(" ");
                                int tmpAmount = Integer.parseInt(tmp[tmp.length - 1]);
                                if (!finalRewards.containsKey(command)) {
                                    finalRewards.put(command, new TemporaryReward(command, tmpItem.getMessageToSend(), tmpAmount));
                                } else {
                                    finalRewards.get(command).addAmount(tmpAmount);
                                }
                                //Bukkit.dispatchCommand(Bukkit.getConsoleSender(), tmpItem.getCommandToExecute().replace("player", player.getName()));
                                //player.sendMessage(prefix + tmpItem.getMessageToSend());
                            }

                            rewardAdded = true;
                            //player.sendMessage(prefix + tmpItem.getMessageToSend());
                        }
                        //}
                        maxIteration--;
                    }
                }

                if (sendMessage) {
                    player.sendMessage("");
                    player.sendMessage("§e§lKEYS §7| Resume");
                    player.sendMessage("§7Keys Type: §f" + type);
                    player.sendMessage("§7Amount: §f" + amount);
                    player.sendMessage("");
                }
                if (totalMoney > 0) {

                    /*String playerClassString = mainClass.getMongoReader().getPlayerData(player.getUniqueId()).getClassCode();
                    if (EnumUtils.isValidEnum(Enums.Classes.class, playerClassString)) {
                        Enums.Classes playerClass = Enums.Classes.valueOf(playerClassString);
                        if (playerClass == Enums.Classes.MERCHANT) {
                            totalMoney = totalMoney * playerClass.getBooster();
                        }
                    }*/

                    /*economy.depositPlayer(player, totalMoney);
                    resume.addValue(player, "Money", totalMoney);*/


                    if (player.hasPermission("pickreset.keysboosters")) {
                        totalMoney = totalMoney * 1.5;
                    }
                    PlayerData data = mainClass.getMongoReader().getPlayerData(player.getUniqueId());
                    data.setKeysMoneyBooster((data.getKeysMoneyBooster() == null ? 0 : data.getKeysMoneyBooster()) + totalMoney);
                    mainClass.getMongoReader().setPlayerMoneyBoosterInMap(player, data.getKeysMoneyBooster());
                    if (sendMessage) {
                        player.sendMessage("§7+ §e§l" + mainClass.newFormatNumber(totalMoney) + "%§7 Money Booster §7§o(" + mainClass.newFormatNumber(data.getKeysMoneyBooster()) + "%)");
                    }
                }
                if (totalTokens > 0) {

                    /*String playerClassString = mainClass.getMongoReader().getPlayerData(player.getUniqueId()).getClassCode();
                    if (EnumUtils.isValidEnum(Enums.Classes.class, playerClassString)) {
                        Enums.Classes playerClass = Enums.Classes.valueOf(playerClassString);
                        if (playerClass == Enums.Classes.MINER) {
                            totalTokens = totalTokens * playerClass.getBooster();
                        }
                    }

                    teAPI.addTokens(player, totalTokens);
                    resume.addValue(player, "Tokens", totalTokens);*/

                    if (player.hasPermission("pickreset.keysboosters")) {
                        totalTokens = totalTokens * 1.5;
                    }
                    PlayerData data = mainClass.getMongoReader().getPlayerData(player.getUniqueId());
                    data.setKeysTokenBooster((data.getKeysTokenBooster() == null ? 0 : data.getKeysTokenBooster()) + totalTokens);
                    mainClass.getMongoReader().setPlayerTokenBoosterInMap(player, data.getKeysTokenBooster());
                    if (sendMessage) {
                        player.sendMessage("§7+ §a§l" + mainClass.newFormatNumber(totalTokens) + "%§7 Token Booster §7§o(" + mainClass.newFormatNumber(data.getKeysTokenBooster()) + "%)");
                    }
                }
                if (totalXP > 0) {

                    String playerClassString = mainClass.getMongoReader().getPlayerData(player.getUniqueId()).getClassCode();
                    if (EnumUtils.isValidEnum(Enums.Classes.class, playerClassString)) {
                        Enums.Classes playerClass = Enums.Classes.valueOf(playerClassString);
                        if (playerClass == Enums.Classes.EXPERIENCER) {
                            totalXP = (int) (totalXP * playerClass.getBooster());
                        }
                    }

                    player.giveExp(totalXP);
                    if (sendMessage) {
                        player.sendMessage("§7+ §b§l" + mainClass.newFormatNumber(totalXP) + "§7 XP");
                    }
                }
                if (totalPrestigePoints > 0) {
                    mainClass.getPrestigePoints().addPrestigePoints(player, totalPrestigePoints);
                    if (sendMessage) {
                        player.sendMessage("§7+ §a§l" + mainClass.newFormatNumber(totalPrestigePoints) + "§7 Prestige Points");
                    }
                }
                if (totalRobots > 0) {
                    int tier = ThreadLocalRandom.current().nextInt(3) + 1;
                    mainClass.getRobotSystem().addPoints(player, tier, totalRobots);
                    if (sendMessage) {
                        player.sendMessage("§7+ §a§l" + mainClass.newFormatNumber(totalRobots) + "§7 Robots");
                    }
                }
                if (!pexToAdd.isEmpty()) {
                    User user = mainClass.getLuckPerms().getPlayerAdapter(Player.class).getUser(player);
                    for (String pex : pexToAdd.keySet()) {
                        if (player.hasPermission(pex)) {
                            it.masterzen.CustomArmor.ItemList armor = mainClass.getArmorSystem().getArmorFromPex(pex);
                            String armorRarity = ChatColor.stripColor(armor.getRarity());
                            if (armorRarity.contains("UNCOMMON")) {
                                mainClass.getArmorPointsSystem().addPoints(player, 25);
                                if (sendMessage) {
                                    player.sendMessage(prefix + armor.getName() + " §7Duplicate. You received 25 ArmorPoints");
                                }
                            } else if (armorRarity.contains("RARE")) {
                                mainClass.getArmorPointsSystem().addPoints(player, 50);
                                if (sendMessage) {
                                    player.sendMessage(prefix + armor.getName() + " §7Duplicate. You received 50 ArmorPoints");
                                }
                            } else if (armorRarity.contains("EPIC")) {
                                mainClass.getArmorPointsSystem().addPoints(player, 75);
                                if (sendMessage) {
                                    player.sendMessage(prefix + armor.getName() + " §7Duplicate. You received 75 ArmorPoints");
                                }
                            }
                        } else if (sendMessage) {
                            player.sendMessage(pexToAdd.get(pex));
                        }
                        Node node = Node.builder(pex).build();
                        user.data().add(node);
                        mainClass.getLuckPerms().getUserManager().saveUser(user);
                    }
                }

                for (String command : finalRewards.keySet()) {
                    int tmpAmount = finalRewards.get(command).getAmount();
                    String[] splittedCommand = finalRewards.get(command).getCommandToExecute().split(" ");
                    StringBuilder finalCommand = new StringBuilder();
                    for (int i = 0; i < splittedCommand.length - 1; i++) {
                        finalCommand.append(splittedCommand[i]).append(" ");
                    }
                    finalCommand.append(tmpAmount);
                    //mainClass.getLogger().info(finalCommand.toString());
                    if (command.contains("giveKey")) {
                        String tmpType = splittedCommand[1];
                        giveKeys(player, tmpType, tmpAmount, false);
                    } else {
                        Bukkit.dispatchCommand(Bukkit.getConsoleSender(), finalCommand.toString());
                    }
                    if (sendMessage) {
                        player.sendMessage("§7+ " + finalRewards.get(command).getMessageToSend().replace("XXX", String.valueOf(tmpAmount)));
                    }
                }
                if (sendMessage) {
                    player.sendMessage("");
                }
            }
        }.runTask(mainClass);
    }

    public void giveRandomKeys(Player player, int maxValue, int multiplier) {
        giveRandomKeys(player, maxValue, multiplier, 1);
    }

    public void giveRandomKeys(Player player, int maxValue, int multiplier, int times) {
        Map<String, Integer> keysAmount = new HashMap<>();

        for (int i = 0; i < times; i++) {
            int keyID = ThreadLocalRandom.current().nextInt(27);
            String type = "";
            if (keyID <= 6) {
                type = "Delta";
            } else if (keyID <= 12) {
                type = "Beta";
            } else if (keyID <= 17) {
                type = "Gamma";
            } else if (keyID <= 21) {
                type = "Token";
            } else if (keyID <= 24) {
                type = "Random";
            } else {
                type = "Omega";
            }
            int amount = ThreadLocalRandom.current().nextInt(maxValue) + 1;
            amount = amount * multiplier;
            keysAmount.put(type, keysAmount.getOrDefault(type, 0) + amount);
        }

        for (String type : keysAmount.keySet()) {
            giveKeys(player, type, keysAmount.get(type), !player.hasPermission("keyfinder.remove"));
        }
    }

    @EventHandler
    public void onPlayerClicks(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        Action action = event.getAction();

        if (action.equals(Action.RIGHT_CLICK_AIR) || action.equals(Action.RIGHT_CLICK_BLOCK)) {
            if (player.getInventory().getItemInMainHand().getType().equals(Material.TRIPWIRE_HOOK) && player.getInventory().getItemInMainHand().hasItemMeta() && player.getInventory().getItemInMainHand().getItemMeta().hasLore()) {
                // CANCELLO EVENTO ALTRIMENTI DA ERRORE "Caused by: java.lang.AssertionError: TRAP"
                event.setCancelled(true);

                boolean canWithdraw = false;
                for (String name : keyNames.values()) {
                    if (player.getInventory().getItemInMainHand().getItemMeta().getDisplayName().startsWith(name)) {
                        canWithdraw = true;
                    }
                }
                if (canWithdraw) {
                    String type = getKeyTypeFromTripwireHook(player.getInventory().getItemInMainHand());
                    int amount = 0; //getKeyAmountFromTripwireHook(player.getInventory().getItemInMainHand()) * player.getInventory().getItemInMainHand().getAmount();

                    List<ItemStack> itemToRemove = new ArrayList<>();
                    for (ItemStack inventoryItem : player.getInventory().getStorageContents()) {
                        if (inventoryItem != null && inventoryItem.isSimilar(player.getInventory().getItemInMainHand())) {
                            amount = amount + (getKeyAmountFromTripwireHook(inventoryItem) * inventoryItem.getAmount());
                            // SPOSTATO SOTTO ALTRIMENTI SE HAI 2 KEYS CON LO STESSO AMOUNT MA SU 2 SLOT DIVERSI IL REMOVE LI TOGLIE ENTRAMBI E PERDI KEYS
                            //player.getInventory().remove(inventoryItem);
                            mainClass.removeItemFromPlayer(player, inventoryItem, null, false, true, 0);
                            itemToRemove.add(inventoryItem);
                        }
                    }

                    giveKeys(player, type, amount, true);
                }
                //for (ItemStack item : itemToRemove) {
                //    player.getInventory().remove(item);
                //}
            }
        }
    }

    public void giveAll(String type, int amount) {
        for (Player player : Bukkit.getOnlinePlayers()) {
            giveKeys(player, type, amount, true);
        }
    }

    public String getKeyTypeFromTripwireHook(ItemStack item) {
        String name = ChatColor.stripColor(item.getItemMeta().getDisplayName());
        String[] splitted = name.split(" ");

        return gui.getTypeName(splitted[0]);
    }

    public int getKeyAmountFromTripwireHook(ItemStack item) {
        int keysAmount = 0;
        String name = ChatColor.stripColor(item.getItemMeta().getDisplayName());
        String[] splitted = name.split(" ");

        //String type = getKeyTypeFromTripwireHook(item);
        keysAmount = Integer.parseInt(splitted[splitted.length - 1].replace("(", "").replace(")", "").replace("x", ""));

        return keysAmount;
    }

    public void resetPlayer(Player player) {
        playerKeys.remove(player.getUniqueId());
        player.sendMessage(prefix + "Your keys has been resetted");
    }

    public void sendKeysBalance(Player player) {
        player.sendMessage("");
        player.sendMessage("§e§lKEYS §7| Balance");
        player.sendMessage("");
        player.sendMessage(keyNames.get("Delta") + "§7 Amount: " + playerKeys.get(player.getUniqueId()).getKeys("Delta"));
        player.sendMessage(keyNames.get("Beta") + "§7 Amount: " + playerKeys.get(player.getUniqueId()).getKeys("Beta"));
        player.sendMessage(keyNames.get("Gamma") + "§7 Amount: " + playerKeys.get(player.getUniqueId()).getKeys("Gamma"));
        player.sendMessage(keyNames.get("Token") + "§7 Amount: " + playerKeys.get(player.getUniqueId()).getKeys("Token"));
        player.sendMessage(keyNames.get("Vote") + "§7 Amount: " + playerKeys.get(player.getUniqueId()).getKeys("Vote"));
        player.sendMessage(keyNames.get("Random") + "§7 Amount: " + playerKeys.get(player.getUniqueId()).getKeys("Random"));
        player.sendMessage(keyNames.get("Omega") + "§7 Amount: " + playerKeys.get(player.getUniqueId()).getKeys("Omega"));
        player.sendMessage(keyNames.get("Armor") + "§7 Amount: " + playerKeys.get(player.getUniqueId()).getKeys("Armor"));
        player.sendMessage(keyNames.get("Alpha") + "§7 Amount: " + playerKeys.get(player.getUniqueId()).getKeys("Alpha"));
        //for (String type : keyNames.keySet()) {
        //    player.sendMessage(keyNames.get(type) + "§7 Amount: " + playerKeys.get(player.getUniqueId()).getKeys(type));
        //}
        player.sendMessage("");
    }

    public void manageAutoOpenPex(Player player) {
        if (player.hasPermission("essentials.kits.mvp+")) {
            if (player.hasPermission("keys.autoopen")) {
                mainClass.removePex(player, "keys.autoopen");
                player.sendMessage(prefix + "Auto Key Open Perk §c§lDISABLED");
            } else {
                mainClass.addPex(player, "keys.autoopen", 0, false);
                player.sendMessage(prefix + "Auto Key Open Perk §a§lENABLED");
            }
        } else {
            player.sendMessage(prefix + "§cYou need to get at least MVP+ in order to active this perk");
        }
    }

    public void startKeyAll(int tier) {
        int totalPlayers = Bukkit.getOnlinePlayers().size();

        if (totalPlayers > 0) {
            for (Player player : Bukkit.getOnlinePlayers()) {
                giveKeys(player, "Delta", (totalPlayers * 10) * tier, false);
                giveKeys(player, "Beta", (totalPlayers * 8) * tier, false);
                giveKeys(player, "Gamma", (totalPlayers * 5) * tier, false);
                giveKeys(player, "Token", (totalPlayers * 10) * tier, false);
                giveKeys(player, "Omega", (int) (Math.floor(totalPlayers / 10)) * tier, false);
                giveKeys(player, "Alpha", (int) (Math.floor(totalPlayers / 50)) * tier, false);
            }

            Bukkit.broadcastMessage("§e§lKEYALL §7§o(Automatic)");
            Bukkit.broadcastMessage("§7§oEveryone received the following rewards");
            Bukkit.broadcastMessage("");
            Bukkit.broadcastMessage("§7" + ((totalPlayers * 10) * tier) + "x " + keyNames.get("Delta"));
            Bukkit.broadcastMessage("§7" + ((totalPlayers * 8) * tier) + "x " + keyNames.get("Beta"));
            Bukkit.broadcastMessage("§7" + ((totalPlayers * 5) * tier) + "x " + keyNames.get("Gamma"));
            Bukkit.broadcastMessage("§7" + ((totalPlayers * 10) * tier) + "x " + keyNames.get("Token"));
            if ((Math.floor(totalPlayers / 10)) * tier > 0) {
                Bukkit.broadcastMessage("§7" + (int) ((Math.floor(totalPlayers / 10)) * tier) + "x " + keyNames.get("Omega"));
            }
            if ((Math.floor(totalPlayers / 50)) * tier > 0) {
                Bukkit.broadcastMessage("§7" + (int) ((Math.floor(totalPlayers / 50)) * tier) + "x " + keyNames.get("Alpha"));
            }
        }
    }

    @Override
    public boolean onCommand(CommandSender sender, Command cmd, String label, String[] args) {
        if (cmd.getName().equalsIgnoreCase("alphaKeys") || cmd.getName().equalsIgnoreCase("Key") || cmd.getName().equalsIgnoreCase("Keys")) {
            if (sender instanceof Player) {
                Player player = (Player) sender;
                if (!playerKeys.containsKey(player.getUniqueId())) {
                    playerKeys.put(player.getUniqueId(), new KeyManager(0, 0, 0, 0, 0, 0, 0, 0, 0));
                }
                if (player.isOp() || player.hasPermission("keys.admin")) {
                    if (args.length == 4 && args[0].equalsIgnoreCase("give")) {
                        Player tmpPlayer = Bukkit.getPlayerExact(args[1]);
                        giveKeys(tmpPlayer, args[2], Integer.parseInt(args[3]), true);
                        player.sendMessage(prefix + "§7Succesfully added §a" + Integer.parseInt(args[3]) + " " + getKeyName(gui.getTypeName(args[2])) + " §7to " + tmpPlayer.getName());
                    } else if (args.length == 3 && args[0].equalsIgnoreCase("giveall")) {
                        giveAll(gui.getTypeName(args[1]), Integer.parseInt(args[2]));
                    } else if (args.length == 4 && args[0].equalsIgnoreCase("remove")) {
                        Player tmpPlayer = Bukkit.getPlayerExact(args[1]);
                        removeKeys(tmpPlayer, args[2], Integer.parseInt(args[3]));
                        player.sendMessage(prefix + "§7Succesfully removed §a" + Integer.parseInt(args[3]) + " " + getKeyName(gui.getTypeName(args[2])) + " §7to " + tmpPlayer.getName());
                    } else if (args.length == 2 && args[0].equalsIgnoreCase("reset")) {
                        Player tmpPlayer = Bukkit.getPlayerExact(args[1]);
                        resetPlayer(tmpPlayer);
                        player.sendMessage(prefix + tmpPlayer.getName() + "§7 Succesfully resetted");
                    } else if (args.length > 0 && args[0].equalsIgnoreCase("keyall")) {
                        if (args.length == 2) {
                            if (mainClass.isNumber(args[1])) {
                                startKeyAll(Integer.parseInt(args[1]));
                            } else {
                                startKeyAll(1);
                            }
                        }
                    }
                }
                if (args.length == 3 && args[0].equalsIgnoreCase("withdraw")) {
                    String type = gui.getTypeName(args[1]);
                    if (keyNames.containsKey(type)) {
                        int amount = Integer.parseInt(args[2]);
                        if (amount > 0) {
                            withdrawKeys(player, type, Math.round(amount), 1);
                        }
                    } else {
                        player.sendMessage(prefix + "§cYou can't withdraw this Key");
                    }
                } else if (args.length == 4 && args[0].equalsIgnoreCase("withdraw")) {
                    String type = gui.getTypeName(args[1]);
                    if (Integer.parseInt(args[3]) <= 2304) {
                        if (keyNames.containsKey(type)) {
                            int amount = Integer.parseInt(args[2]);
                            if (amount > 0) {
                                withdrawKeys(player, type, Math.round(amount), Integer.parseInt(args[3]));
                            }
                        } else {
                            player.sendMessage(prefix + "§cYou can't withdraw this Key");
                        }
                    } else {
                        player.sendMessage(prefix + "§cYou can withdraw a max of 2304 items at time !");
                    }
                } else if (args.length == 2 && args[0].equalsIgnoreCase("withdrawall")) {
                    String type = gui.getTypeName(args[1]);
                    if (keyNames.containsKey(type)) {
                        int amount = playerKeys.get(player.getUniqueId()).getKeys(type);
                        if (amount > 0) {
                            withdrawKeys(player, type, Math.round(amount), 1);
                        }
                    } else {
                        player.sendMessage(prefix + "§cYou can't withdraw this Key");
                    }
                } else if (args.length == 1 && args[0].equalsIgnoreCase("withdrawall")) {
                    for (String type : keyNames.keySet()) {
                        int amount = playerKeys.get(player.getUniqueId()).getKeys(type);
                        if (amount > 0) {
                            withdrawKeys(player, type, Math.round(amount), 1);
                        }
                    }
                } else if (args.length == 1 && args[0].equalsIgnoreCase("bal")) {
                    sendKeysBalance(player);
                } else if (args.length == 0) {
                    gui.openGUI(player);
                }
            } else {
                if (args.length == 4 && args[0].equalsIgnoreCase("give")) {
                    Player tmpPlayer = Bukkit.getPlayerExact(args[1]);
                    giveKeys(tmpPlayer, args[2], Integer.parseInt(args[3]), true);
                } else if (args.length == 3 && args[0].equalsIgnoreCase("giveall")) {
                    giveAll(gui.getTypeName(args[1]), Integer.parseInt(args[2]));
                } else if (args.length == 4 && args[0].equalsIgnoreCase("remove")) {
                    Player tmpPlayer = Bukkit.getPlayerExact(args[1]);
                    giveKeys(tmpPlayer, args[2], Integer.parseInt(args[3]), true);
                } else if (args.length == 2 && args[0].equalsIgnoreCase("reset")) {
                    Player tmpPlayer = Bukkit.getPlayerExact(args[1]);
                    resetPlayer(tmpPlayer);
                }
            }
            //}
        }
        return false;
    }
}
