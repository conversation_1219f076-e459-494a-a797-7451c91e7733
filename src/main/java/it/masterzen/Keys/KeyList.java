package it.masterzen.Keys;

import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.blockbreak.EnchantList;
import it.masterzen.blockbreak.XMaterial;
import org.bukkit.Material;
import org.bukkit.entity.Item;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class KeyList {
    private final HashMap<String, List<ItemList>> keyList = new HashMap<>();

    public KeyList() {
        setupCrates();
    }

    public HashMap<String, List<ItemList>> getKeyList() {
        return this.keyList;
    }

    public void setupCrates() {
        List<ItemList> itemList = new ArrayList<>();

        itemList = new ArrayList<>();
        itemList.add(new ItemList("§e§l0.002% §7Money Seasonal Booster", new ArrayList<>(), 50, Material.EMERALD, "giveMoneyBooster 0.002", "§e§l0.002% §7Money Seasonal Booster"));
        itemList.add(new ItemList("§e§l0.004% §7Money Seasonal Booster", new ArrayList<>(), 25, Material.EMERALD, "giveMoneyBooster 0.004", "§e§l0.004% §7Money Seasonal Booster"));
        itemList.add(new ItemList("§a§l0.001% §7Tokens Seasonal Booster", new ArrayList<>(), 25, Material.MAGMA_CREAM, "giveTokenBooster 0.001", "§a§l0.001% §7Token Seasonal Booster"));
        itemList.add(new ItemList("§a§l0.002% §7Tokens Seasonal Booster", new ArrayList<>(), 10, Material.MAGMA_CREAM, "giveTokenBooster 0.002", "§a§l0.002% §7Token Seasonal Booster"));
        itemList.add(new ItemList("§b§l25 §7XP", new ArrayList<>(), 25, Material.EXP_BOTTLE, "giveXP 25", "§b§l25 §7XP"));
        itemList.add(new ItemList("§a§lTOKEN §7Key", new ArrayList<>(), 5, Material.TRIPWIRE_HOOK, "giveKey Token 1", "XXX §a§lTOKEN §7Key"));
        itemList.add(new ItemList("§b§lDELTA §7Key", new ArrayList<>(), 2.5, Material.TRIPWIRE_HOOK, "giveKey Delta 1", "XXX §b§lDELTA §7Keys"));
        itemList.add(new ItemList("§c§lBETA §7Key", new ArrayList<>(), 2, Material.TRIPWIRE_HOOK, "giveKey Beta 1", "XXX §c§lBETA §7Key"));
        //itemList.add(new ItemList("§a§lVIP §7Voucher", new ArrayList<>(), 0.01, Material.PAPER, "voucher give player Vip 1", "1 §a§lVIP §7Voucher"));
        keyList.put("Delta", itemList);

        itemList = new ArrayList<>();
        itemList.add(new ItemList("§e§l0.004% §7Money Seasonal Booster", new ArrayList<>(), 50, Material.EMERALD, "giveMoneyBooster 0.004", "§e§l0.004% §7Money Seasonal Booster"));
        itemList.add(new ItemList("§e§l0.008% §7Money Seasonal Booster", new ArrayList<>(), 25, Material.EMERALD, "giveMoneyBooster 0.008", "§e§l0.008% §7Money Seasonal Booster"));
        itemList.add(new ItemList("§a§l0.002% §7Tokens Seasonal Booster", new ArrayList<>(), 25, Material.MAGMA_CREAM, "giveTokenBooster 0.002", "§a§l0.002% §7Token Seasonal Booster"));
        itemList.add(new ItemList("§a§l0.004% §7Tokens Seasonal Booster", new ArrayList<>(), 10, Material.MAGMA_CREAM, "giveTokenBooster 0.004", "§a§l0.004% §7Token Seasonal Booster"));
        itemList.add(new ItemList("§b§l50 §7XP", new ArrayList<>(), 25, Material.EXP_BOTTLE, "giveXP 50", "§b§l50 §7XP"));
        itemList.add(new ItemList("§a§lTOKEN §7Key", new ArrayList<>(), 5, Material.TRIPWIRE_HOOK, "giveKey Token 2", "XXX §a§lTOKEN §7Key"));
        itemList.add(new ItemList("§b§lDELTA §7Key", new ArrayList<>(), 5, Material.TRIPWIRE_HOOK, "giveKey Delta 2", "XXX §b§lDELTA §7Keys"));
        itemList.add(new ItemList("§c§lBETA §7Key", new ArrayList<>(), 2.5, Material.TRIPWIRE_HOOK, "giveKey Beta 2", "XXX §c§lBETA §7Key"));
        itemList.add(new ItemList("§d§lGAMMA §7Key", new ArrayList<>(), 2, Material.TRIPWIRE_HOOK, "giveKey Gamma 1", "XXX §d§lGAMMA §7Key"));
        itemList.add(new ItemList("§2§lRAN§a§lDOM §7Key", new ArrayList<>(), 2, Material.TRIPWIRE_HOOK, "giveKey Random 1", "XXX §2§lRAN§a§lDOM §7Key"));
        //itemList.add(new ItemList("§a§lVIP §7Voucher", new ArrayList<>(), 0.015, Material.PAPER, "voucher give player Vip 1", "XXX §a§lVIP §7Voucher"));
        //itemList.add(new ItemList("§a§lVIP+ §7Voucher", new ArrayList<>(), 0.01, Material.PAPER, "voucher give player Vip+ 1", "XXX §a§lVIP+ §7Voucher"));
        keyList.put("Beta", itemList);

        itemList = new ArrayList<>();
        itemList.add(new ItemList("§e§l0.008% §7Money Seasonal Booster", new ArrayList<>(), 50, Material.EMERALD, "giveMoneyBooster 0.008", "§e§l0.008% §7Money Seasonal Booster"));
        itemList.add(new ItemList("§e§l0.016% §7Money Seasonal Booster", new ArrayList<>(), 25, Material.EMERALD, "giveMoneyBooster 0.016", "§e§l0.016% §7Money Seasonal Booster"));
        itemList.add(new ItemList("§a§l0.004% §7Tokens Seasonal Booster", new ArrayList<>(), 25, Material.MAGMA_CREAM, "giveTokenBooster 0.004", "§a§l0.004% §7Token Seasonal Booster"));
        itemList.add(new ItemList("§a§l0.008% §7Tokens Seasonal Booster", new ArrayList<>(), 10, Material.MAGMA_CREAM, "giveTokenBooster 0.008", "§a§l0.008% §7Token Seasonal Booster"));
        itemList.add(new ItemList("§b§l100 §7XP", new ArrayList<>(), 25, Material.EXP_BOTTLE, "giveXP 10", "§b§l100 §7XP"));
        itemList.add(new ItemList("§a§lTOKEN §7Key", new ArrayList<>(), 5, Material.TRIPWIRE_HOOK, "giveKey Token 3", "XXX §a§lTOKEN §7Key"));
        itemList.add(new ItemList("§c§lBETA §7Key", new ArrayList<>(), 5, Material.TRIPWIRE_HOOK, "giveKey Beta 2", "XXX §c§lBETA §7Keys"));
        itemList.add(new ItemList("§d§lGAMMA §7Key", new ArrayList<>(), 2.5, Material.TRIPWIRE_HOOK, "giveKey Gamma 2", "XXX §d§lGAMMA §7Keys"));
        itemList.add(new ItemList("§e§lOMEGA §7Key", new ArrayList<>(), 2, Material.TRIPWIRE_HOOK, "giveKey Omega 1", "XXX §e§lOMEGA §7Key"));
        itemList.add(new ItemList("§2§lRAN§a§lDOM §7Key", new ArrayList<>(), 2, Material.TRIPWIRE_HOOK, "giveKey Random 1", "XXX §2§lRAN§a§lDOM §7Key"));
        //itemList.add(new ItemList("§a§lVIP+ §7Voucher", new ArrayList<>(), 0.015, Material.PAPER, "voucher give player Vip+ 1", "XXX §a§lVIP+ §7Voucher"));
        //itemList.add(new ItemList("§a§lMVP §7Voucher", new ArrayList<>(), 0.01, Material.PAPER, "voucher give player MVP 1", "XXX §a§lMVP §7Voucher"));
        keyList.put("Gamma", itemList);

        itemList = new ArrayList<>();
        itemList.add(new ItemList("§e§l0.025% §7Money Seasonal Booster", new ArrayList<>(), 50, Material.EMERALD, "giveMoneyBooster 0.025", "§e§l0.025% §7Money Seasonal Booster"));
        itemList.add(new ItemList("§e§l0.05% §7Money Seasonal Booster", new ArrayList<>(), 25, Material.EMERALD, "giveMoneyBooster 0.05", "§e§l0.05% §7Money Seasonal Booster"));
        itemList.add(new ItemList("§a§l0.015% §7Tokens Seasonal Booster", new ArrayList<>(), 25, Material.MAGMA_CREAM, "giveTokenBooster 0.015", "§a§l0.015% §7Token Seasonal Booster"));
        itemList.add(new ItemList("§a§l0.03% §7Tokens Seasonal Booster", new ArrayList<>(), 10, Material.MAGMA_CREAM, "giveTokenBooster 0.03", "§a§l0.03% §7Token Seasonal Booster"));
        itemList.add(new ItemList("§b§l250 §7XP", new ArrayList<>(), 25, Material.EXP_BOTTLE, "giveXP 25", "§b§l250 §7XP"));
        itemList.add(new ItemList("§a§lTOKEN §7Key", new ArrayList<>(), 5, Material.TRIPWIRE_HOOK, "giveKey Token 4", "XXX §a§lTOKEN §7Key"));
        itemList.add(new ItemList("§d§lGAMMA §7Key", new ArrayList<>(), 5, Material.TRIPWIRE_HOOK, "giveKey Gamma 2", "XXX §d§lGAMMA §7Keys"));
        itemList.add(new ItemList("§e§lOMEGA §7Key", new ArrayList<>(), 2.5, Material.TRIPWIRE_HOOK, "giveKey Omega 2", "XXX §e§lOMEGA §7Keys"));
        //itemList.add(new ItemList("§6§lARMOR §7Key", new ArrayList<>(), 0.3, Material.TRIPWIRE_HOOK, "giveKey Armor 1", "XXX §6§lARMOR §7Keys"));
        itemList.add(new ItemList("§c§lA§6§lL§e§lP§a§lH§b§lA§r §7Key", new ArrayList<>(), 2, Material.TRIPWIRE_HOOK, "giveKey Alpha 1", "XXX §c§lA§6§lL§e§lP§a§lH§b§lA§r §7Key"));
        itemList.add(new ItemList("§2§lRAN§a§lDOM §7Key", new ArrayList<>(), 2, Material.TRIPWIRE_HOOK, "giveKey Random 1", "XXX §2§lRAN§a§lDOM §7Key"));
        itemList.add(new ItemList("§a§lMVP §7Voucher", new ArrayList<>(), 0.002, Material.PAPER, "voucher give player MVP 1", "XXX §a§lMVP §7Voucher"));
        itemList.add(new ItemList("§a§lMVP+ §7Voucher", new ArrayList<>(), 0.001, Material.PAPER, "voucher give player MVP+ 1", "XXX §a§lMVP+ §7Voucher"));
        keyList.put("Omega", itemList);

        itemList = new ArrayList<>();
        itemList.add(new ItemList("§e§l0.1% §7Money Seasonal Booster", new ArrayList<>(), 50, Material.EMERALD, "giveMoneyBooster 0.1", "§e§l0.1% §7Money Seasonal Booster"));
        itemList.add(new ItemList("§e§l0.2% §7Money Seasonal Booster", new ArrayList<>(), 25, Material.EMERALD, "giveMoneyBooster 0.2", "§e§l0.2% §7Money Seasonal Booster"));
        itemList.add(new ItemList("§a§l0.05% §7Tokens Seasonal Booster", new ArrayList<>(), 25, Material.MAGMA_CREAM, "giveTokenBooster 0.05", "§a§l0.05% §7Token Seasonal Booster"));
        itemList.add(new ItemList("§a§l0.075% §7Tokens Seasonal Booster", new ArrayList<>(), 10, Material.MAGMA_CREAM, "giveTokenBooster 0.075", "§a§l0.075% §7Token Seasonal Booster"));
        itemList.add(new ItemList("§b§l1.000 §7XP", new ArrayList<>(), 25, Material.EXP_BOTTLE, "giveXP 1000", "§b§l1k §7XP"));
        itemList.add(new ItemList("§6§lARMOR §7Key", new ArrayList<>(), 1, Material.TRIPWIRE_HOOK, "giveKey Armor 1", "XXX §6§lARMOR §7Keys"));
        itemList.add(new ItemList("§c§lA§6§lL§e§lP§a§lH§b§lA§r §7Key", new ArrayList<>(), 2.5, Material.TRIPWIRE_HOOK, "giveKey Alpha 2", "XXX §c§lA§6§lL§e§lP§a§lH§b§lA§r §7Keys"));
        itemList.add(new ItemList("§c§lR§6§lA§e§lN§a§lK §7Crate", new ArrayList<>(), 0.25, Material.ENDER_CHEST, "amc give player Rank 1", "XXX §c§lR§6§lA§e§lN§a§lK §7Crate"));
        itemList.add(new ItemList("§c§lM§6§lO§e§lN§a§lT§b§lH§5§lL§d§lY §7Crate", new ArrayList<>(), 0.2, Material.ENDER_CHEST, "amc give player AlphaMonthlyCrate 1", "XXX §c§lM§6§lO§e§lN§a§lT§b§lH§5§lL§d§lY §7Crate"));
        keyList.put("Alpha", itemList);

        // Alpha crate: add Mine Bomb Tier 2 (very low chance)
        itemList.add(new ItemList("§6Mine Bomb §7(Money) §8T2", new ArrayList<>(), 0.25, Material.FIREWORK_CHARGE, "minebomb give player money 2 1", "XXX §6Mine Bomb §7(Money) T2"));
        itemList.add(new ItemList("§6Mine Bomb §7(Tokens) §8T2", new ArrayList<>(), 0.25, Material.FIREWORK_CHARGE, "minebomb give player tokens 2 1", "XXX §6Mine Bomb §7(Tokens) T2"));

        itemList = new ArrayList<>();
        itemList.add(new ItemList("§a§l0.001% §7Tokens Seasonal Booster", new ArrayList<>(), 50, Material.MAGMA_CREAM, "giveTokenBooster 0.001", "§a§l0.001% §7Token Seasonal Booster"));
        itemList.add(new ItemList("§a§l0.002% §7Tokens Seasonal Booster", new ArrayList<>(), 25, Material.MAGMA_CREAM, "giveTokenBooster 0.002", "§a§l0.002% §7Token Seasonal Booster"));
        itemList.add(new ItemList("§a§l0.005% §7Tokens Seasonal Booster", new ArrayList<>(), 10, Material.MAGMA_CREAM, "giveTokenBooster 0.005", "§a§l0.005% §7Token Seasonal Booster"));
        itemList.add(new ItemList("§a§l0.01% §7Tokens Seasonal Booster", new ArrayList<>(), 5, Material.MAGMA_CREAM, "giveTokenBooster 0.01", "§a§l0.01% §7Token Seasonal Booster"));
        itemList.add(new ItemList("§a§l0.025% §7Tokens Seasonal Booster", new ArrayList<>(), 2.5, Material.MAGMA_CREAM, "giveTokenBooster 0.025", "§a§l0.025% §7Token Seasonal Booster"));
        itemList.add(new ItemList("§a§l0.05% §7Tokens Seasonal Booster", new ArrayList<>(), 1, Material.MAGMA_CREAM, "giveTokenBooster 0.05", "§a§l0.05% §7Token Seasonal Booster"));
        keyList.put("Token", itemList);

        itemList = new ArrayList<>();
        itemList.add(new ItemList("§73 Tier 1-3 Robots", new ArrayList<>(), 10, XMaterial.ZOMBIE_SPAWN_EGG.parseMaterial(), "giveRobots 3", "XXX §7Robots"));
        itemList.add(new ItemList("§72500 Prestige Points", new ArrayList<>(), 10, Material.DIAMOND, "givePrestigePoints 2500", "XXX §7Prestige Points"));
        //itemList.add(new ItemList("§7100 Tier 1 Crystals", new ArrayList<>(), 10, Material.PRISMARINE_SHARD, "dungeon give player 1 100", "XXX §7Tier 1 Crystals"));
        //itemList.add(new ItemList("§750 Tier 2 Crystals", new ArrayList<>(), 10, Material.PRISMARINE_SHARD, "dungeon give player 2 50", "XXX §7Tier 2 Crystals"));
        //itemList.add(new ItemList("§725 Tier 3 Crystals", new ArrayList<>(), 10, Material.PRISMARINE_SHARD, "dungeon give player 3 25", "XXX §7Tier 3 Crystals"));
        itemList.add(new ItemList("§710 Minute - Supreme Mine", new ArrayList<>(), 5, Material.PAPER, "voucher give player SupremeMine 1", "XXX §710 Minute - Supreme Mine"));
        itemList.add(new ItemList("§710 Minute - Rainbow Mine", new ArrayList<>(), 2, Material.PAPER, "voucher give player RainbowMine 1", "XXX §710 Minute - Rainbow Mine"));
        //itemList.add(new ItemList("§7Silverfish Spawner", new ArrayList<>(), 1, Material.MOB_SPAWNER, "stacker give player spawner SilverFish 1", "XXX §7Silverfish Spawner"));
        //itemList.add(new ItemList("§7MobCoins Spawner", new ArrayList<>(), 1, Material.MOB_SPAWNER, "stacker give player spawner Villager 1", "XXX §7Villager Spawner"));
        //itemList.add(new ItemList("§7Money Spawner", new ArrayList<>(), 1, Material.MOB_SPAWNER, "stacker give player spawner Bat 1", "XXX §7Money Spawner"));
        //itemList.add(new ItemList("§7Token Spawner", new ArrayList<>(), 1, Material.MOB_SPAWNER, "stacker give player spawner Parrot 1", "XXX §7Token Spawner"));
        keyList.put("Random", itemList);

        // Random crate: add Mine Bomb Tier 1 (very low chance)
        itemList.add(new ItemList("§6Mine Bomb §7(Money) §8T1", new ArrayList<>(), 0.25, Material.FIREWORK_CHARGE, "minebomb give player money 1 1", "XXX §6Mine Bomb §7(Money) T1"));
        itemList.add(new ItemList("§6Mine Bomb §7(Tokens) §8T1", new ArrayList<>(), 0.25, Material.FIREWORK_CHARGE, "minebomb give player tokens 1 1", "XXX §6Mine Bomb §7(Tokens) T1"));
        itemList = new ArrayList<>();
        itemList.add(new ItemList("§e§l0.01% §7Money Seasonal Booster", new ArrayList<>(), 50, Material.EMERALD, "giveMoneyBooster 0.01", "§e§l0.01% §7Money Seasonal Booster"));
        itemList.add(new ItemList("§e§l0.02% §7Money Seasonal Booster", new ArrayList<>(), 25, Material.EMERALD, "giveMoneyBooster 0.02", "§e§l0.02% §7Money Seasonal Booster"));
        itemList.add(new ItemList("§a§l0.005% §7Tokens Seasonal Booster", new ArrayList<>(), 25, Material.MAGMA_CREAM, "giveTokenBooster 0.005", "§a§l0.005% §7Token Seasonal Booster"));
        itemList.add(new ItemList("§a§l0.01% §7Tokens Seasonal Booster", new ArrayList<>(), 10, Material.MAGMA_CREAM, "giveTokenBooster 0.01", "§a§l0.01% §7Token Seasonal Booster"));
        itemList.add(new ItemList("§b§l500 §7XP", new ArrayList<>(), 25, Material.EXP_BOTTLE, "giveXP 500", "§b§l500 §7XP"));
        //itemList.add(new ItemList("§b§lDELTA §7Key", new ArrayList<>(), 5, Material.TRIPWIRE_HOOK, "giveKey Delta 50", "XXX §b§lDELTA §7Keys"));
        //itemList.add(new ItemList("§c§lBETA §7Key", new ArrayList<>(), 2.5, Material.TRIPWIRE_HOOK, "giveKey Beta 25", "XXX §c§lBETA §7Key"));
        //itemList.add(new ItemList("§a§lTOKEN §7Key", new ArrayList<>(), 5, Material.TRIPWIRE_HOOK, "giveKey Token 10", "XXX §a§lTOKEN §7Key"));
        //itemList.add(new ItemList("§d§lGAMMA §7Key", new ArrayList<>(), 5, Material.TRIPWIRE_HOOK, "giveKey Gamma 5", "XXX §d§lGAMMA §7Keys"));
        itemList.add(new ItemList("§6Mine Bomb §7(Money) §8T2", new ArrayList<>(), 1, Material.FIREWORK_CHARGE, "minebomb give player money 2 1", "XXX §6Mine Bomb §7(Money) T2"));
        itemList.add(new ItemList("§6Mine Bomb §7(Tokens) §8T2", new ArrayList<>(), 1, Material.FIREWORK_CHARGE, "minebomb give player tokens 2 1", "XXX §6Mine Bomb §7(Tokens) T2"));
        itemList.add(new ItemList("§2§lRAN§a§lDOM §7Key", new ArrayList<>(), 2, Material.TRIPWIRE_HOOK, "giveKey Random 5", "XXX §2§lRAN§a§lDOM §7Key"));
        itemList.add(new ItemList("§e§lOMEGA §7Key", new ArrayList<>(), 2.5, Material.TRIPWIRE_HOOK, "giveKey Omega 2", "XXX §e§lOMEGA §7Keys"));
        itemList.add(new ItemList("§c§lA§6§lL§e§lP§a§lH§b§lA§r §7Key", new ArrayList<>(), 2, Material.TRIPWIRE_HOOK, "giveKey Alpha 1", "XXX §c§lA§6§lL§e§lP§a§lH§b§lA§r §7Key"));
        itemList.add(new ItemList("§a§lVIP §7Voucher", new ArrayList<>(), 0.025, Material.PAPER, "voucher give player Vip 1", "XXX §a§lVIP §7Voucher"));
        itemList.add(new ItemList("§a§lVIP+ §7Voucher", new ArrayList<>(), 0.020, Material.PAPER, "voucher give player Vip+ 1", "XXX §a§lVIP+ §7Voucher"));
        itemList.add(new ItemList("§a§lMVP §7Voucher", new ArrayList<>(), 0.015, Material.PAPER, "voucher give player MVP 1", "XXX §a§lMVP §7Voucher"));
        itemList.add(new ItemList("§a§lMVP+ §7Voucher", new ArrayList<>(), 0.01, Material.PAPER, "voucher give player MVP+ 1", "XXX §a§lMVP+ §7Voucher"));
        keyList.put("Vote", itemList);

        itemList = new ArrayList<>();
        List<it.masterzen.CustomArmor.ItemList> customArmor = AlphaBlockBreak.GetInstance().getArmorSystem().getCustomArmor();
        for (it.masterzen.CustomArmor.ItemList armor : customArmor) {
            itemList.add(new ItemList(armor.getName(), AlphaBlockBreak.GetInstance().getArmorSystem().getGui().getLoreFromArmor(armor, false, null, false), armor.getChanceForCrate(), armor.getMaterial(), "givePex " + armor.getPex(), armor.getRarity() + " " + armor.getName() + " §aUnlocked "));
        }
        keyList.put("Armor", itemList);
    }
}
