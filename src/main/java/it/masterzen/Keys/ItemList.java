package it.masterzen.Keys;

import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;

import java.util.List;

public class ItemList {

    private String name;
    private List<String> lore;
    private double chance;
    private Material item;
    private String commandToExecute;
    private String messageToSend;

    public ItemList(String name, List<String> lore, double chance, Material item, String commandToExecute, String messageToSend) {
        this.name = name;
        this.lore = lore;
        this.chance = chance;
        this.item = item;
        this.commandToExecute = commandToExecute;
        this.messageToSend = messageToSend;
    }

    public String getName() {
        return this.name;
    }

    public List<String> getLore() {
        return this.lore;
    }

    public double getChance() {
        return this.chance;
    }

    public Material getItem() {
        return this.item;
    }

    public String getCommandToExecute() {
        return this.commandToExecute;
    }

    public String getMessageToSend() {
        return this.messageToSend;
    }

}
