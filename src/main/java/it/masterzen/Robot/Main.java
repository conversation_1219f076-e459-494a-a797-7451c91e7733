package it.masterzen.Robot;

import com.sk89q.worldedit.MaxChangedBlocksException;
import com.sk89q.worldedit.world.DataException;
import com.sk89q.worldguard.protection.ApplicableRegionSet;
import com.sk89q.worldguard.protection.flags.DefaultFlag;
import com.sk89q.worldguard.protection.flags.StateFlag;
import it.masterzen.blockbreak.AlphaBlockBreak;
import me.clip.ezblocks.EZBlocks;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

public class Main implements CommandExecutor, Listener {

    private static HashMap<UUID, Robot> points = new HashMap<>();
    private final String prefix = "§e§lROBOTS §8»§7 ";
    private final String name = "Robots";
    private static YamlConfiguration ymlFile;

    private it.masterzen.Robot.GUI gui;
    public final AlphaBlockBreak mainClass;

    public Main(AlphaBlockBreak plugin) {
        mainClass = plugin;
        gui = new GUI(this);
    }

    public String getPrefix() {
        return this.prefix;
    }

    public String getName() {
        return this.name;
    }

    public void loadPoints() {
        for (Player player : Bukkit.getOnlinePlayers()) {
            loadPoints(player);
        }
    }

    public void loadPoints(Player player) {
        File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/Player" + name + ".yml");
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (Exception ex) {
            }
        }

        ymlFile = YamlConfiguration.loadConfiguration(file);
        if (!points.containsKey(player.getUniqueId()) && ymlFile.contains(player.getUniqueId() + ".tier1Amount")) {
            points.put(player.getUniqueId(), new Robot(LocalDateTime.parse(ymlFile.getString(player.getUniqueId() + ".lastuse")),
                    ymlFile.getLong(player.getUniqueId() + ".tier1Amount"),
                    ymlFile.getLong(player.getUniqueId() + ".tier2Amount"),
                    ymlFile.getLong(player.getUniqueId() + ".tier3Amount"),
                    ymlFile.getLong(player.getUniqueId() + ".tier4Amount"),
                    ymlFile.getLong(player.getUniqueId() + ".tier5Amount"),
                    ymlFile.getLong(player.getUniqueId() + ".tier6Amount"),
                    ymlFile.getLong(player.getUniqueId() + ".tier7Amount"),
                    ymlFile.getLong(player.getUniqueId() + ".tier8Amount"),
                    ymlFile.getLong(player.getUniqueId() + ".tier9Amount"),
                    ymlFile.getLong(player.getUniqueId() + ".tier10Amount")
                    ));
        }
    }

    public HashMap<UUID, Robot> getPoints() {
        return points;
    }

    public void savePoints() throws IOException {
        File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/Player" + name + ".yml");
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (Exception ex) {
            }
        }

        ymlFile = YamlConfiguration.loadConfiguration(file);
        if (!points.isEmpty() && ymlFile != null) {
            for (UUID player : points.keySet()) {
                savePoints(player, false);
            }
        }
    }

    public void savePoints(Player player, boolean remove) throws IOException {
        if (!points.isEmpty() && points.containsKey(player.getUniqueId())) {
            File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/Player" + name + ".yml");
            ymlFile = YamlConfiguration.loadConfiguration(file);
            ymlFile.set(player.toString() + ".tier1Amount", points.get(player.getUniqueId()).getTier1Amount());
            ymlFile.set(player.toString() + ".tier2Amount", points.get(player.getUniqueId()).getTier2Amount());
            ymlFile.set(player.toString() + ".tier3Amount", points.get(player.getUniqueId()).getTier3Amount());
            ymlFile.set(player.toString() + ".tier4Amount", points.get(player.getUniqueId()).getTier4Amount());
            ymlFile.set(player.toString() + ".tier5Amount", points.get(player.getUniqueId()).getTier5Amount());
            ymlFile.set(player.toString() + ".tier6Amount", points.get(player.getUniqueId()).getTier6Amount());
            ymlFile.set(player.toString() + ".tier7Amount", points.get(player.getUniqueId()).getTier7Amount());
            ymlFile.set(player.toString() + ".tier8Amount", points.get(player.getUniqueId()).getTier8Amount());
            ymlFile.set(player.toString() + ".tier9Amount", points.get(player.getUniqueId()).getTier9Amount());
            ymlFile.set(player.toString() + ".tier10Amount", points.get(player.getUniqueId()).getTier10Amount());
            ymlFile.set(player.getUniqueId() + ".lastuse", points.get(player.getUniqueId()).getLastUse());
            ymlFile.save(file);
            if (remove) {
                points.remove(player.getUniqueId());
            }
        }
    }

    public void savePoints(UUID player, boolean remove) throws IOException {
        if (!points.isEmpty() && points.containsKey(player)) {
            File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/Player" + name + ".yml");
            ymlFile = YamlConfiguration.loadConfiguration(file);
            ymlFile.set(player.toString() + ".tier1Amount", points.get(player).getTier1Amount());
            ymlFile.set(player.toString() + ".tier2Amount", points.get(player).getTier2Amount());
            ymlFile.set(player.toString() + ".tier3Amount", points.get(player).getTier3Amount());
            ymlFile.set(player.toString() + ".tier4Amount", points.get(player).getTier4Amount());
            ymlFile.set(player.toString() + ".tier5Amount", points.get(player).getTier5Amount());
            ymlFile.set(player.toString() + ".tier6Amount", points.get(player).getTier6Amount());
            ymlFile.set(player.toString() + ".tier7Amount", points.get(player).getTier7Amount());
            ymlFile.set(player.toString() + ".tier8Amount", points.get(player).getTier8Amount());
            ymlFile.set(player.toString() + ".tier9Amount", points.get(player).getTier9Amount());
            ymlFile.set(player.toString() + ".tier10Amount", points.get(player).getTier10Amount());
            ymlFile.set(player.toString() + ".lastuse", points.get(player).getLastUse().toString());
            ymlFile.save(file);
            if (remove) {
                points.remove(player);
            }
        }
    }

    public void addPoints(Player player, int tier, long amount) {
        if (!points.containsKey(player.getUniqueId())) {
            points.put(player.getUniqueId(), new Robot(LocalDateTime.now(), 0, 0, 0, 0, 0, 0, 0, 0, 0, 0));
        }

        if (tier == 1) {
            points.get(player.getUniqueId()).setTier1Amount(points.get(player.getUniqueId()).getTier1Amount() + amount);
        } else if (tier == 2) {
            points.get(player.getUniqueId()).setTier2Amount(points.get(player.getUniqueId()).getTier2Amount() + amount);
        } else if (tier == 3) {
            points.get(player.getUniqueId()).setTier3Amount(points.get(player.getUniqueId()).getTier3Amount() + amount);
        } else if (tier == 4) {
            points.get(player.getUniqueId()).setTier4Amount(points.get(player.getUniqueId()).getTier4Amount() + amount);
        } else if (tier == 5) {
            points.get(player.getUniqueId()).setTier5Amount(points.get(player.getUniqueId()).getTier5Amount() + amount);
        } else if (tier == 6) {
            points.get(player.getUniqueId()).setTier6Amount(points.get(player.getUniqueId()).getTier6Amount() + amount);
        } else if (tier == 7) {
            points.get(player.getUniqueId()).setTier7Amount(points.get(player.getUniqueId()).getTier7Amount() + amount);
        } else if (tier == 8) {
            points.get(player.getUniqueId()).setTier8Amount(points.get(player.getUniqueId()).getTier8Amount() + amount);
        } else if (tier == 9) {
            points.get(player.getUniqueId()).setTier9Amount(points.get(player.getUniqueId()).getTier9Amount() + amount);
        } else if (tier == 10) {
            points.get(player.getUniqueId()).setTier10Amount(points.get(player.getUniqueId()).getTier10Amount() + amount);
        }
    }

    public void removePoints(Player player, int tier, long amount) {
        if (points.containsKey(player.getUniqueId())) {
            if (tier == 1) {
                points.get(player.getUniqueId()).setTier1Amount(points.get(player.getUniqueId()).getTier1Amount() - amount);
            } else if (tier == 2) {
                points.get(player.getUniqueId()).setTier2Amount(points.get(player.getUniqueId()).getTier2Amount() - amount);
            } else if (tier == 3) {
                points.get(player.getUniqueId()).setTier3Amount(points.get(player.getUniqueId()).getTier3Amount() - amount);
            } else if (tier == 4) {
                points.get(player.getUniqueId()).setTier4Amount(points.get(player.getUniqueId()).getTier4Amount() - amount);
            } else if (tier == 5) {
                points.get(player.getUniqueId()).setTier5Amount(points.get(player.getUniqueId()).getTier5Amount() - amount);
            } else if (tier == 6) {
                points.get(player.getUniqueId()).setTier6Amount(points.get(player.getUniqueId()).getTier6Amount() - amount);
            } else if (tier == 7) {
                points.get(player.getUniqueId()).setTier7Amount(points.get(player.getUniqueId()).getTier7Amount() - amount);
            } else if (tier == 8) {
                points.get(player.getUniqueId()).setTier8Amount(points.get(player.getUniqueId()).getTier8Amount() - amount);
            } else if (tier == 9) {
                points.get(player.getUniqueId()).setTier9Amount(points.get(player.getUniqueId()).getTier9Amount() - amount);
            } else if (tier == 10) {
                points.get(player.getUniqueId()).setTier10Amount(points.get(player.getUniqueId()).getTier10Amount() - amount);
            }
        }
    }

    public void sendInfo(Player player) {
        //player.sendMessage(prefix + "You got a total of §a§l" + getPoints(player) + "§7 " + name);
        player.sendMessage("");
        player.sendMessage("§7Command List");
        player.sendMessage("§7  /robots");
        player.sendMessage("");
    }

    public LocalDateTime getLastUse(Player player) {
        if (!points.containsKey(player.getUniqueId())) {
            return LocalDateTime.now();
        } else {
            return points.get(player.getUniqueId()).getLastUse();
        }
    }

    public void updateLastUse(Player player) {
        if (points.containsKey(player.getUniqueId())) {
            points.get(player.getUniqueId()).setLastUse(LocalDateTime.now());
        }
    }

    /*public void withdrawPoints(Player player, long amount) {
        if (getPoints(player) >= amount) {
            if (mainClass.getEmptySlots(player.getInventory()) > 0) {
                removePoints(player, amount);
                ItemStack item = new ItemStack(Material.PAPER);
                ItemMeta meta = item.getItemMeta();
                meta.setDisplayName("§a" + amount + " §7" + name);
                List<String> lore = new ArrayList<>();
                lore.add("§6§l* §eRight click to deposit");
                meta.setLore(lore);
                item.setItemMeta(meta);
                player.getInventory().addItem(item);
                player.sendMessage(prefix + "You withdraw a total of §a§l" + amount + " §7" + name);
            } else {
                player.sendMessage(prefix + "§cYou don't have enough space in your inventory");
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough " + name + " to withdraw");
        }
    }*/

    @Override
    public boolean onCommand(CommandSender sender, Command cmd, String label, String[] args) {
        if (cmd.getName().equalsIgnoreCase("robot") || cmd.getName().equalsIgnoreCase("robot")) {
            if (sender instanceof Player) {
                Player player = (Player) sender;
                if (player.isOp() && args.length == 4 && args[0].equalsIgnoreCase("give")) {
                    if (Bukkit.getPlayerExact(args[1]) != null) {
                        Player tmpPlayer = Bukkit.getPlayerExact(args[1]);
                        int tier = Integer.parseInt(args[2]);
                        long points = Long.parseLong(args[3]);
                        addPoints(tmpPlayer, tier, points);
                        player.sendMessage(prefix + "§a§l" + points + " §7" + name + " tier " + tier + " Added to " + tmpPlayer.getName() + "'s balance");
                        if (!player.getName().equals(tmpPlayer.getName())) {
                            tmpPlayer.sendMessage(prefix + "You received §a§l" + points + " §7tier " + tier + " " + name);
                        }
                    } else {
                        player.sendMessage(prefix + "§cPlayer offline");
                    }
                } /*else if (player.isOp() && args.length == 2 && args[0].equalsIgnoreCase("reset")) {
                    Player tmpPlayer = Bukkit.getPlayerExact(args[1]);
                    removePoints(tmpPlayer, getPoints(tmpPlayer));
                    tmpPlayer.sendMessage(prefix + "Your point balance has been resetted");
                    player.sendMessage(prefix + "You succesfully reset " + tmpPlayer.getName() + "'s point balance");
                } else if (player.isOp() && args.length == 2 && args[0].equalsIgnoreCase("fix")) {
                    Player tmpPlayer = Bukkit.getPlayerExact(args[1]);
                    long tmpRobots = getPoints(tmpPlayer);
                    points.remove(tmpPlayer.getUniqueId());
                    points.put(tmpPlayer.getUniqueId(), new Robot(LocalDateTime.now(), tmpRobots));
                } else if (args.length == 1 && (args[0].equalsIgnoreCase("bal") || args[0].equalsIgnoreCase("balance"))) {
                    sendPoints(player);
                } else if (args.length == 2 && args[0].equalsIgnoreCase("withdraw") && Long.parseLong(args[1]) > 0) {
                    withdrawPoints(player, Long.parseLong(args[1]));
                }*/ else {
                    gui.openGUI(player);
                }
            } else {
                if (args.length == 4 && args[0].equalsIgnoreCase("give")) {
                    if (Bukkit.getPlayerExact(args[1]) != null) {
                        Player tmpPlayer = Bukkit.getPlayerExact(args[1]);
                        int tier = Integer.parseInt(args[2]);
                        long points = Long.parseLong(args[3]);
                        addPoints(tmpPlayer, tier, points);
                        mainClass.getLogger().info(prefix + "§a§l" + points + " §7" + name + " tier " + tier + " Added to " + tmpPlayer.getName() + "'s balance");
                        tmpPlayer.sendMessage(prefix + "You received §a§l" + points + " §7tier " + tier + " " + name);
                    }
                } /*else if (args.length == 2 && args[0].equalsIgnoreCase("reset")) {
                    Player tmpPlayer = Bukkit.getPlayerExact(args[1]);
                    removePoints(tmpPlayer, getPoints(tmpPlayer));
                    tmpPlayer.sendMessage(prefix + "Your point balance has been resetted");
                    mainClass.getLogger().info(prefix + "You succesfully reset " + tmpPlayer.getName() + "'s point balance");
                }*/
            }
        }
        return false;
    }

}
