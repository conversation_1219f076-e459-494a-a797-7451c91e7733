package it.masterzen.CustomNPC;

import com.sk89q.worldedit.BlockVector;
import com.sk89q.worldguard.bukkit.RegionContainer;
import com.sk89q.worldguard.protection.ApplicableRegionSet;
import com.sk89q.worldguard.protection.managers.RegionManager;
import com.sk89q.worldguard.protection.regions.ProtectedRegion;
import it.masterzen.blockbreak.AlphaBlockBreak;
import net.citizensnpcs.api.CitizensAPI;
import net.citizensnpcs.api.ai.Goal;
import net.citizensnpcs.api.ai.event.CancelReason;
import net.citizensnpcs.api.ai.event.NavigationCompleteEvent;
import net.citizensnpcs.api.ai.event.NavigatorCallback;
import net.citizensnpcs.api.ai.goals.MoveToGoal;
import net.citizensnpcs.api.event.NPCRightClickEvent;
import net.citizensnpcs.api.npc.BlockBreaker;
import net.citizensnpcs.api.npc.NPC;
import net.citizensnpcs.api.trait.trait.Equipment;
import net.citizensnpcs.nms.v1_12_R1.util.CitizensBlockBreaker;
import net.citizensnpcs.trait.SkinTrait;
import net.minecraft.server.v1_12_R1.BlockPosition;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.craftbukkit.v1_12_R1.entity.CraftPlayer;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

public class Main implements Listener {

    public final AlphaBlockBreak mainClass;
    private final String prefix = "§e§lNPC §8»§7 ";
    private static HashMap<NPC, UUID> playerNPC = new HashMap<>();
    int maxIterations = 0;
    //boolean canMove = false;
    int counter = 0;

    public Main(AlphaBlockBreak plugin) {
        mainClass = plugin;
    }

    @EventHandler
    public void onRightClick(NPCRightClickEvent event) {
        Player player = event.getClicker();
        NPC npc = event.getNPC();

        npc.destroy();
        playerNPC.remove(npc);
    }

    @EventHandler
    public void onFinishMoving(NavigationCompleteEvent event) {
        NPC npc = event.getNPC();
        nextBlock(npc);
        Bukkit.broadcastMessage("NavigationCompleteEvent");
    }

    /*@EventHandler
    public void onPlayerMove(PlayerMoveEvent event) {
        if (event.getTo().getBlockX() == event.getFrom().getBlockX() && event.getTo().getBlockY() == event.getFrom().getBlockY() && event.getTo().getBlockZ() == event.getFrom().getBlockZ()) {
            //The player hasn't moved
            return;
        }
        Player player = event.getPlayer();

        if (playerNPC.containsKey(player.getUniqueId())) {
            //playerNPC.get(player.getUniqueId()).getNavigator().setTarget(player, false);
            playerNPC.get(player.getUniqueId()).getNavigator().setTarget(player.getLocation().clone().add(-2, 0, 0));
        }
    }*/

    public void moveToFirstBlock(NPC npc) {
        World world = npc.getEntity().getWorld();
        RegionContainer container = mainClass.getWorldGuard().getRegionContainer();
        RegionManager regions = container.get(world);

        assert regions != null;
        ProtectedRegion region = regions.getRegion("d977559d-e8a2-45e2-a01a-f4ce37276ad9");
        BlockVector minPointLocation = region.getMinimumPoint();
        BlockVector maxPointLocation = region.getMaximumPoint();

        Block nextBlock = getFirstBlockMinable(new Location(npc.getEntity().getWorld(), maxPointLocation.getBlockX(), maxPointLocation.getBlockY(), maxPointLocation.getBlockZ()), new Location(npc.getEntity().getWorld(), minPointLocation.getBlockX(), minPointLocation.getBlockY(), minPointLocation.getBlockZ()));
        npc.getNavigator().setTarget(nextBlock.getLocation());
        /*if (npc.getEntity().getLocation().distance(nextBlock.getLocation()) > 4) {
            //npc.getNavigator().getLocalParameters().addSingleUseCallback(new NavigatorCallback() {
            //    @Override
            //    public void onCompletion(CancelReason cancelReason) {
            //        nextBlock(npc);
            //        //Bukkit.broadcastMessage("I'm on a new block !" + counter);
            //    }
            //}).run();

            npc.getNavigator().setTarget(nextBlock.getLocation());
            //Bukkit.broadcastMessage("Moving to New Block..." + counter);
        } else {
            nextBlock(npc);
        }*/
    }

    public BukkitRunnable nextBlockLocation(NPC npc) {
        return new BukkitRunnable() {
            @Override
            public void run() {

                //if (canMove) {
                //    canMove = false;
                    World world = npc.getEntity().getWorld();
                    RegionContainer container = mainClass.getWorldGuard().getRegionContainer();
                    RegionManager regions = container.get(world);

                    assert regions != null;
                    ProtectedRegion region = regions.getRegion("d977559d-e8a2-45e2-a01a-f4ce37276ad9");
                    BlockVector minPointLocation = region.getMinimumPoint();
                    BlockVector maxPointLocation = region.getMaximumPoint();

                    Block nextBlock = getFirstBlockMinable(new Location(npc.getEntity().getWorld(), maxPointLocation.getBlockX(), maxPointLocation.getBlockY(), maxPointLocation.getBlockZ()), new Location(npc.getEntity().getWorld(), minPointLocation.getBlockX(), minPointLocation.getBlockY(), minPointLocation.getBlockZ()));
                    npc.getNavigator().setTarget(nextBlock.getLocation());
                    /*if (npc.getEntity().getLocation().distance(nextBlock.getLocation()) > 4) {
                        //npc.getNavigator().getLocalParameters().addSingleUseCallback(new NavigatorCallback() {
                        //    @Override
                        //    public void onCompletion(CancelReason cancelReason) {
                        //        nextBlock(npc);
                        //        //Bukkit.broadcastMessage("I'm on a new block !");
                        //    }
                        //}).run();

                        //npc.getNavigator().getLocalParameters().distanceMargin(100);
                        npc.getNavigator().setTarget(nextBlock.getLocation());
                        //Bukkit.broadcastMessage("Moving to New Block...");
                    } else {
                        //Bukkit.broadcastMessage("No need to move");
                        nextBlock(npc);
                    }*/
                //}
                //npc.getNavigator().getLocalParameters().addSingleUseCallback(new NavigatorCallback() {
                //    @Override
                //    public void onCompletion(CancelReason cancelReason) {
                //        Bukkit.broadcastMessage("OKE?");
                //        npc.getDefaultGoalController().addGoal(new CitizensBlockBreaker(npc.getEntity(), npc.getStoredLocation().add(3, -1, 0).getBlock(), new BlockBreaker.BlockBreakerConfiguration().callback(nextBlock(npc))), 1);
                //    }
                //});

                //npc.getNavigator().setTarget(npc.getStoredLocation().clone().add(10, 0, 0));

                //npc.getNavigator().setTarget(getFirstBlockMinable(new Location(npc.getEntity().getWorld(), maxPointLocation.getBlockX(), maxPointLocation.getBlockY(), maxPointLocation.getBlockZ()), new Location(npc.getEntity().getWorld(), minPointLocation.getBlockX(), minPointLocation.getBlockY(), minPointLocation.getBlockZ())).getLocation());
                //Bukkit.broadcastMessage("Moved");
                //npc.getDefaultGoalController().cancelCurrentExecution();
                //Bukkit.broadcastMessage(npc.getDefaultGoalController().isExecutingGoal() + "");
                //npc.getDefaultGoalController().addGoal(new MoveToGoal(npc, npc.getStoredLocation().clone().add(10, 0, 0)), 1);
                //npc.getDefaultGoalController().addGoal(new CitizensBlockBreaker(npc.getEntity(), npc.getStoredLocation().clone().add(1, -1, 0).getBlock(), new BlockBreaker.BlockBreakerConfiguration()), 1);
                //npc.getBlockBreaker(npc.getStoredLocation().add(5, 0, 0).getBlock(), new BlockBreaker.BlockBreakerConfiguration().callback(nextBlock(npc)));
                //Bukkit.broadcastMessage(npc.getDefaultGoalController().isExecutingGoal() + "");
                //npc.getDefaultGoalController().addGoal(new CitizensBlockBreaker(npc.getEntity(), npc.getStoredLocation().add(1, -1, 0).getBlock(), new BlockBreaker.BlockBreakerConfiguration()), 1);
                //npc.getDefaultGoalController().setPaused(true);
                //npc.getDefaultGoalController().clear();
                //npc.getDefaultGoalController().addGoal(new CitizensBlockBreaker(npc.getEntity(), npc.getStoredLocation().add(1, 0, 0).getBlock(), new BlockBreaker.BlockBreakerConfiguration()), 1);
                //npc.getDefaultGoalController().run();
                //npc.getDefaultGoalController().setPaused(false);
                //npc.getDefaultGoalController().clear();
                //npc.getDefaultGoalController().run();
            }
        };
    }

    public Block getNextBlock(NPC npc) {
        Block block = null;

        Location loc = npc.getEntity().getLocation();
        World world = npc.getEntity().getWorld();
        boolean founded = false;

        for(int x = loc.getBlockX() - 2; x < loc.getBlockX() + 2; x++ ) {
            for(int y = loc.getBlockY() - 2; y < loc.getBlockY() + 2; y++ ) {
                for (int z = loc.getBlockZ() - 2; z < loc.getBlockZ() + 2; z++) {
                    if (!founded) {
                        block = world.getBlockAt(x, y, z);
                        founded = true;
                    }
                }
            }
        }

        return block;
    }

    public Block getFirstBlockMinable(Location pos1, Location pos2) {
        if (pos1.getWorld() != pos2.getWorld())
            return null;

        World world = pos1.getWorld();
        //List<Block> blocks = new ArrayList<>();
        Block block = null;
        int x1 = pos1.getBlockX();
        int y1 = pos1.getBlockY();
        int z1 = pos1.getBlockZ();

        int x2 = pos2.getBlockX();
        int y2 = pos2.getBlockY();
        int z2 = pos2.getBlockZ();

        int lowestX = Math.min(x1, x2);
        int lowestY = Math.min(y1, y2);
        int lowestZ = Math.min(z1, z2);

        int highestX = lowestX == x1 ? x2 : x1;
        int highestY = lowestX == y1 ? y2 : y1;
        int highestZ = lowestX == z1 ? z2 : z1;

        for (int x = lowestX; x <= highestX; x++) {
            for (int y = lowestY; y <= highestY; y++) {
                for (int z = lowestZ; z <= highestZ; z++) {
                    block = world.getBlockAt(x, y, z);
                    if (block != null && !block.getType().equals(Material.AIR)) {
                        //Bukkit.broadcastMessage("New Block Location: " + block.getLocation());
                        return block;
                    }
                }
            }
        }
        //blocks.add(world.getBlockAt(x, y, z));

        return block;
    }

    public void nextBlock(NPC npc) {
        //Bukkit.broadcastMessage("Breaking New Block... "+ counter);
        counter++;

        World world = npc.getEntity().getWorld();
        RegionContainer container = mainClass.getWorldGuard().getRegionContainer();
        RegionManager regions = container.get(world);

        assert regions != null;
        ProtectedRegion region = regions.getRegion("d977559d-e8a2-45e2-a01a-f4ce37276ad9");
        BlockVector minPointLocation = region.getMinimumPoint();
        BlockVector maxPointLocation = region.getMaximumPoint();

        Block block = getFirstBlockMinable(new Location(npc.getEntity().getWorld(), maxPointLocation.getBlockX(), maxPointLocation.getBlockY(), maxPointLocation.getBlockZ()), new Location(npc.getEntity().getWorld(), minPointLocation.getBlockX(), minPointLocation.getBlockY(), minPointLocation.getBlockZ()));
        //npc.getDefaultGoalController().clear();
        npc.getDefaultGoalController().addGoal(new CitizensBlockBreaker(npc.getEntity(), block, new BlockBreaker.BlockBreakerConfiguration().radius(4).callback(nextBlockLocation(npc))), 1);
        //npc.getBlockBreaker(block, new BlockBreaker.BlockBreakerConfiguration()).run();
        //npc.getDefaultGoalController().run();
        //if (Bukkit.getPlayer(playerNPC.get(npc)) != null) {
        //    Player player = Bukkit.getPlayer(playerNPC.get(npc));
        //    ((CraftPlayer) player).getHandle().playerInteractManager.breakBlock(new BlockPosition(block.getX(), block.getY(), block.getZ()));
        //}
        //((CraftPlayer) player).getHandle().playerInteractManager.breakBlock(new BlockPosition(block.getX(), block.getY(), block.getZ()));
        //canMove = true;

        //if (!npc.getDefaultGoalController().isExecutingGoal()) {
        //    //npc.getDefaultGoalController().clear();
        //    List<CitizensBlockBreaker> blockBreakerList = new ArrayList<>();
//
        //    Location loc = npc.getEntity().getLocation();
        //    World world = npc.getEntity().getWorld();
//
        //    int x = 0;
        //    int y = 0;
        //    int z = 0;
//
        //    int priority = 1;
        //    /*for (x = loc.getBlockX() - 2; x < loc.getBlockX() + 2; x++ ) {
        //        for (y = loc.getBlockY() - 2; y < loc.getBlockY() + 2; y++ ) {
        //            for (z = loc.getBlockZ() - 2; z < loc.getBlockZ() + 2; z++) {
        //                blockBreakerList.add(new CitizensBlockBreaker(npc.getEntity(), world.getBlockAt(x, y, z), new BlockBreaker.BlockBreakerConfiguration()));
        //            }
        //        }
        //    }*/
//
        //    //blockBreakerList.remove(blockBreakerList.get(blockBreakerList.size() - 1));
        //    ////blockBreakerList.add(new CitizensBlockBreaker(npc.getEntity(), world.getBlockAt(x, y, z), new BlockBreaker.BlockBreakerConfiguration().callback(nextBlockLocation(npc))));
        //    //npc.getDefaultGoalController().setPaused(true);
        //    //for (CitizensBlockBreaker goal : blockBreakerList) {
        //    //    npc.getDefaultGoalController().addGoal(goal, priority);
        //    //    priority++;
        //    //}
        //    //priority++;
        //    //BlockVector minPointLocation = region.getMinimumPoint();
        //    //BlockVector maxPointLocation = region.getMaximumPoint();
        //    RegionContainer container = mainClass.getWorldGuard().getRegionContainer();
        //    RegionManager regions = container.get(world);
//
        //    assert regions != null;
        //    ProtectedRegion region = regions.getRegion("d977559d-e8a2-45e2-a01a-f4ce37276ad9");
        //    BlockVector minPointLocation = region.getMinimumPoint();
        //    BlockVector maxPointLocation = region.getMaximumPoint();
//
        //    Block block = getFirstBlockMinable(new Location(npc.getEntity().getWorld(), maxPointLocation.getBlockX(), maxPointLocation.getBlockY(), maxPointLocation.getBlockZ()), new Location(npc.getEntity().getWorld(), minPointLocation.getBlockX(), minPointLocation.getBlockY(), minPointLocation.getBlockZ()));
        //    Bukkit.broadcastMessage(block.getLocation() + "");
        //    npc.getDefaultGoalController().addGoal(new CitizensBlockBreaker(npc.getEntity(), block, new BlockBreaker.BlockBreakerConfiguration().callback(nextBlockLocation(npc))), priority);
        //    npc.getDefaultGoalController().setPaused(false);
//
        //    //npc.getDefaultGoalController().addGoal(new CitizensBlockBreaker(npc.getEntity(), npc.getEntity().getLocation().add(2, -1, 0).getBlock(), new BlockBreaker.BlockBreakerConfiguration().callback(nextBlockLocation(npc))), 1);
        //} /*else {
        //    npc.getDefaultGoalController().run();
        //}*/
        //Bukkit.broadcastMessage("I'm working? " + npc.getDefaultGoalController().isExecutingGoal());
        //canMove = true;
    }

    public void createNPC(Player player, String npcName, Location location) {
        NPC npc = CitizensAPI.getNPCRegistry().createNPC(EntityType.PLAYER, mainClass.getEssentials().getUser(player).getNickname() + "§7's Robot");
        counter = 0;

        /*npc.data().set(NPC.PLAYER_SKIN_UUID_METADATA, "d977559d-e8a2-45e2-a01a-f4ce37276ad9");
        npc.spawn(location.add(0, 1, 0));
        ((SkinnableEntity) npc.getEntity()).setSkinName(Bukkit.getOfflinePlayer(UUID.fromString("d977559d-e8a2-45e2-a01a-f4ce37276ad9")).getName());
        npc.despawn(DespawnReason.PENDING_RESPAWN);
        npc.setName(essentials.getUser(player).getNickname() + "§7's Robot");
        npc.spawn(location.add(location.getX() > 0 ? 0.5 : -0.5, 1.0, location.getZ() > 0 ? -0.5 : 0.5));*/

        //npc.data().setPersistent(NPC.NAMEPLATE_VISIBLE_METADATA, false);
        //npc.data().setPersistent(NPC.FLYABLE_METADATA, true);
        SkinTrait skinTrait = npc.getTrait(SkinTrait.class);
        skinTrait.setSkinName(player.getName());
        //npc.data().set(NPC.PLAYER_SKIN_UUID_METADATA, "d977559d-e8a2-45e2-a01a-f4ce37276ad9");
        //npc.setBukkitEntityType(EntityType.ENDERMAN);
        npc.setName(mainClass.getEssentials().getUser(player).getNickname() + "§7's Robot");
        //npc.setUseMinecraftAI(true);
        npc.setFlyable(true);
        npc.getOrAddTrait(Equipment.class).set(Equipment.EquipmentSlot.HAND, player.getInventory().getItemInMainHand());
        //npc.data().set(NPC.PLAYER_SKIN_UUID_METADATA, essentials.getUser(player).getName());
        //Location tmpLoc = location.add(location.getX() > 0 ? 0.5 : -0.5, 1.0, location.getZ() > 0 ? -0.5 : 0.5);
        //Location tmpLoc = location.add(0.5, 1.0,0.5);
        //npc.spawn(player.getLocation());
        npc.getNavigator().getDefaultParameters().distanceMargin(1);
        npc.spawn(mainClass.getMineSystem().getMineList().get(player.getUniqueId()).getOriginalHomeLocation());
        //npc.getNavigator().getLocalParameters().distanceMargin(999);
        //npc.getNavigator().getLocalParameters().pathDistanceMargin(999);
        //npc.getNavigator().getDefaultParameters().useNewPathfinder(false);
        //npc.getNavigator().setTarget(player, true);
        //npc.getBlockBreaker(player.getLocation().add(0, -1, 0).getBlock(), new BlockBreaker.BlockBreakerConfiguration());
        //Goal goal = new CitizensBlockBreaker(npc.getEntity(), player.getLocation().add(0, -1, 0).getBlock(), new BlockBreaker.BlockBreakerConfiguration());
        //npc.getDefaultGoalController().addGoal(new CitizensBlockBreaker(npc.getEntity(), npc.getStoredLocation().clone().add(-5, -1, 0).getBlock(), new BlockBreaker.BlockBreakerConfiguration().callback(nextBlockLocation(npc))), 1);
        //npc.getNavigator().setTarget(getNextBlock(npc).getLocation());
        //npc.getNavigator().getLocalParameters().addSingleUseCallback(new NavigatorCallback() {
        //    @Override
        //    public void onCompletion(CancelReason cancelReason) {
        //        nextBlock(npc);
        //    }
        //});
        //npc.getBlockBreaker(npc.getStoredLocation().add(1, 0, 0).getBlock(), new BlockBreaker.BlockBreakerConfiguration().callback(nextBlock(npc)));
        //npc.getNavigator().setTarget(player, true);
        //npc.getNavigator().getLocalParameters().addRunCallback(nextBlock2(npc));
        if (!npc.getDefaultGoalController().isExecutingGoal()) {
            Bukkit.broadcastMessage("Starting !");
            moveToFirstBlock(npc);
            //npc.getDefaultGoalController().run();
        }

        playerNPC.put(npc, player.getUniqueId());
    }
}
