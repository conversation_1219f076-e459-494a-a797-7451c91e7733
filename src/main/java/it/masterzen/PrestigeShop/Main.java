package it.masterzen.PrestigeShop;

import it.masterzen.blockbreak.AlphaBlockBreak;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.block.Chest;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.UUID;

public class Main implements CommandExecutor, Listener {

    private static HashMap<UUID, Long> prestigePoints = new HashMap<>();
    private final String prefix = "§e§lPRESTIGE POINTS §8»§7 ";
    private static YamlConfiguration ymlFile;

    private it.masterzen.PrestigeShop.GUI gui;
    public final AlphaBlockBreak mainClass;

    public Main(AlphaBlockBreak plugin) {
        mainClass = plugin;
        gui = new GUI(this);
    }

    public void loadPlayerPrestigePoints() {
        //File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/PlayerPrestigePoints.yml");
        //ymlFile = YamlConfiguration.loadConfiguration(file);

        for (Player player : Bukkit.getOnlinePlayers()) {
            loadPlayerPrestigePoints(player);
        }
    }

    public void loadPlayerPrestigePoints(Player player) {
        File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/PlayerPrestigePoints.yml");
        ymlFile = YamlConfiguration.loadConfiguration(file);

        if (!prestigePoints.containsKey(player.getUniqueId())) {
            prestigePoints.put(player.getUniqueId(), ymlFile.getLong(player.getUniqueId() + ".points"));
        }
    }

    public HashMap<UUID, Long> getPrestigePoints() {
        return prestigePoints;
    }

    public void savePlayerPrestigePoints(Player player) throws IOException {
        File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/PlayerPrestigePoints.yml");
        ymlFile = YamlConfiguration.loadConfiguration(file);
        ymlFile.set(player.getUniqueId() + ".points", prestigePoints.get(player.getUniqueId()));
        ymlFile.save(file);
        prestigePoints.remove(player.getUniqueId());
    }

    public void addPrestigePoints(Player player, long amount) {
        if (!prestigePoints.containsKey(player.getUniqueId())) {
            prestigePoints.put(player.getUniqueId(), amount);
        } else {
            prestigePoints.replace(player.getUniqueId(), prestigePoints.get(player.getUniqueId()) + amount);
        }
        if (!player.hasPermission("prestigepointfinder.remove")) {
            player.sendMessage(prefix + "You received §a§l" + amount + " §7Prestige Points");
        }
    }

    public void removePrestigePoints(Player player, long amount) {
        if (prestigePoints.containsKey(player.getUniqueId())) {
            prestigePoints.replace(player.getUniqueId(), prestigePoints.get(player.getUniqueId()) - amount);
        }
    }

    public void sendPrestigePoints(Player player) {
        if (!prestigePoints.containsKey(player.getUniqueId())) {
            player.sendMessage(prefix + "You got §a§l0 §7Prestige Points");
        } else {
            player.sendMessage(prefix + "You got §a§l" + prestigePoints.get(player.getUniqueId()) + " §7Prestige Points");
        }
    }

    public long getPrestigePoints(Player player) {
        if (!prestigePoints.containsKey(player.getUniqueId())) {
            return 0;
        } else {
            return prestigePoints.get(player.getUniqueId());
        }
    }

    @Override
    public boolean onCommand(CommandSender sender, Command cmd, String label, String[] args) {
        if (cmd.getName().equalsIgnoreCase("prestigeshop") || cmd.getName().equalsIgnoreCase("pshop")) {
            if (sender instanceof Player) {
                Player player = (Player) sender;
                if (args.length == 3 && player.isOp()) {
                    addPrestigePoints(Bukkit.getPlayerExact(args[1]), Integer.parseInt(args[2]));
                    player.sendMessage(prefix + "§a§l" + Integer.parseInt(args[2]) + "§7 PrestigePoints added to " + Bukkit.getPlayerExact(args[1]).getName() + "§7's balance");
                }
                gui.openGUI(player);
            } else {
                addPrestigePoints(Bukkit.getPlayerExact(args[1]), Integer.parseInt(args[2]));
            }
        }
        return false;
    }
}
