package it.masterzen.CandyFinder;

import com.sk89q.worldedit.MaxChangedBlocksException;
import com.sun.javafx.robot.impl.FXRobotHelper;
import it.masterzen.MongoDB.PlayerData;
import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.blockbreak.Enums;
import it.masterzen.blockbreak.XMaterial;
import org.apache.commons.lang3.EnumUtils;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class GUI implements Listener {

    private it.masterzen.CandyFinder.Main main;
    private it.masterzen.Keys.Main keyManager;

    private final String prefix = "§e§lCANDYS §8»§7 ";

    public GUI(it.masterzen.CandyFinder.Main main) {
        this.main = main;
        keyManager = main.mainClass.getKeysManager();
        //withdrawManager = new it.masterzen.Withdraw.Main(main.mainClass);
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) throws IOException, MaxChangedBlocksException {
        if (event.getClickedInventory() != null && event.getClickedInventory().equals(event.getView().getTopInventory())) {
            Player player = (Player) event.getWhoClicked();

            if (event.getView().getTitle().equalsIgnoreCase("§e§lCANDY §f| §7Shop")) {
                event.setCancelled(true);
                if (event.getSlot() == 12) {
                    if (event.isLeftClick()) {
                        giveMoney(player, false);
                    } else if (event.isRightClick()) {
                        giveMoney(player, true);
                    }
                } else if (event.getSlot() == 14) {
                    if (event.isLeftClick()) {
                        giveTokens(player, false);
                    } else if (event.isRightClick()) {
                        giveTokens(player, true);
                    }
                }
                openGUI(player);
            }
        }
    }

    public void giveTokens(Player player, boolean max) {
        if (max) {
            if (main.getCandy(player) >= 1000) {
                int times = (int) Math.floor(main.getCandy(player) / 1000);

                /*double totalAmount = 0;

                double playerPickValue = main.mainClass.getPickValue(player);
                totalAmount = playerPickValue * 0.05 * times;

                String playerClassString = main.mainClass.getMongoReader().getPlayerData(player.getUniqueId()).getClassCode();
                if (EnumUtils.isValidEnum(Enums.Classes.class, playerClassString)) {
                    Enums.Classes playerClass = Enums.Classes.valueOf(playerClassString);
                    if (playerClass == Enums.Classes.MINER) {
                        totalAmount = totalAmount * playerClass.getBooster();
                    }
                }

                main.mainClass.getTeAPI().addTokens(player, totalAmount);*/

                double totalBoosters = times * 2.5;
                PlayerData data = main.mainClass.getMongoReader().getPlayerData(player.getUniqueId());
                data.setKeysTokenBooster((data.getKeysTokenBooster() == null ? 0 : data.getKeysTokenBooster()) + totalBoosters);
                main.mainClass.getMongoReader().setPlayerTokenBoosterInMap(player, data.getKeysTokenBooster());

                main.removeCandy(player, (times * 1000L));
                player.sendMessage(prefix + "You received a total of §a§l" + totalBoosters + "%§7 Token Booster §7§o(" + times + "x)");
            } else {
                player.sendMessage(prefix + "§cYou don't have enough Candys");
            }
        } else {
            if (main.getCandy(player) >= 1000) {
                /*double totalAmount = 0;

                double playerPickValue = main.mainClass.getPickValue(player);
                totalAmount = playerPickValue * 0.05;

                String playerClassString = main.mainClass.getMongoReader().getPlayerData(player.getUniqueId()).getClassCode();
                if (EnumUtils.isValidEnum(Enums.Classes.class, playerClassString)) {
                    Enums.Classes playerClass = Enums.Classes.valueOf(playerClassString);
                    if (playerClass == Enums.Classes.MINER) {
                        totalAmount = totalAmount * playerClass.getBooster();
                    }
                }

                main.mainClass.getTeAPI().addTokens(player, totalAmount);*/

                double totalBoosters = 2.5;
                PlayerData data = main.mainClass.getMongoReader().getPlayerData(player.getUniqueId());
                data.setKeysTokenBooster((data.getKeysTokenBooster() == null ? 0 : data.getKeysTokenBooster()) + totalBoosters);
                main.mainClass.getMongoReader().setPlayerTokenBoosterInMap(player, data.getKeysTokenBooster());

                main.removeCandy(player, 1000L);
                player.sendMessage(prefix + "You received a total of §a§l" + totalBoosters + "%§7 Token Booster");
            } else {
                player.sendMessage(prefix + "§cYou don't have enough Candys");
            }
        }
    }

    public void giveMoney(Player player, boolean max) {
        if (max) {
            if (main.getCandy(player) >= 1000) {
                int times = (int) Math.floor(main.getCandy(player) / 1000);

                /*double totalAmount = 0;

                double playerMoney = main.mainClass.getEconomy().getBalance(player);
                totalAmount = playerMoney * 0.05 * times;

                String playerClassString = main.mainClass.getMongoReader().getPlayerData(player.getUniqueId()).getClassCode();
                if (EnumUtils.isValidEnum(Enums.Classes.class, playerClassString)) {
                    Enums.Classes playerClass = Enums.Classes.valueOf(playerClassString);
                    if (playerClass == Enums.Classes.MERCHANT) {
                        totalAmount = totalAmount * playerClass.getBooster();
                    }
                }

                main.mainClass.getEconomy().depositPlayer(player, totalAmount);*/
                double totalBoosters = times * 5;
                PlayerData data = main.mainClass.getMongoReader().getPlayerData(player.getUniqueId());
                data.setKeysMoneyBooster((data.getKeysMoneyBooster() == null ? 0 : data.getKeysMoneyBooster()) + totalBoosters);
                main.mainClass.getMongoReader().setPlayerMoneyBoosterInMap(player, data.getKeysMoneyBooster());

                main.removeCandy(player, (times * 1000L));
                player.sendMessage(prefix + "You received a total of §6§l" + totalBoosters + "%§7 Money Booster §7§o(" + times + "x)");
            } else {
                player.sendMessage(prefix + "§cYou don't have enough Candys");
            }
        } else {
            if (main.getCandy(player) >= 1000) {
                /*double totalAmount = 0;

                double playerMoney = main.mainClass.getEconomy().getBalance(player);
                totalAmount = playerMoney * 0.05;

                String playerClassString = main.mainClass.getMongoReader().getPlayerData(player.getUniqueId()).getClassCode();
                if (EnumUtils.isValidEnum(Enums.Classes.class, playerClassString)) {
                    Enums.Classes playerClass = Enums.Classes.valueOf(playerClassString);
                    if (playerClass == Enums.Classes.MERCHANT) {
                        totalAmount = totalAmount * playerClass.getBooster();
                    }
                }

                main.mainClass.getEconomy().depositPlayer(player, totalAmount);*/

                double totalBoosters = 5;
                PlayerData data = main.mainClass.getMongoReader().getPlayerData(player.getUniqueId());
                data.setKeysMoneyBooster((data.getKeysMoneyBooster() == null ? 0 : data.getKeysMoneyBooster()) + totalBoosters);
                main.mainClass.getMongoReader().setPlayerMoneyBoosterInMap(player, data.getKeysMoneyBooster());

                main.removeCandy(player, 1000L);
                player.sendMessage(prefix + "You received a total of §6§l" + totalBoosters + "%§7 Money Booster");
            } else {
                player.sendMessage(prefix + "§cYou don't have enough Candys");
            }
        }
    }

    public void openGUI(Player player) {
        Inventory gui = Bukkit.createInventory(null, 27, "§e§lCANDY §f| §7Shop");
        it.masterzen.commands.Main.FillBorder(gui);

        /*ItemStack tokens = new ItemStack(Material.MAGMA_CREAM);
        ItemMeta meta = tokens.getItemMeta();
        meta.setDisplayName("§a5% Pick Value §a§lTokens");
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e5.000 §fCandy");
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fYou will get a total of 5% of your");
        lore.add("§7| §fpick's value as a reward");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft click: 1x");
        lore.add("§6| §fRight click: Max Use");
        lore.add("");
        meta.setLore(lore);
        tokens.setItemMeta(meta);

        ItemStack money = new ItemStack(Material.PAPER);
        meta = money.getItemMeta();
        meta.setDisplayName("§65% §lMoney §6Balance");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e5.000 §fCandy");
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fYou will get a total of 5% of your");
        lore.add("§7| §fmoney balance");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft click: 1x");
        lore.add("§6| §fRight click: Max Use");
        lore.add("");
        meta.setLore(lore);
        money.setItemMeta(meta);*/

        ItemStack moneyBoost = new ItemStack(Material.GOLD_INGOT);
        ItemMeta meta = moneyBoost.getItemMeta();
        meta.setDisplayName("§e§lMONEY §7Booster");
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e1.000 §fCandy");
        lore.add("");
        lore.add("§7§lREWARD");
        lore.add("§7| §f5% Money §7booster");
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §7This booster stacks with the keys booster (/boosters).");
        lore.add("");
        meta.setLore(lore);
        moneyBoost.setItemMeta(meta);

        ItemStack tokenBoost = new ItemStack(Material.MAGMA_CREAM);
        meta = tokenBoost.getItemMeta();
        meta.setDisplayName("§a§lTOKEN §7Booster");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e1.000 §fCandy");
        lore.add("");
        lore.add("§7§lREWARD");
        lore.add("§7| §f2.5% Token §7booster");
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §7This booster stacks with the keys booster (/boosters).");
        lore.add("");
        meta.setLore(lore);
        tokenBoost.setItemMeta(meta);

        gui.setItem(12, moneyBoost);
        gui.setItem(14, tokenBoost);

        player.openInventory(gui);
    }
}
