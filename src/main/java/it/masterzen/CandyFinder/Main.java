package it.masterzen.CandyFinder;

import com.songoda.skyblock.api.SkyBlockAPI;
import com.songoda.skyblock.api.island.Island;
import com.songoda.skyblock.api.island.IslandRole;
import it.masterzen.blockbreak.AlphaBlockBreak;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.block.Chest;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.InvalidConfigurationException;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.UUID;

public class Main implements CommandExecutor, Listener {

    private static HashMap<UUID, Long> candys = new HashMap<>();
    private final String prefix = "§e§lCANDYS §8»§7 ";
    private static YamlConfiguration ymlFile;

    private it.masterzen.CandyFinder.GUI gui;
    public final AlphaBlockBreak mainClass;

    public Main(AlphaBlockBreak plugin) {
        mainClass = plugin;
        gui = new GUI(this);
    }

    public void loadPlayerCandys() {
        for (Player player : Bukkit.getOnlinePlayers()) {
            loadPlayerCandy(player);
        }
    }

    public void loadPlayerCandy(Player player) {
        File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/PlayerCandys.yml");
        ymlFile = YamlConfiguration.loadConfiguration(file);

        if (!candys.containsKey(player.getUniqueId())) {
            candys.put(player.getUniqueId(), ymlFile.getLong(player.getUniqueId() + ".points"));
        }
    }

    public HashMap<UUID, Long> getCandys() {
        return candys;
    }

    public void savePlayersCandy() throws IOException {
        File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/PlayerCandys.yml");
        ymlFile = YamlConfiguration.loadConfiguration(file);

        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (Exception ex) {
            }
        }

        if (!candys.isEmpty() && ymlFile != null) {
            for (UUID player : candys.keySet()) {
                savePlayerCandy(player, false);
            }
        }
    }

    public void savePlayerCandy(Player player, boolean remove) throws IOException {
        File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/PlayerCandys.yml");
        ymlFile = YamlConfiguration.loadConfiguration(file);
        ymlFile.set(player.getUniqueId() + ".points", candys.get(player.getUniqueId()));
        ymlFile.save(file);
        if (remove) {
            candys.remove(player.getUniqueId());
        }
    }

    public void savePlayerCandy(UUID player, boolean remove) throws IOException {
        File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/PlayerCandys.yml");
        ymlFile = YamlConfiguration.loadConfiguration(file);
        ymlFile.set(player.toString() + ".points", candys.get(player));
        ymlFile.save(file);
        if (remove) {
            candys.remove(player);
        }
    }

    public void addCandy(Player player, long amount, boolean message) {
        if (!candys.containsKey(player.getUniqueId())) {
            candys.put(player.getUniqueId(), amount);
        } else {
            candys.replace(player.getUniqueId(), candys.get(player.getUniqueId()) + amount);
        }

        if (message) {
            player.sendMessage(prefix + "You received §a" + amount + " §7candy(s)");
        }
    }

    public void removeCandy(Player player, long amount) {
        if (candys.containsKey(player.getUniqueId())) {
            candys.replace(player.getUniqueId(), candys.get(player.getUniqueId()) - amount);
        }
    }

    public void sendMobCoins(Player player) {
        if (!candys.containsKey(player.getUniqueId())) {
            player.sendMessage(prefix + "You got §a§l0 §7Candys");
        } else {
            player.sendMessage(prefix + "You got §a§l" + candys.get(player.getUniqueId()) + " §7Candys");
        }
    }

    public long getCandy(Player player) {
        if (!candys.containsKey(player.getUniqueId())) {
            return 0;
        } else {
            return candys.get(player.getUniqueId());
        }
    }

    public void sendInfo(Player player) {
        player.sendMessage(prefix + "You got a total of §a§l" + getCandy(player) + "§7 Candy/s");
        player.sendMessage("");
        player.sendMessage("§7Command List");
        player.sendMessage("§7  /candy balance");
        player.sendMessage("§7  /candy shop");
        player.sendMessage("");
    }

    @Override
    public boolean onCommand(CommandSender sender, Command cmd, String label, String[] args) {
        if (cmd.getName().equalsIgnoreCase("candy") || cmd.getName().equalsIgnoreCase("candys")) {
            if (sender instanceof Player) {
                Player player = (Player) sender;
                if (args.length == 3 && player.isOp()) {
                    addCandy(Bukkit.getPlayerExact(args[1]), Integer.parseInt(args[2]), true);
                    player.sendMessage(prefix + "§a§l" + Integer.parseInt(args[2]) + "§7 Candy added to " + Bukkit.getPlayerExact(args[1]).getName() + "§7's balance");
                } else if (args.length == 1 && args[0].equalsIgnoreCase("Shop")) {
                    gui.openGUI(player);
                } else if (args.length == 1 && (args[0].equalsIgnoreCase("balance") || args[0].equalsIgnoreCase("bal"))) {
                    player.sendMessage(prefix + "You got a total of §a§l" + getCandy(player) + "§7 Candy/s");
                } else {
                    sendInfo(player);
                }
            } else {
                addCandy(Bukkit.getPlayerExact(args[1]), Integer.parseInt(args[2]), true);
            }
        }
        return false;
    }
}
