package it.masterzen.BeaconPoint;

import com.earth2me.essentials.commands.WarpNotFoundException;
import it.masterzen.MobCoin.GUI;
import it.masterzen.Robot.Robot;
import it.masterzen.blockbreak.AlphaBlockBreak;
import net.ess3.api.InvalidWorldException;
import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.Listener;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;

public class Main implements CommandExecutor, Listener {

    private static HashMap<UUID, Long> points = new HashMap<>();
    private final String prefix = "§e§lBEACON POINT §8»§7 ";
    private final String name = "BeaconPoint";
    private static YamlConfiguration ymlFile;

    public final AlphaBlockBreak mainClass;

    public Main(AlphaBlockBreak plugin) {
        mainClass = plugin;
    }

    public String getPrefix() {
        return this.prefix;
    }

    public String getName() {
        return this.name;
    }

    public void loadPoints() {
        for (Player player : Bukkit.getOnlinePlayers()) {
            loadPoints(player);
        }
    }

    public void loadPoints(Player player) {
        File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/Player" + name + ".yml");
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (Exception ex) {
            }
        }

        ymlFile = YamlConfiguration.loadConfiguration(file);
        if (!points.containsKey(player.getUniqueId()) && ymlFile.contains(player.getUniqueId() + ".points")) {
            points.put(player.getUniqueId(), ymlFile.getLong(player.getUniqueId() + ".points"));
        }
    }

    public HashMap<UUID, Long> getPoints() {
        return points;
    }

    public void savePoints() throws IOException {
        File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/Player" + name + ".yml");
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (Exception ex) {
            }
        }

        ymlFile = YamlConfiguration.loadConfiguration(file);
        if (!points.isEmpty() && ymlFile != null) {
            for (UUID player : points.keySet()) {
                savePoints(player, false);
            }
        }
    }

    public void savePoints(Player player, boolean remove) throws IOException {
        if (!points.isEmpty() && points.containsKey(player.getUniqueId())) {
            File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/Player" + name + ".yml");
            ymlFile = YamlConfiguration.loadConfiguration(file);
            ymlFile.set(player.getUniqueId() + ".points", points.get(player.getUniqueId()));
            ymlFile.save(file);
            if (remove) {
                points.remove(player.getUniqueId());
            }
        }
    }

    public void savePoints(UUID player, boolean remove) throws IOException {
        if (!points.isEmpty() && points.containsKey(player)) {
            File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/Player" + name + ".yml");
            ymlFile = YamlConfiguration.loadConfiguration(file);
            ymlFile.set(player.toString() + ".points", points.get(player));
            ymlFile.save(file);
            if (remove) {
                points.remove(player);
            }
        }
    }

    public void addPoints(Player player, long amount) {
        if (!points.containsKey(player.getUniqueId())) {
            points.put(player.getUniqueId(), amount);
        } else {
            points.replace(player.getUniqueId(), points.get(player.getUniqueId()) + amount);
        }
    }

    public void giveRandomPoints(Player player, int max) {
        addPoints(player, ThreadLocalRandom.current().nextInt(max) + 1);
    }

    public int getRandomPoints(Player player, int max) {
        return ThreadLocalRandom.current().nextInt(max) + 1;
    }

    public void removePoints(Player player, long amount) {
        if (points.containsKey(player.getUniqueId())) {
            points.replace(player.getUniqueId(), points.get(player.getUniqueId()) - amount);
        }
    }

    public void setPoints(Player player, long amount) {
        points.remove(player.getUniqueId());
        points.put(player.getUniqueId(), amount);
    }

    public void sendPoints(Player player) {
        if (!points.containsKey(player.getUniqueId())) {
            player.sendMessage(prefix + "You got §a§l0 §7" + name);
        } else {
            player.sendMessage(prefix + "You got §a§l" + points.get(player.getUniqueId()) + " §7" + name);
        }
    }

    public long getPoints(Player player) {
        if (!points.containsKey(player.getUniqueId())) {
            return 0;
        } else {
            return points.get(player.getUniqueId());
        }
    }

    public void sendInfo(Player player) {
        player.sendMessage(prefix + "You got a total of §a§l" + getPoints(player) + "§7 " + name);
        player.sendMessage("");
        player.sendMessage("§7Command List");
        player.sendMessage("§7  /armor");
        player.sendMessage("");
    }

    @Override
    public boolean onCommand(CommandSender sender, Command cmd, String label, String[] args) {
        if (cmd.getName().equalsIgnoreCase("beaconpoints")) {
            if (sender instanceof Player) {
                Player player = (Player) sender;
                if (player.isOp() && args.length == 3 && args[0].equalsIgnoreCase("give")) {
                    if (Bukkit.getPlayerExact(args[1]) != null) {
                        Player tmpPlayer = Bukkit.getPlayerExact(args[1]);
                        long points = Long.parseLong(args[2]);
                        addPoints(tmpPlayer, points);
                        player.sendMessage(prefix + "§a§l" + points + " §7" + name + " §7Added to " + tmpPlayer.getName() + "'s balance §7§o(" + getPoints(tmpPlayer) + ")");
                        if (!player.getName().equals(tmpPlayer.getName())) {
                            tmpPlayer.sendMessage(prefix + "You received §a§l" + points + " §7" + name);
                        }
                    } else {
                        player.sendMessage(prefix + "§cPlayer offline");
                    }
                } else if (player.isOp() && args.length == 2 && args[0].equalsIgnoreCase("reset")) {
                    Player tmpPlayer = Bukkit.getPlayerExact(args[1]);
                    removePoints(tmpPlayer, getPoints(tmpPlayer));
                    tmpPlayer.sendMessage(prefix + "Your point balance has been resetted");
                    player.sendMessage(prefix + "You succesfully reset " + tmpPlayer.getName() + "'s point balance");
                } else if (args.length == 1 && (args[0].equalsIgnoreCase("bal") || args[0].equalsIgnoreCase("balance"))) {
                    sendPoints(player);
                }
            } else {
                if (args.length == 3 && args[0].equalsIgnoreCase("give")) {
                    if (Bukkit.getPlayerExact(args[1]) != null) {
                        Player tmpPlayer = Bukkit.getPlayerExact(args[1]);
                        long points = Long.parseLong(args[2]);
                        addPoints(tmpPlayer, points);
                        mainClass.getLogger().info(prefix + "§a§l" + points + " §7" + name + " §7Added to " + tmpPlayer.getName() + "'s balance §7§o(" + getPoints(tmpPlayer) + ")");
                        tmpPlayer.sendMessage(prefix + "You received §a§l" + points + " §7" + name);
                    }
                } else if (args.length == 2 && args[0].equalsIgnoreCase("reset")) {
                    Player tmpPlayer = Bukkit.getPlayerExact(args[1]);
                    removePoints(tmpPlayer, getPoints(tmpPlayer));
                    tmpPlayer.sendMessage(prefix + "Your point balance has been resetted");
                    mainClass.getLogger().info(prefix + "You succesfully reset " + tmpPlayer.getName() + "'s point balance");
                }
            }
        }
        return false;
    }
}