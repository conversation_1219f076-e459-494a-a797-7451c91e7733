package it.masterzen.Dungeon;

public class Crystals {

    private long tier1;
    private long tier2;
    private long tier3;

    public Crystals(long tier1, long tier2, long tier3) {
        this.tier1 = tier1;
        this.tier2 = tier2;
        this.tier3 = tier3;
    }

    public long getTier1() {
        return tier1;
    }

    public long getTier2() {
        return tier2;
    }

    public long getTier3() {
        return tier3;
    }

    public void setTier1(long tier1) {
        this.tier1 = tier1;
    }

    public void setTier2(long tier2) {
        this.tier2 = tier2;
    }

    public void setTier3(long tier3) {
        this.tier3 = tier3;
    }

    public void addTier1(long amount) {
        this.tier1 = this.tier1 + amount;
    }

    public void addTier2(long amount) {
        this.tier2 = this.tier2 + amount;
    }

    public void addTier3(long amount) {
        this.tier3 = this.tier3 + amount;
    }

    public void addCrystals(int tier, long amount) {
        if (tier == 1) {
            this.tier1 = this.tier1 + amount;
        } else if (tier == 2) {
            this.tier2 = this.tier2 + amount;
        } else if (tier == 3) {
            this.tier3 = this.tier3 + amount;
        }
    }

    public void removeCrystals(int tier, long amount) {
        if (tier == 1) {
            this.tier1 = this.tier1 - amount;
        } else if (tier == 2) {
            this.tier2 = this.tier2 - amount;
        } else if (tier == 3) {
            this.tier3 = this.tier3 - amount;
        }
    }

    public void setCrystals(int tier, long amount) {
        if (tier == 1) {
            this.tier1 = amount;
        } else if (tier == 2) {
            this.tier2 = amount;
        } else if (tier == 3) {
            this.tier3 = amount;
        }
    }
}
