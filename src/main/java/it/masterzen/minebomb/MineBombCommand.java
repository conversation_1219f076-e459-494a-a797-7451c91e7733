package it.masterzen.minebomb;

import it.masterzen.blockbreak.AlphaBlockBreak;
import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class MineBombCommand implements CommandExecutor {

    private final AlphaBlockBreak plugin;

    public MineBombCommand(AlphaBlockBreak plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command cmd, String label, String[] args) {
        if (!cmd.getName().equalsIgnoreCase("minebomb")) return false;

        if ((sender instanceof Player)) {
            Player player = (Player) sender;
            if (!player.hasPermission("minebomb.admin") && !player.isOp()) return false;
        }

        if (args.length == 0) {
            sender.sendMessage("§6§lMINE BOMB §8»§7 /minebomb give <player> <money|tokens> <tier 1-3> <amount>");
            return true;
        }

        if (args.length == 5 && args[0].equalsIgnoreCase("give")) {
            Player target = Bukkit.getPlayerExact(args[1]);
            if (target == null) {
                sender.sendMessage("§6§lMINE BOMB §8»§7 §cPlayer not found");
                return true;
            }
            BombType type = BombType.fromString(args[2]);
            int tierInt;
            try { tierInt = Integer.parseInt(args[3]); } catch (Exception e) { tierInt = -1; }
            BombTier tier = BombTier.fromInt(tierInt);
            int amount;
            try { amount = Integer.parseInt(args[4]); } catch (Exception e) { amount = 1; }

            if (type == null || tier == null) {
                sender.sendMessage("§6§lMINE BOMB §8»§7 §cUsage: /minebomb give <player> <money|tokens> <tier 1-3> <amount>");
                return true;
            }

            target.getInventory().addItem(MineBombItem.create(type, tier, amount));
            sender.sendMessage("§6§lMINE BOMB §8»§7 §aGave " + amount + " §7T" + tier.getLevel() + " " + type.name().toLowerCase() + " bomb(s) to §f" + target.getName());
            return true;
        }

        sender.sendMessage("§6§lMINE BOMB §8»§7 §cUsage: /minebomb give <player> <money|tokens> <tier 1-3> <amount>");
        return true;
    }
}
