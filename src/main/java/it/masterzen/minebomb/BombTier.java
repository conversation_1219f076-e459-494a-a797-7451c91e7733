package it.masterzen.minebomb;

public enum BombTier {
    T1(1),
    T2(2),
    T3(3);

    private final int level;

    BombTier(int level) {
        this.level = level;
    }

    public int getLevel() {
        return level;
    }

    public static BombTier fromInt(int i) {
        switch (i) {
            case 1: return T1;
            case 2: return T2;
            case 3: return T3;
            default: return null;
        }
    }
}
