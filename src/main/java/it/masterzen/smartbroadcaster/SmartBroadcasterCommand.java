package it.masterzen.smartbroadcaster;

import it.masterzen.blockbreak.AlphaBlockBreak;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

/**
 * Command handler for Smart Broadcaster system.
 * Provides testing and management functionality.
 */
public class SmartBroadcaster<PERSON>ommand implements CommandExecutor {
    
    private final AlphaBlockBreak plugin;
    private final SmartBroadcasterManager manager;
    
    public SmartBroadcasterCommand(AlphaBlockBreak plugin, SmartBroadcasterManager manager) {
        this.plugin = plugin;
        this.manager = manager;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!command.getName().equalsIgnoreCase("smarttips")) {
            return false;
        }
        
        if (!(sender instanceof Player)) {
            sender.sendMessage("§cThis command can only be used by players.");
            return true;
        }
        
        Player player = (Player) sender;
        
        if (!player.hasPermission("smartbroadcaster.admin") && !player.isOp()) {
            player.sendMessage("§cYou don't have permission to use this command.");
            return true;
        }
        
        if (args.length == 0) {
            showHelp(player);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "test":
                testTips(player);
                break;
            case "reload":
                reloadConfig(player);
                break;
            case "status":
                showStatus(player);
                break;
            case "trigger":
                if (args.length > 1) {
                    triggerTip(player, args[1]);
                } else {
                    player.sendMessage("§cUsage: /smarttips trigger <tip-id>");
                }
                break;
            default:
                showHelp(player);
                break;
        }
        
        return true;
    }
    
    private void showHelp(Player player) {
        player.sendMessage("§e§lSMART BROADCASTER §8»§7 Commands:");
        player.sendMessage("§7/smarttips test - Test tip eligibility");
        player.sendMessage("§7/smarttips status - Show system status");
        player.sendMessage("§7/smarttips reload - Reload configuration");
        player.sendMessage("§7/smarttips trigger <tip-id> - Manually trigger a tip");
    }
    
    private void testTips(Player player) {
        player.sendMessage("§e§lSMART BROADCASTER §8»§7 Testing tip eligibility...");
        
        SmartBroadcaster broadcaster = manager.getBroadcaster();
        
        // Test each tip type
        for (FeatureTip tip : broadcaster.getFeatureTips()) {
            boolean eligible = tip.isEligible(player);
            String status = eligible ? "§aEligible" : "§cNot Eligible";
            player.sendMessage("§7  " + tip.getId() + ": " + status);
        }
        
        // Show additional info
        player.sendMessage("§7");
        player.sendMessage("§7Additional checks:");
        player.sendMessage("§7  Has Alpha+ permission: " + (player.hasPermission("essentials.warps.Alpha") ? "§aYes" : "§cNo"));
        player.sendMessage("§7  Has private mine permission: " + (player.hasPermission("prestigemine.own") ? "§aYes" : "§cNo"));
        
        // Check mine system
        try {
            boolean hasMine = plugin.getMineSystem() != null && 
                             plugin.getMineSystem().getMineList().containsKey(player.getUniqueId());
            player.sendMessage("§7  Has mine in system: " + (hasMine ? "§aYes" : "§cNo"));
        } catch (Exception e) {
            player.sendMessage("§7  Mine system check: §cError - " + e.getMessage());
        }
    }
    
    private void reloadConfig(Player player) {
        try {
            manager.reloadConfig();
            player.sendMessage("§e§lSMART BROADCASTER §8»§7 Configuration reloaded successfully!");
        } catch (Exception e) {
            player.sendMessage("§e§lSMART BROADCASTER §8»§7 §cError reloading configuration: " + e.getMessage());
        }
    }
    
    private void showStatus(Player player) {
        player.sendMessage("§e§lSMART BROADCASTER §8»§7 System Status:");
        player.sendMessage("§7  Enabled: " + (manager.isEnabled() ? "§aYes" : "§cNo"));
        player.sendMessage("§7  Broadcaster enabled: " + (manager.getBroadcaster().isEnabled() ? "§aYes" : "§cNo"));
        player.sendMessage("§7  Total tips: §e" + manager.getBroadcaster().getFeatureTips().size());
        
        // Show tip details
        player.sendMessage("§7");
        player.sendMessage("§7Registered tips:");
        for (FeatureTip tip : manager.getBroadcaster().getFeatureTips()) {
            player.sendMessage("§7  - " + tip.getId() + " (Priority: " + tip.getPriority() + 
                             ", Cooldown: " + tip.getCooldownMinutes() + "min)");
        }
    }
    
    private void triggerTip(Player player, String tipId) {
        SmartBroadcaster broadcaster = manager.getBroadcaster();
        
        // Find the tip
        FeatureTip targetTip = null;
        for (FeatureTip tip : broadcaster.getFeatureTips()) {
            if (tip.getId().equalsIgnoreCase(tipId)) {
                targetTip = tip;
                break;
            }
        }
        
        if (targetTip == null) {
            player.sendMessage("§e§lSMART BROADCASTER §8»§7 §cTip '" + tipId + "' not found.");
            player.sendMessage("§7Available tips: " + 
                String.join(", ", broadcaster.getFeatureTips().stream()
                    .map(FeatureTip::getId)
                    .toArray(String[]::new)));
            return;
        }
        
        // Check eligibility
        if (!targetTip.isEligible(player)) {
            player.sendMessage("§e§lSMART BROADCASTER §8»§7 §cYou are not eligible for tip '" + tipId + "'.");
            return;
        }
        
        // Send the tip
        targetTip.sendTo(player);
        player.sendMessage("§7§o(Manually triggered tip)");
    }
}
