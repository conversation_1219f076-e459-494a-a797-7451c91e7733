package it.masterzen.smartbroadcaster;

import org.bukkit.entity.Player;

import java.util.function.Predicate;

/**
 * Represents a feature tip that can be broadcasted to players.
 * Contains the tip content, conditions for eligibility, and metadata.
 */
public class FeatureTip {
    
    private final String id;
    private final String title;
    private final String message;
    private final Predicate<Player> eligibilityCondition;
    private final int priority;
    private final long cooldownMinutes;
    
    /**
     * Creates a new feature tip.
     * 
     * @param id Unique identifier for this tip
     * @param title Title/header for the tip
     * @param message Main message content
     * @param eligibilityCondition Function to check if player is eligible for this tip
     * @param priority Priority level (higher = more important)
     * @param cooldownMinutes Minutes to wait before showing this tip again to the same player
     */
    public FeatureTip(String id, String title, String message, 
                     Predicate<Player> eligibilityCondition, int priority, long cooldownMinutes) {
        this.id = id;
        this.title = title;
        this.message = message;
        this.eligibilityCondition = eligibilityCondition;
        this.priority = priority;
        this.cooldownMinutes = cooldownMinutes;
    }
    
    /**
     * Checks if a player is eligible to receive this tip.
     * 
     * @param player The player to check
     * @return true if the player should receive this tip
     */
    public boolean isEligible(Player player) {
        if (player == null || !player.isOnline()) {
            return false;
        }
        
        try {
            return eligibilityCondition.test(player);
        } catch (Exception e) {
            // Log error but don't crash - return false for safety
            return false;
        }
    }
    
    /**
     * Sends this tip to a player.
     * 
     * @param player The player to send the tip to
     */
    public void sendTo(Player player) {
        if (player == null || !player.isOnline()) {
            return;
        }
        
        // Send title if present
        if (title != null && !title.isEmpty()) {
            player.sendMessage(title);
        }
        
        // Send main message
        if (message != null && !message.isEmpty()) {
            player.sendMessage(message);
        }
    }
    
    // Getters
    public String getId() {
        return id;
    }
    
    public String getTitle() {
        return title;
    }
    
    public String getMessage() {
        return message;
    }
    
    public int getPriority() {
        return priority;
    }
    
    public long getCooldownMinutes() {
        return cooldownMinutes;
    }
    
    @Override
    public String toString() {
        return "FeatureTip{id='" + id + "', priority=" + priority + ", cooldown=" + cooldownMinutes + "min}";
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        FeatureTip that = (FeatureTip) obj;
        return id.equals(that.id);
    }
    
    @Override
    public int hashCode() {
        return id.hashCode();
    }
}
