package it.masterzen.smartbroadcaster;

import it.masterzen.blockbreak.AlphaBlockBreak;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;

/**
 * Core logic class for the Smart Broadcaster system.
 * Handles player eligibility checking, tip selection, and delivery.
 */
public class SmartBroadcaster {
    
    private final AlphaBlockBreak plugin;
    private final String prefix = "§e§lSMART TIP §8»§7 ";
    
    // Feature tips registry
    private final List<FeatureTip> featureTips;
    
    // Player state tracking
    private final Map<UUID, Map<String, Long>> playerTipCooldowns;
    private final Map<UUID, Long> playerLastTipTime;
    
    // Configuration
    private boolean enabled = true;
    private long globalCooldownMinutes = 10; // Minimum time between any tips for a player
    private int maxTipsPerSession = 3; // Maximum tips per player per session
    private final Map<UUID, Integer> playerTipCount;
    
    public SmartBroadcaster(AlphaBlockBreak plugin) {
        this.plugin = plugin;
        this.featureTips = new ArrayList<>();
        this.playerTipCooldowns = new ConcurrentHashMap<>();
        this.playerLastTipTime = new ConcurrentHashMap<>();
        this.playerTipCount = new ConcurrentHashMap<>();
        
        initializeFeatureTips();
    }
    
    /**
     * Initializes the default feature tips.
     */
    private void initializeFeatureTips() {
        // Tips will be added by the manager after this class is created
        // This method is kept for potential future default tips
    }
    
    /**
     * Adds a feature tip to the broadcaster.
     * 
     * @param tip The tip to add
     */
    public void addFeatureTip(FeatureTip tip) {
        if (tip != null && !featureTips.contains(tip)) {
            featureTips.add(tip);
            // Sort by priority (higher priority first)
            featureTips.sort((a, b) -> Integer.compare(b.getPriority(), a.getPriority()));
        }
    }
    
    /**
     * Processes all online players and sends appropriate tips.
     */
    public void processAllPlayers() {
        if (!enabled || featureTips.isEmpty()) {
            return;
        }
        
        for (Player player : Bukkit.getOnlinePlayers()) {
            try {
                processPlayer(player);
            } catch (Exception e) {
                plugin.getLogger().warning("Error processing smart tip for player " + player.getName() + ": " + e.getMessage());
            }
        }
    }
    
    /**
     * Processes a single player for tip delivery.
     * 
     * @param player The player to process
     */
    public void processPlayer(Player player) {
        if (player == null || !player.isOnline()) {
            return;
        }
        
        UUID playerId = player.getUniqueId();
        
        // Check global cooldown
        if (isPlayerOnGlobalCooldown(playerId)) {
            return;
        }
        
        // Check session tip limit
        if (hasPlayerReachedTipLimit(playerId)) {
            return;
        }
        
        // Find eligible tips
        List<FeatureTip> eligibleTips = getEligibleTips(player);
        if (eligibleTips.isEmpty()) {
            return;
        }
        
        // Select a tip to send
        FeatureTip selectedTip = selectTipForPlayer(player, eligibleTips);
        if (selectedTip != null) {
            sendTipToPlayer(player, selectedTip);
        }
    }
    
    /**
     * Gets all tips that a player is eligible for.
     * 
     * @param player The player to check
     * @return List of eligible tips
     */
    private List<FeatureTip> getEligibleTips(Player player) {
        List<FeatureTip> eligible = new ArrayList<>();
        UUID playerId = player.getUniqueId();
        
        for (FeatureTip tip : featureTips) {
            // Check if player is eligible for this tip
            if (!tip.isEligible(player)) {
                continue;
            }
            
            // Check tip-specific cooldown
            if (isPlayerOnTipCooldown(playerId, tip.getId())) {
                continue;
            }
            
            eligible.add(tip);
        }
        
        return eligible;
    }
    
    /**
     * Selects the best tip for a player from eligible tips.
     * 
     * @param player The player
     * @param eligibleTips List of eligible tips
     * @return Selected tip or null
     */
    private FeatureTip selectTipForPlayer(Player player, List<FeatureTip> eligibleTips) {
        if (eligibleTips.isEmpty()) {
            return null;
        }
        
        // For now, use weighted random selection based on priority
        // Higher priority tips have better chance of being selected
        int totalWeight = eligibleTips.stream().mapToInt(FeatureTip::getPriority).sum();
        if (totalWeight <= 0) {
            // If no priorities, select randomly
            return eligibleTips.get(ThreadLocalRandom.current().nextInt(eligibleTips.size()));
        }
        
        int randomWeight = ThreadLocalRandom.current().nextInt(totalWeight);
        int currentWeight = 0;
        
        for (FeatureTip tip : eligibleTips) {
            currentWeight += tip.getPriority();
            if (randomWeight < currentWeight) {
                return tip;
            }
        }
        
        // Fallback to first tip
        return eligibleTips.get(0);
    }
    
    /**
     * Sends a tip to a player and updates tracking.
     * 
     * @param player The player
     * @param tip The tip to send
     */
    private void sendTipToPlayer(Player player, FeatureTip tip) {
        UUID playerId = player.getUniqueId();
        long currentTime = System.currentTimeMillis();
        
        // Send the tip
        tip.sendTo(player);
        
        // Update tracking
        playerLastTipTime.put(playerId, currentTime);
        playerTipCount.merge(playerId, 1, Integer::sum);
        
        // Update tip-specific cooldown
        playerTipCooldowns.computeIfAbsent(playerId, k -> new ConcurrentHashMap<>())
                         .put(tip.getId(), currentTime);
        
        // Log for debugging
        plugin.getLogger().info("Sent smart tip '" + tip.getId() + "' to player " + player.getName());
    }
    
    /**
     * Checks if a player is on global cooldown.
     * 
     * @param playerId Player UUID
     * @return true if on cooldown
     */
    private boolean isPlayerOnGlobalCooldown(UUID playerId) {
        Long lastTipTime = playerLastTipTime.get(playerId);
        if (lastTipTime == null) {
            return false;
        }
        
        long timeSinceLastTip = System.currentTimeMillis() - lastTipTime;
        return timeSinceLastTip < (globalCooldownMinutes * 60 * 1000);
    }
    
    /**
     * Checks if a player is on cooldown for a specific tip.
     * 
     * @param playerId Player UUID
     * @param tipId Tip ID
     * @return true if on cooldown
     */
    private boolean isPlayerOnTipCooldown(UUID playerId, String tipId) {
        Map<String, Long> tipCooldowns = playerTipCooldowns.get(playerId);
        if (tipCooldowns == null) {
            return false;
        }
        
        Long lastTipTime = tipCooldowns.get(tipId);
        if (lastTipTime == null) {
            return false;
        }
        
        // Get the tip to check its specific cooldown
        FeatureTip tip = featureTips.stream()
                                   .filter(t -> t.getId().equals(tipId))
                                   .findFirst()
                                   .orElse(null);
        
        if (tip == null) {
            return false;
        }
        
        long timeSinceLastTip = System.currentTimeMillis() - lastTipTime;
        return timeSinceLastTip < (tip.getCooldownMinutes() * 60 * 1000);
    }
    
    /**
     * Checks if a player has reached the tip limit for this session.
     * 
     * @param playerId Player UUID
     * @return true if limit reached
     */
    private boolean hasPlayerReachedTipLimit(UUID playerId) {
        Integer tipCount = playerTipCount.get(playerId);
        return tipCount != null && tipCount >= maxTipsPerSession;
    }
    
    /**
     * Resets session data for a player (called on join/leave).
     * 
     * @param playerId Player UUID
     */
    public void resetPlayerSession(UUID playerId) {
        playerTipCount.remove(playerId);
        // Keep cooldowns across sessions
    }
    
    /**
     * Cleans up old cooldown data.
     */
    public void cleanupOldData() {
        long currentTime = System.currentTimeMillis();
        long maxCooldownMs = 24 * 60 * 60 * 1000; // 24 hours
        
        // Clean up global cooldowns
        playerLastTipTime.entrySet().removeIf(entry -> 
            currentTime - entry.getValue() > maxCooldownMs);
        
        // Clean up tip-specific cooldowns
        playerTipCooldowns.entrySet().removeIf(playerEntry -> {
            playerEntry.getValue().entrySet().removeIf(tipEntry ->
                currentTime - tipEntry.getValue() > maxCooldownMs);
            return playerEntry.getValue().isEmpty();
        });
    }
    
    // Configuration methods
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setGlobalCooldownMinutes(long minutes) {
        this.globalCooldownMinutes = Math.max(1, minutes);
    }
    
    public void setMaxTipsPerSession(int max) {
        this.maxTipsPerSession = Math.max(1, max);
    }
    
    public List<FeatureTip> getFeatureTips() {
        return new ArrayList<>(featureTips);
    }
    
    public String getPrefix() {
        return prefix;
    }
}
